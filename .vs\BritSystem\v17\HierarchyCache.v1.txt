﻿++解决方案 'BritSystem' ‎ (1 个项目，共 1 个)
i:{00000000-0000-0000-0000-000000000000}:BritSystem.sln
++BritSystem
i:{00000000-0000-0000-0000-000000000000}:BritSystem
++导入
i:{20f450ba-a30f-**********************}:>1426
++依赖项
i:{20f450ba-a30f-**********************}:>3017
++.vscode
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\.vscode\
++backup
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\backup\
++bin
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\bin\
++Brittleness
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\brittleness\
++.gitattributes
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\brittleness\.gitattributes
++Controls
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\controls\
++MineralDistributionControl.cs
++MineralDistributionControl.Designer.cs
++MineralStackedBarChartControl.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\controls\mineralstackedbarchartcontrol.cs
++Core
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\core\
++Models
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\core\models\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\models\
++AlgorithmFormulaCal.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\core\algorithmformulacal.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\algorithmformulacal.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\backup\algorithmformulacal.cs
++BrittlenessCalculator.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\core\brittlenesscalculator.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\brittlenesscalculator.cs
++ColumnDetector.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\core\columndetector.cs
++DataManager.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\core\datamanager.cs
++Docs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\docs\
++用户手册.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\docs\用户手册.txt
++Helpers
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\helpers\
++MineralVisualizationHelper.cs
++VisualizationHelper.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\helpers\visualizationhelper.cs
++Images
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\images\
++Ikun.ico
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\images\ikun.ico
++Mi.ico
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\images\mi.ico
++BrittlenessDataPoint.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\models\brittlenessdatapoint.cs
++CalculationResult.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\models\calculationresult.cs
++MineralData.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\models\mineraldata.cs
++NativeFormTest
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\
++obj
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\
++DashboardForm.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\dashboardform.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\dashboardform.cs
++LoginForm.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\loginform.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\loginform.cs
++MainForm.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\mainform.cs
++MineralogicalForm.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\mineralogicalform.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralogicalform.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\backup\mineralogicalform.cs
++NativeFormTest.csproj
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\nativeformtest.csproj
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest.csproj
++Program.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\program.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\program.cs
++Output
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\output\
++Properties
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\properties\
++runtimes
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\runtimes\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\
++SampleData
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\sampledata\
++Services
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\services\
++Setup
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\setup\
++WebView2FixedVersion
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\webview2fixedversion\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2fixedversion\
++AddSampleDataFromScreenshot.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\addsampledatafromscreenshot.cs
++AlgorithmFormulaCal.Designer.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\algorithmformulacal.designer.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\backup\algorithmformulacal.designer.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\core\algorithmformulacal.designer.cs
++AlgorithmFormulaCal11.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\algorithmformulacal11.cs
++app.manifest
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\app.manifest
++AppConfig.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\appconfig.cs
++BritIndexAnalysisForm.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\britindexanalysisform.cs
++BritSystem.csproj.Backup.tmp
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\britsystem.csproj.backup.tmp
++BritSystem.csproj.bak
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\britsystem.csproj.bak
++BritSystem.csproj.new
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\britsystem.csproj.new
++BritSystem.sln
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\britsystem.sln
++BritSystemApp.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\britsystemapp.cs
++BritSystemSetup.iss
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\britsystemsetup.iss
++BritSystemSetup_Fixed.iss
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\britsystemsetup_fixed.iss
++BritSystemWebView.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\britsystemwebview.cs
++BtnReset_Click.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\btnreset_click.cs
++BuildInstaller.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\buildinstaller.bat
++BuildInstaller_Fixed.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\buildinstaller_fixed.bat
++CheckWebView2.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\checkwebview2.cs
++ClayMineralColumnDetector.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\claymineralcolumndetector.cs
++ClayMineralDetectionTest.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\claymineraldetectiontest.cs
++ClayMineralLog.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\clayminerallog.txt
++CleanWebView2Data.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\cleanwebview2data.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\cleanwebview2data.bat
++ColumnDetectionHelper.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\columndetectionhelper.cs
++CompileSetup.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\compilesetup.bat
++core.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\core.txt
++CORE1.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\core1.txt
++CreateLogData.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\createlogdata.cs
++dashboard.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\dashboard.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\dashboard.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\dashboard.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\dashboard.css
++dashboard.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\dashboard.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\dashboard.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\dashboard.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\dashboard.html
++DebugBuild.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\debugbuild.bat
++DetectColumnsPositionNew.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\detectcolumnspositionnew.cs
++DirectBuild.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\directbuild.bat
++DownloadWebView2Runtime.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\downloadwebview2runtime.bat
++errors.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\errors.txt
++ExcelFormulaParser.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\excelformulaparser.cs
++FindBestColumnMatch.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\findbestcolumnmatch.cs
++FixedMouseClickMethod.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\fixedmouseclickmethod.cs
++Form1.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\form1.cs
++ImprovedClayMineralDetection.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\improvedclaymineraldetection.cs
++index.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\index.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\index.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\index.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\index.html
++InstallWebView2.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\installwebview2.bat
++IsTOC_method.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\istoc_method.txt
++IsTOCFix.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\istocfix.cs
++LICENSE.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\license.txt
++LoadExcelData_part1.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\loadexceldata_part1.cs
++LoadExcelData_part2.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\loadexceldata_part2.cs
++LoadExcelData_part3.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\loadexceldata_part3.cs
++Log.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\log.txt
++LogDataManager.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\logdatamanager.cs
++ManualDetectForm.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\manualdetectform.cs
++ManualDetectForm.resx.bak
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\manualdetectform.resx.bak
++ManualMappingForm.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\manualmappingform.cs
++MineralInputForm.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralinputform.cs
++mineralogical.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralogical.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\mineralogical.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\mineralogical.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\mineralogical.css
++mineralogical.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralogical.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\mineralogical.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\mineralogical.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\mineralogical.html
++MineralogicalAnalysisForm.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralogicalanalysisform.cs
++MineralogicalForm.resx.bak
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralogicalform.resx.bak
++MineralogicalForm.resx.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralogicalform.resx.old
++MineralogicalFormVisualization.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralogicalformvisualization.resx
++msbuild.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\msbuild.log
++NativeFormTest.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest.cs
++NewMouseClickMethod.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\newmouseclickmethod.cs
++NOTICE.TXT
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\dotnetcore.npoi\1.2.3\contentfiles\any\netstandard2.0\notice.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\notice.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\notice.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\notice.txt
++PublishAndPackage.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\publishandpackage.bat
++README.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\readme.txt
++RunSimpleWebViewContainer.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\runsimplewebviewcontainer.bat
++RunTest.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\runtest.bat
++RunWebView2Test.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\runwebview2test.bat
++ShowSourceData.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\showsourcedata.cs
++SimpleBuild.bat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\simplebuild.bat
++SimpleSetup.iss
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\simplesetup.iss
++SimpleTest.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\simpletest.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\simpletest.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\simpletest.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\simpletest.html
++SimpleWebView2Test.csproj
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\simplewebview2test.csproj
++SimpleWebView2Test.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\simplewebview2test.html
++style.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\style.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\style.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\style.css
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\style.css
++temp_script.ps1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\temp_script.ps1
++TestForm.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\testform.cs
++UserManual.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\usermanual.txt
++VerySimpleWebView2Test.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\verysimplewebview2test.cs
++VerySimpleWebView2Test.csproj
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\verysimplewebview2test.csproj
++VisualizationForm.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\visualizationform.cs
++WebView2Tester.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\webview2tester.cs
++WebView2Tester.csproj
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\webview2tester.csproj
++WebViewContainer.cs.bak
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\webviewcontainer.cs.bak
++Sdk.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\sdk\sdk.props
++Sdk.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\sdk\sdk.targets
++包
i:{20f450ba-a30f-**********************}:>3035
++分析器
i:{20f450ba-a30f-**********************}:>3018
++框架
i:{20f450ba-a30f-**********************}:>3032
++Debug
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\bin\debug\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\
++Release
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\
++AlgorithmFormulaCal.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\core\algorithmformulacal.resx
++NativeFormTest.csproj.nuget.dgspec.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\nativeformtest.csproj.nuget.dgspec.json
++NativeFormTest.csproj.nuget.g.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\nativeformtest.csproj.nuget.g.props
++NativeFormTest.csproj.nuget.g.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\nativeformtest.csproj.nuget.g.targets
++project.assets.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\project.assets.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\project.assets.json
++project.nuget.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\project.nuget.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\project.nuget.cache
++BritSystem.csproj.nuget.dgspec.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\britsystem.csproj.nuget.dgspec.json
++BritSystem.csproj.nuget.g.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\britsystem.csproj.nuget.g.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\britsystem.csproj.nuget.g.props
++BritSystem.csproj.nuget.g.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\britsystem.csproj.nuget.g.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\britsystem.csproj.nuget.g.targets
++BritSystemSetup.exe
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\output\britsystemsetup.exe
++PublishProfiles
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\properties\publishprofiles\
++win-arm64
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\runtimes\win-arm64\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-arm64\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-arm64\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-arm64\
++win-x64
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\runtimes\win-x64\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-x64\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-x64\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-x64\
++win-x86
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\runtimes\win-x86\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-x86\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-x86\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-x86\
++示例数据说明.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\sampledata\示例数据说明.txt
++BrittlenessCalculationService.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\services\brittlenesscalculationservice.cs
++ExportService.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\services\exportservice.cs
++ImportService.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\services\importservice.cs
++LoggingService.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\services\loggingservice.cs
++BritIndexAnalysisForm.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\britindexanalysisform.resx
++DashboardForm.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\dashboardform.resx
++DetectColumnsPositionNew.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\detectcolumnspositionnew.resx
++LoginForm.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\loginform.resx
++ManualDetectForm.Designer.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\manualdetectform.designer.cs
++ManualDetectForm.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\manualdetectform.resx
++ManualMappingForm.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\manualmappingform.resx
++MineralInputForm.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralinputform.resx
++MineralogicalAnalysisForm.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralogicalanalysisform.resx
++MineralogicalForm.Designer.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralogicalform.designer.cs
++MineralogicalForm.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\mineralogicalform.resx
++VisualizationForm.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\visualizationform.resx
++Microsoft.Common.props
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\microsoft.common.props
++Microsoft.NET.Sdk.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.props
++Microsoft.NET.Sdk.BeforeCommon.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.beforecommon.targets
++Microsoft.CSharp.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\amd64\microsoft.csharp.targets
++Microsoft.NET.Sdk.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.targets
++Microsoft.NET.ApiCompat.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.apicompat.targets
++NuGet.Build.Tasks.Pack.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\nuget.build.tasks.pack\build\nuget.build.tasks.pack.targets
++Microsoft.NET.Build.Containers.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\containers\build\microsoft.net.build.containers.props
++Microsoft.NET.Build.Containers.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\containers\build\microsoft.net.build.containers.targets
++AntDesign (1.3.2)
i:{20f450ba-a30f-**********************}:>3037
++AntDesign.Charts (0.6.1)
i:{20f450ba-a30f-**********************}:>3041
++AntDesign.ProLayout (1.3.1)
i:{20f450ba-a30f-**********************}:>3043
++DotNetCore.NPOI (1.2.3)
i:{20f450ba-a30f-**********************}:>3042
++EPPlus (8.0.1)
i:{20f450ba-a30f-**********************}:>3039
++HIC.System.Windows.Forms.DataVisualization (1.0.1)
i:{20f450ba-a30f-**********************}:>3036
++Microsoft.Office.Interop.Excel (15.0.4795.1001)
i:{20f450ba-a30f-**********************}:>3038
++Microsoft.Web.WebView2 (1.0.1774.30)
i:{20f450ba-a30f-**********************}:>3040
++Microsoft.Web.WebView2.DevToolsProtocolExtension (1.0.824)
i:{20f450ba-a30f-**********************}:>3045
++System.Data.OleDb (9.0.4)
i:{20f450ba-a30f-**********************}:>3044
++Microsoft.AspNetCore.Components.Analyzers
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.aspnetcore.components.analyzers\8.0.0\analyzers\dotnet\cs\microsoft.aspnetcore.components.analyzers.dll
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.Extensions.Logging.Generators
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.logging.generators.dll
++Microsoft.Extensions.Options.SourceGeneration
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.0\analyzers\dotnet\roslyn4.4\cs\microsoft.extensions.options.sourcegeneration.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Text.Json.SourceGeneration
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.15\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++System.Windows.Forms.Analyzers
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\8.0.15\analyzers\dotnet\system.windows.forms.analyzers.dll
++System.Windows.Forms.Analyzers.CSharp
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\packs\microsoft.windowsdesktop.app.ref\8.0.15\analyzers\dotnet\cs\system.windows.forms.analyzers.csharp.dll
++Microsoft.NETCore.App
i:{20f450ba-a30f-**********************}:>3033
++Microsoft.WindowsDesktop.App.WindowsForms
i:{20f450ba-a30f-**********************}:>3034
++net8.0-windows
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\
++net6.0-windows
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\bin\debug\net6.0-windows\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\
++native
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\runtimes\win-arm64\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\runtimes\win-x64\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\runtimes\win-x86\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-arm\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-arm64\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-x64\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-x86\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-arm\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-arm64\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-x64\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-x86\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-arm\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-arm64\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-x64\native\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-x86\native\
++UseArtifactsOutputPath.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\sdk\useartifactsoutputpath.props
++Microsoft.NuGet.ImportBefore.props
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\imports\microsoft.common.props\importbefore\microsoft.nuget.importbefore.props
++NuGet.props
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\common7\ide\commonextensions\microsoft\nuget\nuget.props
++Microsoft.NET.Sdk.DefaultItems.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.defaultitems.props
++Microsoft.NET.Sdk.ImportWorkloads.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.importworkloads.props
++Microsoft.NET.SupportedTargetFrameworks.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.supportedtargetframeworks.props
++Microsoft.NET.SupportedPlatforms.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.supportedplatforms.props
++Microsoft.NET.WindowsSdkSupportedTargetPlatforms.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.windowssdksupportedtargetplatforms.props
++Microsoft.NET.Sdk.SourceLink.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.sourcelink.props
++Microsoft.NET.Sdk.CSharp.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.csharp.props
++Microsoft.NET.PackTool.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.packtool.props
++Microsoft.NET.PackProjectTool.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.packprojecttool.props
++Microsoft.NET.Sdk.WindowsDesktop.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk.windowsdesktop\targets\microsoft.net.sdk.windowsdesktop.props
++Microsoft.NET.Windows.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.windows.props
++Microsoft.NET.DefaultAssemblyInfo.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.defaultassemblyinfo.targets
++Microsoft.NET.Sdk.ImportPublishProfile.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.importpublishprofile.targets
++Microsoft.NET.TargetFrameworkInference.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.targetframeworkinference.targets
++Microsoft.NET.DefaultOutputPaths.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.defaultoutputpaths.targets
++Microsoft.NET.Sdk.ImportWorkloads.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.importworkloads.targets
++Microsoft.NET.RuntimeIdentifierInference.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.runtimeidentifierinference.targets
++Microsoft.NET.EolTargetFrameworks.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.eoltargetframeworks.targets
++Microsoft.NET.NuGetOfflineCache.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.nugetofflinecache.targets
++Microsoft.Managed.Before.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\amd64\microsoft.managed.before.targets
++Microsoft.CSharp.CurrentVersion.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\amd64\microsoft.csharp.currentversion.targets
++Microsoft.Managed.After.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\amd64\microsoft.managed.after.targets
++Microsoft.NET.Sdk.Common.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.common.targets
++Microsoft.PackageDependencyResolution.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.packagedependencyresolution.targets
++Microsoft.NET.Sdk.DefaultItems.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.defaultitems.targets
++Microsoft.NET.Sdk.FrameworkReferenceResolution.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.frameworkreferenceresolution.targets
++Microsoft.NET.Sdk.Shared.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.shared.targets
++Microsoft.NET.Sdk.SourceLink.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.sourcelink.targets
++Microsoft.NET.DisableStandardFrameworkResolution.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.disablestandardframeworkresolution.targets
++Microsoft.NET.DesignerSupport.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.designersupport.targets
++Microsoft.NET.GenerateAssemblyInfo.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.generateassemblyinfo.targets
++Microsoft.NET.GenerateGlobalUsings.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.generateglobalusings.targets
++Microsoft.NET.GenerateSupportedRuntime.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.generatesupportedruntime.targets
++Microsoft.NET.ComposeStore.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.composestore.targets
++Microsoft.NET.CrossGen.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.crossgen.targets
++Microsoft.NET.ObsoleteReferences.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.obsoletereferences.targets
++Microsoft.NET.Publish.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.publish.targets
++Microsoft.NET.PackTool.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.packtool.targets
++Microsoft.NET.PackProjectTool.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.packprojecttool.targets
++Microsoft.NET.PreserveCompilationContext.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.preservecompilationcontext.targets
++Microsoft.NET.ConflictResolution.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.conflictresolution.targets
++Microsoft.NET.Sdk.CSharp.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.csharp.targets
++Microsoft.NET.Sdk.Analyzers.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.analyzers.targets
++Microsoft.NET.Windows.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.windows.targets
++Microsoft.NET.Sdk.WindowsDesktop.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk.windowsdesktop\targets\microsoft.net.sdk.windowsdesktop.targets
++Microsoft.NET.ApiCompat.Common.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.apicompat.common.targets
++Microsoft.NET.ApiCompat.ValidatePackage.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.apicompat.validatepackage.targets
++BritSystem.exe.WebView2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\
++Logs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\
++AntDesign.Charts.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\antdesign.charts.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\antdesign.charts.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\antdesign.charts.dll
++AntDesign.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\antdesign.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\antdesign.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\antdesign.dll
++AntDesign.ProLayout.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\antdesign.prolayout.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\antdesign.prolayout.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\antdesign.prolayout.dll
++Azure.Core.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\azure.core.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\azure.core.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\azure.core.dll
++Azure.Identity.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\azure.identity.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\azure.identity.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\azure.identity.dll
++BritSystem.deps.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.deps.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\britsystem.deps.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.deps.json
++BritSystem.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\britsystem.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\ref\britsystem.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\refint\britsystem.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\ref\britsystem.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\refint\britsystem.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.dll
++BritSystem.exe
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\britsystem.exe
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe
++BritSystem.pdb
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.pdb
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.pdb
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\britsystem.pdb
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.pdb
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.pdb
++BritSystem.runtimeconfig.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.runtimeconfig.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\britsystem.runtimeconfig.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.runtimeconfig.json
++debug_log.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\debug_log.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\debug_log.txt
++EPPlus.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\epplus.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\epplus.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\epplus.dll
++EPPlus.Interfaces.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\epplus.interfaces.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\epplus.interfaces.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\epplus.interfaces.dll
++ICSharpCode.SharpZipLib.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\icsharpcode.sharpziplib.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\icsharpcode.sharpziplib.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\icsharpcode.sharpziplib.dll
++Microsoft.AspNetCore.Authorization.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.aspnetcore.authorization.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.aspnetcore.authorization.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.aspnetcore.authorization.dll
++Microsoft.AspNetCore.Components.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.aspnetcore.components.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.aspnetcore.components.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.aspnetcore.components.dll
++Microsoft.AspNetCore.Components.Forms.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.aspnetcore.components.forms.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.aspnetcore.components.forms.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.aspnetcore.components.forms.dll
++Microsoft.AspNetCore.Components.Web.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.aspnetcore.components.web.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.aspnetcore.components.web.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.aspnetcore.components.web.dll
++Microsoft.AspNetCore.Metadata.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.aspnetcore.metadata.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.aspnetcore.metadata.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.aspnetcore.metadata.dll
++Microsoft.Bcl.AsyncInterfaces.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.bcl.asyncinterfaces.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.bcl.asyncinterfaces.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.bcl.asyncinterfaces.dll
++Microsoft.Data.SqlClient.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.data.sqlclient.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.data.sqlclient.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\unix\lib\netcoreapp3.1\microsoft.data.sqlclient.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\lib\netcoreapp3.1\microsoft.data.sqlclient.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\unix\lib\netcoreapp3.1\microsoft.data.sqlclient.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\lib\netcoreapp3.1\microsoft.data.sqlclient.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.data.sqlclient.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\lib\netcoreapp3.1\microsoft.data.sqlclient.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\unix\lib\netcoreapp3.1\microsoft.data.sqlclient.dll
++Microsoft.Extensions.Configuration.Abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.configuration.abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.configuration.abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.configuration.abstractions.dll
++Microsoft.Extensions.Configuration.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.configuration.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.configuration.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.configuration.dll
++Microsoft.Extensions.Configuration.FileExtensions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.configuration.fileextensions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.configuration.fileextensions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.configuration.fileextensions.dll
++Microsoft.Extensions.Configuration.Json.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.configuration.json.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.configuration.json.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.configuration.json.dll
++Microsoft.Extensions.DependencyInjection.Abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.dependencyinjection.abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.dependencyinjection.abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.dependencyinjection.abstractions.dll
++Microsoft.Extensions.DependencyInjection.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.dependencyinjection.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.dependencyinjection.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.dependencyinjection.dll
++Microsoft.Extensions.DependencyModel.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.dependencymodel.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.dependencymodel.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.dependencymodel.dll
++Microsoft.Extensions.FileProviders.Abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.fileproviders.abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.fileproviders.abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.fileproviders.abstractions.dll
++Microsoft.Extensions.FileProviders.Physical.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.fileproviders.physical.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.fileproviders.physical.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.fileproviders.physical.dll
++Microsoft.Extensions.FileSystemGlobbing.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.filesystemglobbing.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.filesystemglobbing.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.filesystemglobbing.dll
++Microsoft.Extensions.Logging.Abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.logging.abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.logging.abstractions.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.logging.abstractions.dll
++Microsoft.Extensions.Options.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.options.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.options.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.options.dll
++Microsoft.Extensions.Primitives.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.extensions.primitives.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.extensions.primitives.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.extensions.primitives.dll
++Microsoft.Identity.Client.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.identity.client.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.identity.client.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.identity.client.dll
++Microsoft.Identity.Client.Extensions.Msal.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.identity.client.extensions.msal.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.identity.client.extensions.msal.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.identity.client.extensions.msal.dll
++Microsoft.IdentityModel.JsonWebTokens.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.identitymodel.jsonwebtokens.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.identitymodel.jsonwebtokens.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.identitymodel.jsonwebtokens.dll
++Microsoft.IdentityModel.Logging.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.identitymodel.logging.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.identitymodel.logging.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.identitymodel.logging.dll
++Microsoft.IdentityModel.Protocols.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.identitymodel.protocols.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.identitymodel.protocols.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.identitymodel.protocols.dll
++Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.identitymodel.protocols.openidconnect.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.identitymodel.protocols.openidconnect.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.identitymodel.protocols.openidconnect.dll
++Microsoft.IdentityModel.Tokens.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.identitymodel.tokens.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.identitymodel.tokens.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.identitymodel.tokens.dll
++Microsoft.IO.RecyclableMemoryStream.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.io.recyclablememorystream.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.io.recyclablememorystream.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.io.recyclablememorystream.dll
++Microsoft.JSInterop.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.jsinterop.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.jsinterop.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.jsinterop.dll
++Microsoft.Office.Interop.Excel.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.office.interop.excel.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.office.interop.excel.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.office.interop.excel.dll
++Microsoft.Web.WebView2.Core.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.web.webview2.core.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.web.webview2.core.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.web.webview2.core.dll
++Microsoft.Web.WebView2.DevToolsProtocolExtension.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.web.webview2.devtoolsprotocolextension.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.web.webview2.devtoolsprotocolextension.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.web.webview2.devtoolsprotocolextension.dll
++Microsoft.Web.WebView2.WinForms.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.web.webview2.winforms.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.web.webview2.winforms.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.web.webview2.winforms.dll
++Microsoft.Web.WebView2.Wpf.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\microsoft.web.webview2.wpf.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\microsoft.web.webview2.wpf.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\microsoft.web.webview2.wpf.dll
++NPOI.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\npoi.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\npoi.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\npoi.dll
++NPOI.OOXML.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\npoi.ooxml.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\npoi.ooxml.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\npoi.ooxml.dll
++NPOI.OpenXml4Net.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\npoi.openxml4net.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\npoi.openxml4net.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\npoi.openxml4net.dll
++NPOI.OpenXmlFormats.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\npoi.openxmlformats.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\npoi.openxmlformats.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\npoi.openxmlformats.dll
++OneOf.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\oneof.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\oneof.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\oneof.dll
++System.Configuration.ConfigurationManager.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\system.configuration.configurationmanager.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\system.configuration.configurationmanager.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\system.configuration.configurationmanager.dll
++System.Data.OleDb.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\system.data.oledb.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\system.data.oledb.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\lib\net8.0\system.data.oledb.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\lib\net8.0\system.data.oledb.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\lib\net8.0\system.data.oledb.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\system.data.oledb.dll
++System.Diagnostics.EventLog.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\system.diagnostics.eventlog.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\system.diagnostics.eventlog.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\lib\net8.0\system.diagnostics.eventlog.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\lib\net8.0\system.diagnostics.eventlog.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\lib\net8.0\system.diagnostics.eventlog.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\system.diagnostics.eventlog.dll
++System.Diagnostics.PerformanceCounter.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\system.diagnostics.performancecounter.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\system.diagnostics.performancecounter.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\lib\net8.0\system.diagnostics.performancecounter.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\lib\net8.0\system.diagnostics.performancecounter.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\lib\net8.0\system.diagnostics.performancecounter.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\system.diagnostics.performancecounter.dll
++System.IdentityModel.Tokens.Jwt.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\system.identitymodel.tokens.jwt.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\system.identitymodel.tokens.jwt.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\system.identitymodel.tokens.jwt.dll
++System.IO.Pipelines.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\system.io.pipelines.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\system.io.pipelines.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\system.io.pipelines.dll
++System.Runtime.Caching.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\system.runtime.caching.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\system.runtime.caching.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\lib\netstandard2.0\system.runtime.caching.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\lib\netstandard2.0\system.runtime.caching.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\lib\netstandard2.0\system.runtime.caching.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\system.runtime.caching.dll
++System.Security.Cryptography.Pkcs.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\system.security.cryptography.pkcs.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\system.security.cryptography.pkcs.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\lib\net8.0\system.security.cryptography.pkcs.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\lib\net8.0\system.security.cryptography.pkcs.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\lib\net8.0\system.security.cryptography.pkcs.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\system.security.cryptography.pkcs.dll
++System.Security.Cryptography.ProtectedData.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\system.security.cryptography.protecteddata.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\system.security.cryptography.protecteddata.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\system.security.cryptography.protecteddata.dll
++System.Security.Cryptography.Xml.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\system.security.cryptography.xml.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\system.security.cryptography.xml.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\system.security.cryptography.xml.dll
++System.Windows.Forms.DataVisualization.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\system.windows.forms.datavisualization.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\system.windows.forms.datavisualization.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\system.windows.forms.datavisualization.dll
++webview_log.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\webview_log.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview_log.txt
++publish
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\
++SimpleTest
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\simpletest\
++WebView2Data
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\
++test.html
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\test.html
++NativeFormTest.deps.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\bin\debug\net6.0-windows\nativeformtest.deps.json
++NativeFormTest.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\bin\debug\net6.0-windows\nativeformtest.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\ref\nativeformtest.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\refint\nativeformtest.dll
++NativeFormTest.exe
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\bin\debug\net6.0-windows\nativeformtest.exe
++NativeFormTest.pdb
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\bin\debug\net6.0-windows\nativeformtest.pdb
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.pdb
++NativeFormTest.runtimeconfig.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\bin\debug\net6.0-windows\nativeformtest.runtimeconfig.json
++ref
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\ref\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\ref\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\ref\
++refint
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\refint\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\refint\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\refint\
++.NETCoreApp,Version=v6.0.AssemblyAttributes.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\.netcoreapp,version=v6.0.assemblyattributes.cs
++apphost.exe
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\apphost.exe
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\apphost.exe
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\apphost.exe
++NativeFormTest.AssemblyInfo.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.assemblyinfo.cs
++NativeFormTest.AssemblyInfoInputs.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.assemblyinfoinputs.cache
++NativeFormTest.assets.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.assets.cache
++NativeFormTest.csproj.CoreCompileInputs.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.csproj.corecompileinputs.cache
++NativeFormTest.csproj.FileListAbsolute.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.csproj.filelistabsolute.txt
++NativeFormTest.GeneratedMSBuildEditorConfig.editorconfig
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.generatedmsbuildeditorconfig.editorconfig
++NativeFormTest.genruntimeconfig.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.genruntimeconfig.cache
++NativeFormTest.GlobalUsings.g.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\nativeformtest\obj\debug\net6.0-windows\nativeformtest.globalusings.g.cs
++.NETCoreApp,Version=v8.0.AssemblyAttributes.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\.netcoreapp,version=v8.0.assemblyattributes.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\.netcoreapp,version=v8.0.assemblyattributes.cs
++BritSystem.AssemblyInfo.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.assemblyinfo.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.assemblyinfo.cs
++BritSystem.AssemblyInfoInputs.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.assemblyinfoinputs.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.assemblyinfoinputs.cache
++BritSystem.assets.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.assets.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.assets.cache
++BritSystem.BritIndexAnalysisForm.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.britindexanalysisform.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.britindexanalysisform.resources
++BritSystem.Core.AlgorithmFormulaCal.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.core.algorithmformulacal.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.core.algorithmformulacal.resources
++BritSystem.csproj.AssemblyReference.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.csproj.assemblyreference.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.csproj.assemblyreference.cache
++BritSystem.csproj.BuildWithSkipAnalyzers
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.csproj.buildwithskipanalyzers
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.csproj.buildwithskipanalyzers
++BritSystem.csproj.CoreCompileInputs.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.csproj.corecompileinputs.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.csproj.corecompileinputs.cache
++BritSystem.csproj.FileListAbsolute.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.csproj.filelistabsolute.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.csproj.filelistabsolute.txt
++BritSystem.csproj.GenerateResource.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.csproj.generateresource.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.csproj.generateresource.cache
++BritSystem.csproj.Up2Date
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.csproj.up2date
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.csproj.up2date
++BritSystem.DashboardForm.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.dashboardform.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.dashboardform.resources
++BritSystem.designer.deps.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.designer.deps.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.designer.deps.json
++BritSystem.designer.runtimeconfig.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.designer.runtimeconfig.json
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.designer.runtimeconfig.json
++BritSystem.GeneratedMSBuildEditorConfig.editorconfig
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.generatedmsbuildeditorconfig.editorconfig
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.generatedmsbuildeditorconfig.editorconfig
++BritSystem.genruntimeconfig.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.genruntimeconfig.cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.genruntimeconfig.cache
++BritSystem.GlobalUsings.g.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.globalusings.g.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.globalusings.g.cs
++BritSystem.LoginForm.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.loginform.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.loginform.resources
++BritSystem.ManualMappingForm.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.manualmappingform.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.manualmappingform.resources
++BritSystem.MineralInputForm.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.mineralinputform.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.mineralinputform.resources
++BritSystem.MineralogicalAnalysisForm.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.mineralogicalanalysisform.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.mineralogicalanalysisform.resources
++BritSystem.MineralogicalForm.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.mineralogicalform.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.mineralogicalform.resources
++BritSystem.MineralogicalFormVisualization.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.mineralogicalformvisualization.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.mineralogicalformvisualization.resources
++BritSystem.Resources.DetectColumnsPositionNew.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.resources.detectcolumnspositionnew.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.resources.detectcolumnspositionnew.resources
++BritSystem.UI.ManualDetectForm.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.ui.manualdetectform.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.ui.manualdetectform.resources
++BritSystem.VisualizationForm.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\debug\net8.0-windows\britsystem.visualizationform.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.visualizationform.resources
++PublishOutputs.ba51041926.txt
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\publishoutputs.ba51041926.txt
++WebView2Loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.1774.30\runtimes\win-arm64\native\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.1774.30\runtimes\win-x64\native\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.1774.30\runtimes\win-x86\native\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2fixedversion\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-arm64\native\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-x64\native\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-x86\native\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-arm64\native\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-x64\native\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-x86\native\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-x86\native\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-x64\native\webview2loader.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-arm64\native\webview2loader.dll
++AntDesign.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign\1.3.2\buildtransitive\antdesign.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign\1.3.2\buildmultitargeting\antdesign.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign\1.3.2\build\antdesign.props
++AntDesign.ProLayout.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign.prolayout\1.3.1\buildtransitive\antdesign.prolayout.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign.prolayout\1.3.1\buildmultitargeting\antdesign.prolayout.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign.prolayout\1.3.1\build\antdesign.prolayout.props
++AntDesign.Charts.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign.charts\0.6.1\buildtransitive\antdesign.charts.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign.charts\0.6.1\buildmultitargeting\antdesign.charts.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign.charts\0.6.1\build\antdesign.charts.props
++Microsoft.NuGet.props
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\microsoft\nuget\17.0\microsoft.nuget.props
++Microsoft.NETCoreSdk.BundledVersions.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\microsoft.netcoresdk.bundledversions.props
++Microsoft.NETCoreSdk.BundledMSBuildInformation.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\microsoft.netcoresdk.bundledmsbuildinformation.props
++Microsoft.Build.Tasks.Git.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.build.tasks.git\build\microsoft.build.tasks.git.props
++Microsoft.SourceLink.Common.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.sourcelink.common\build\microsoft.sourcelink.common.props
++Microsoft.SourceLink.GitHub.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.sourcelink.github\build\microsoft.sourcelink.github.props
++Microsoft.SourceLink.GitLab.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.sourcelink.gitlab\build\microsoft.sourcelink.gitlab.props
++Microsoft.SourceLink.AzureRepos.Git.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.sourcelink.azurerepos.git\build\microsoft.sourcelink.azurerepos.git.props
++Microsoft.SourceLink.Bitbucket.Git.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.sourcelink.bitbucket.git\build\microsoft.sourcelink.bitbucket.git.props
++Microsoft.NET.Sdk.WindowsDesktop.WindowsForms.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk.windowsdesktop\targets\microsoft.net.sdk.windowsdesktop.windowsforms.props
++Microsoft.NET.Sdk.WindowsDesktop.WPF.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk.windowsdesktop\targets\microsoft.net.sdk.windowsdesktop.wpf.props
++Microsoft.NET.DefaultArtifactsPath.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.defaultartifactspath.props
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.android\35.0.39)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.android\35.0.39\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.ios\18.2.9180)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.ios\18.2.9180\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.maccatalyst\18.2.9180)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.maccatalyst\18.2.9180\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.macos\15.2.9180)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.macos\15.2.9180\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.maui\9.0.14)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.maui\9.0.14\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.tvos\18.2.9180)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.sdk.tvos\18.2.9180\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.current\9.0.4)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.current\9.0.4\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.current\9.0.4)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.current\9.0.4\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.net6\9.0.4)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.net6\9.0.4\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.net7\9.0.4)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.net7\9.0.4\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.net8\9.0.4)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.emscripten.net8\9.0.4\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.net6\9.0.4)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.net6\9.0.4\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.net7\9.0.4)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.net7\9.0.4\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.net8\9.0.4)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\9.0.100\microsoft.net.workload.mono.toolchain.net8\9.0.4\workloadmanifest.targets
++WorkloadManifest.targets (C:\Program Files\dotnet\sdk-manifests\8.0.100\microsoft.net.sdk.aspire\8.2.2)
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk-manifests\8.0.100\microsoft.net.sdk.aspire\8.2.2\workloadmanifest.targets
++Microsoft.CSharp.Core.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\roslyn\microsoft.csharp.core.targets
++Microsoft.CSharp.DesignTime.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\microsoft\visualstudio\managed\microsoft.csharp.designtime.targets
++Microsoft.Common.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\amd64\microsoft.common.targets
++Microsoft.ServiceModel.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\amd64\microsoft.servicemodel.targets
i:{20f450ba-a30f-**********************}:c:\windows\microsoft.net\framework\v4.0.30319\microsoft.servicemodel.targets
++Microsoft.DesignTime.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\common7\ide\commonextensions\microsoft\projectservices\microsoft.designtime.targets
++Microsoft.NET.Sdk.DefaultItems.Shared.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.sdk.defaultitems.shared.targets
++Microsoft.Build.Tasks.Git.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.build.tasks.git\build\microsoft.build.tasks.git.targets
++Microsoft.SourceLink.Common.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.sourcelink.common\build\microsoft.sourcelink.common.targets
++Microsoft.SourceLink.GitHub.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.sourcelink.github\build\microsoft.sourcelink.github.targets
++Microsoft.SourceLink.GitLab.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.sourcelink.gitlab\build\microsoft.sourcelink.gitlab.targets
++Microsoft.SourceLink.AzureRepos.Git.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.sourcelink.azurerepos.git\build\microsoft.sourcelink.azurerepos.git.targets
++Microsoft.SourceLink.Bitbucket.Git.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.sourcelink.bitbucket.git\build\microsoft.sourcelink.bitbucket.git.targets
++Microsoft.NETCoreSdk.BundledCliTools.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\microsoft.netcoresdk.bundledclitools.props
++Microsoft.NET.DefaultPackageConflictOverrides.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\targets\microsoft.net.defaultpackageconflictoverrides.targets
++Microsoft.CodeAnalysis.NetAnalyzers.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\build\microsoft.codeanalysis.netanalyzers.props
++Microsoft.CodeAnalysis.NetAnalyzers.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\build\microsoft.codeanalysis.netanalyzers.targets
++Microsoft.CodeAnalysis.CSharp.CodeStyle.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\codestyle\cs\build\microsoft.codeanalysis.csharp.codestyle.targets
++Microsoft.NET.Sdk.WindowsDesktop.WindowsForms.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk.windowsdesktop\targets\microsoft.net.sdk.windowsdesktop.windowsforms.targets
++Microsoft.WinFX.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk.windowsdesktop\targets\microsoft.winfx.targets
++EBWebView
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\
++BritSystem_Log_2025-04-26.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\britsystem_log_2025-04-26.log
++BritSystem_Log_2025-04-27.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\britsystem_log_2025-04-27.log
++BritSystem_Log_2025-04-28.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\britsystem_log_2025-04-28.log
++BritSystem_Log_2025-04-29.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\britsystem_log_2025-04-29.log
++BritSystem_Log_2025-04-30.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\britsystem_log_2025-04-30.log
++BritSystem_Log_2025-05-06.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\britsystem_log_2025-05-06.log
++BritSystem_Log_2025-05-07.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\britsystem_log_2025-05-07.log
++BrittlenessCalculation_20250425_233833.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\brittlenesscalculation_20250425_233833.log
++Calculation_20250425_083545.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\calculation_20250425_083545.log
++Calculation_20250425_084053.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\calculation_20250425_084053.log
++Calculation_20250425_085845.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\calculation_20250425_085845.log
++Calculation_20250425_085856.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\calculation_20250425_085856.log
++Calculation_20250425_090924.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\calculation_20250425_090924.log
++Calculation_20250425_092640.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\calculation_20250425_092640.log
++Calculation_20250425_092723.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\calculation_20250425_092723.log
++Calculation_20250425_092731.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\calculation_20250425_092731.log
++Calculation_20250425_094224.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\calculation_20250425_094224.log
++Calculation_20250425_095103.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\calculation_20250425_095103.log
++Calculation_20250425_095130.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\logs\calculation_20250425_095130.log
++unix
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\unix\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\unix\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\unix\
++win
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\
++win-arm
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-arm\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-arm\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-arm\
++System.Windows.Forms.Analyzers.props
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk.windowsdesktop\targets\system.windows.forms.analyzers.props
++Microsoft.Managed.Core.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\roslyn\microsoft.managed.core.targets
++Microsoft.Managed.DesignTime.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\microsoft\visualstudio\managed\microsoft.managed.designtime.targets
++Microsoft.Common.CurrentVersion.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\amd64\microsoft.common.currentversion.targets
++InitializeSourceControlInformation.targets
i:{20f450ba-a30f-**********************}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.sourcelink.common\build\initializesourcecontrolinformation.targets
++AmountExtractionHeuristicRegexes
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\amountextractionheuristicregexes\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\amountextractionheuristicregexes\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\amountextractionheuristicregexes\
++AutoLaunchProtocolsComponent
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\autolaunchprotocolscomponent\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\autolaunchprotocolscomponent\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\autolaunchprotocolscomponent\
++CertificateRevocation
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\certificaterevocation\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\certificaterevocation\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\certificaterevocation\
++CookieReadinessList
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\cookiereadinesslist\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\cookiereadinesslist\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\cookiereadinesslist\
++Crashpad
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\crashpad\
++Default
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\
++Domain Actions
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\domain actions\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\domain actions\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\domain actions\
++GraphiteDawnCache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\graphitedawncache\
++GrShaderCache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\grshadercache\
++hyphen-data
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\hyphen-data\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\hyphen-data\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\hyphen-data\
++OpenCookieDatabase
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\opencookiedatabase\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\opencookiedatabase\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\opencookiedatabase\
++OriginTrials
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\origintrials\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\origintrials\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\origintrials\
++PKIMetadata
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\pkimetadata\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\pkimetadata\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\pkimetadata\
++ProbabilisticRevealTokenRegistry
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\probabilisticrevealtokenregistry\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\probabilisticrevealtokenregistry\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\probabilisticrevealtokenregistry\
++ShaderCache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\shadercache\
++SmartScreen
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\
++Speech Recognition
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\speech recognition\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\speech recognition\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\speech recognition\
++Subresource Filter
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\subresource filter\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\subresource filter\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\subresource filter\
++Trust Protection Lists
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\trust protection lists\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\trust protection lists\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\trust protection lists\
++TrustTokenKeyCommitments
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\trusttokenkeycommitments\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\trusttokenkeycommitments\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\trusttokenkeycommitments\
++WidevineCdm
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\widevinecdm\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\widevinecdm\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\widevinecdm\
++ZxcvbnData
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\zxcvbndata\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\zxcvbndata\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\zxcvbndata\
++Last Version
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\last version
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\last version
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\last version
++Local State
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\local state
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\local state
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\local state
++Variations
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\variations
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\variations
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\variations
++lib
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\unix\lib\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\lib\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\unix\lib\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\lib\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\unix\lib\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\lib\
++RevisitationBloomfilter
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\revisitationbloomfilter
++Microsoft.Managed.Core.CurrentVersions.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\roslyn\microsoft.managed.core.currentversions.targets
++BritSystem.csproj.user
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\britsystem.csproj.user
++Microsoft.NET.props
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\amd64\microsoft.net.props
++Microsoft.CodeAnalysis.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\microsoft\visualstudio\v17.0\codeanalysis\microsoft.codeanalysis.targets
++Microsoft.Xaml.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\amd64\microsoft.xaml.targets
i:{20f450ba-a30f-**********************}:c:\windows\microsoft.net\framework\v4.0.30319\microsoft.xaml.targets
++Microsoft.WorkflowBuildExtensions.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\bin\amd64\microsoft.workflowbuildextensions.targets
i:{20f450ba-a30f-**********************}:c:\windows\microsoft.net\framework\v4.0.30319\microsoft.workflowbuildextensions.targets
++Microsoft.TeamTest.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\microsoft\visualstudio\v17.0\teamtest\microsoft.teamtest.targets
++NuGet.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\common7\ide\commonextensions\microsoft\nuget\nuget.targets
++Maui.Upgrade.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\microsoft.common.targets\importafter\maui.upgrade.targets
++Microsoft.NET.Build.Extensions.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\microsoft.common.targets\importafter\microsoft.net.build.extensions.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\microsoft\microsoft.net.build.extensions\microsoft.net.build.extensions.targets
++Microsoft.NuGet.ImportAfter.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\microsoft.common.targets\importafter\microsoft.nuget.importafter.targets
++Microsoft.Web.ImportAfter.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\microsoft.common.targets\importafter\microsoft.web.importafter.targets
++Microsoft.WebTools.Aspire.ImportAfter.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\current\microsoft.common.targets\importafter\microsoft.webtools.aspire.importafter.targets
++System.Text.Json.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\system.text.json\7.0.0\buildtransitive\net6.0\system.text.json.targets
++Microsoft.Web.WebView2.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.1774.30\build\microsoft.web.webview2.targets
++Microsoft.Extensions.Options.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.0\buildtransitive\net6.0\microsoft.extensions.options.targets
++Microsoft.Extensions.Logging.Abstractions.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.0\buildtransitive\net6.0\microsoft.extensions.logging.abstractions.targets
++Microsoft.AspNetCore.Components.Analyzers.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.aspnetcore.components.analyzers\8.0.0\buildtransitive\netstandard2.0\microsoft.aspnetcore.components.analyzers.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.aspnetcore.components.analyzers\8.0.0\build\netstandard2.0\microsoft.aspnetcore.components.analyzers.targets
++AntDesign.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign\1.3.2\build\antdesign.targets
++attachments
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\attachments\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\attachments\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\crashpad\attachments\
++reports
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\reports\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\reports\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\crashpad\reports\
++metadata
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\metadata
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\metadata
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\crashpad\metadata
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\metadata\
++settings.dat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\settings.dat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\settings.dat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\crashpad\settings.dat
++throttle_store.dat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\throttle_store.dat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\crashpad\throttle_store.dat
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\crashpad\throttle_store.dat
++AutofillStrikeDatabase
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\autofillstrikedatabase\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\autofillstrikedatabase\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\autofillstrikedatabase\
++blob_storage
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\blob_storage\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\blob_storage\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\blob_storage\
++BudgetDatabase
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\budgetdatabase\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\budgetdatabase\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\budgetdatabase\
++Cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\cache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\cache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\cache\
++ClientCertificates
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\clientcertificates\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\clientcertificates\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\clientcertificates\
++Code Cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\code cache\
++commerce_subscription_db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\commerce_subscription_db\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\commerce_subscription_db\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\commerce_subscription_db\
++DawnGraphiteCache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawngraphitecache\
++DawnWebGPUCache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawnwebgpucache\
++discounts_db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\discounts_db\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\discounts_db\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\discounts_db\
++Download Service
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\download service\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\download service\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\download service\
++EdgeEDrop
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\edgeedrop\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\edgeedrop\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\edgeedrop\
++Extension Rules
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension rules\
++Extension Scripts
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension scripts\
++Extension State
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension state\
++Feature Engagement Tracker
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\feature engagement tracker\
++GPUCache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\gpucache\
++Local Storage
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\local storage\
++Network
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\network\
++optimization_guide_hint_cache_store
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\optimization_guide_hint_cache_store\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\optimization_guide_hint_cache_store\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\optimization_guide_hint_cache_store\
++parcel_tracking_db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\parcel_tracking_db\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\parcel_tracking_db\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\parcel_tracking_db\
++PersistentOriginTrials
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\persistentorigintrials\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\persistentorigintrials\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\persistentorigintrials\
++Safe Browsing Network
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\safe browsing network\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\safe browsing network\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\safe browsing network\
++Session Storage
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\session storage\
++Sessions
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sessions\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sessions\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\sessions\
++Shared Dictionary
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared dictionary\
++shared_proto_db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\
++Site Characteristics Database
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\site characteristics database\
++Sync Data
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\sync data\
++DIPS
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dips
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dips
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dips
++ExtensionActivityComp
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extensionactivitycomp
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extensionactivitycomp
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extensionactivitycomp
++ExtensionActivityComp-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extensionactivitycomp-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extensionactivitycomp-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extensionactivitycomp-journal
++ExtensionActivityEdge
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extensionactivityedge
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extensionactivityedge
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extensionactivityedge
++ExtensionActivityEdge-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extensionactivityedge-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extensionactivityedge-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extensionactivityedge-journal
++Favicons
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\favicons
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\favicons
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\favicons
++Favicons-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\favicons-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\favicons-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\favicons-journal
++favorites_diagnostic.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\favorites_diagnostic.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\favorites_diagnostic.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\favorites_diagnostic.log
++heavy_ad_intervention_opt_out.db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\heavy_ad_intervention_opt_out.db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\heavy_ad_intervention_opt_out.db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\heavy_ad_intervention_opt_out.db
++heavy_ad_intervention_opt_out.db-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\heavy_ad_intervention_opt_out.db-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\heavy_ad_intervention_opt_out.db-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\heavy_ad_intervention_opt_out.db-journal
++History
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\history
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\history
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\history
++History-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\history-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\history-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\history-journal
++LOCK
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\autofillstrikedatabase\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\budgetdatabase\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\clientcertificates\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\commerce_subscription_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\discounts_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\optimization_guide_hint_cache_store\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\parcel_tracking_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\persistentorigintrials\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\autofillstrikedatabase\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\budgetdatabase\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\clientcertificates\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\commerce_subscription_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\discounts_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\optimization_guide_hint_cache_store\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\parcel_tracking_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\persistentorigintrials\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\autofillstrikedatabase\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\budgetdatabase\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\clientcertificates\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\commerce_subscription_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\discounts_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension rules\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension scripts\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension state\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\optimization_guide_hint_cache_store\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\parcel_tracking_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\persistentorigintrials\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\session storage\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\site characteristics database\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\download service\entrydb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\availabilitydb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\eventdb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\download service\entrydb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\availabilitydb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\eventdb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\download service\entrydb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\feature engagement tracker\availabilitydb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\feature engagement tracker\eventdb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\local storage\leveldb\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\metadata\lock
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\sync data\leveldb\lock
++LOG
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\autofillstrikedatabase\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\budgetdatabase\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\clientcertificates\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\commerce_subscription_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\discounts_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\optimization_guide_hint_cache_store\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\parcel_tracking_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\persistentorigintrials\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\autofillstrikedatabase\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\budgetdatabase\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\clientcertificates\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\commerce_subscription_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\discounts_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\optimization_guide_hint_cache_store\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\parcel_tracking_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\persistentorigintrials\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\autofillstrikedatabase\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\budgetdatabase\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\clientcertificates\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\commerce_subscription_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\discounts_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension rules\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension scripts\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension state\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\optimization_guide_hint_cache_store\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\parcel_tracking_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\persistentorigintrials\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\session storage\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\site characteristics database\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\download service\entrydb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\availabilitydb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\eventdb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\download service\entrydb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\availabilitydb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\eventdb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\download service\entrydb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\feature engagement tracker\availabilitydb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\feature engagement tracker\eventdb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\local storage\leveldb\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\metadata\log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\sync data\leveldb\log
++Login Data
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\login data
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\login data
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\login data
++Login Data-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\login data-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\login data-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\login data-journal
++Network Action Predictor
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network action predictor
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network action predictor
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\network action predictor
++Network Action Predictor-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network action predictor-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network action predictor-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\network action predictor-journal
++Preferences
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\preferences
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\preferences
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\preferences
++PreferredApps
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\preferredapps
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\preferredapps
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\preferredapps
++README
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\readme
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\readme
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\readme
++Secure Preferences
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\secure preferences
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\secure preferences
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\secure preferences
++SharedStorage
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sharedstorage
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sharedstorage
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\sharedstorage
++Top Sites
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\top sites
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\top sites
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\top sites
++Top Sites-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\top sites-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\top sites-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\top sites-journal
++Visited Links
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\visited links
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\visited links
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\visited links
++Web Data
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\web data
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\web data
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\web data
++Web Data-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\web data-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\web data-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\web data-journal
++data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\graphitedawncache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\grshadercache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\shadercache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawngraphitecache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawnwebgpucache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\gpucache\data_0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\cache\cache_data\data_0
++data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\graphitedawncache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\grshadercache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\shadercache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawngraphitecache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawnwebgpucache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\gpucache\data_1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\cache\cache_data\data_1
++data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\graphitedawncache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\grshadercache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\shadercache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawngraphitecache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawnwebgpucache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\gpucache\data_2
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\cache\cache_data\data_2
++data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\graphitedawncache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\grshadercache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\shadercache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawngraphitecache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawnwebgpucache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\gpucache\data_3
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\cache\cache_data\data_3
++index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\graphitedawncache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\shadercache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\graphitedawncache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\grshadercache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\shadercache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawngraphitecache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\dawnwebgpucache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\gpucache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawngraphitecache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dawnwebgpucache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\gpucache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\js\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\wasm\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\cache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\cache\cache_data\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\js\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\wasm\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\cache\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\code cache\js\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\code cache\wasm\index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared dictionary\cache\index
++f_000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\f_000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\f_000001
++RemoteData
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\remotedata\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\remotedata\
++Unindexed Rules
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\subresource filter\unindexed rules\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\subresource filter\unindexed rules\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\subresource filter\unindexed rules\
++netcoreapp3.1
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\unix\lib\netcoreapp3.1\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\lib\netcoreapp3.1\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\unix\lib\netcoreapp3.1\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\lib\netcoreapp3.1\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\unix\lib\netcoreapp3.1\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\lib\netcoreapp3.1\
++net8.0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\lib\net8.0\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\lib\net8.0\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\lib\net8.0\
++netstandard2.0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\lib\netstandard2.0\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\lib\netstandard2.0\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\lib\netstandard2.0\
++Microsoft.Data.SqlClient.SNI.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-arm\native\microsoft.data.sqlclient.sni.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-arm64\native\microsoft.data.sqlclient.sni.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-x64\native\microsoft.data.sqlclient.sni.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win-x86\native\microsoft.data.sqlclient.sni.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-arm\native\microsoft.data.sqlclient.sni.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-arm64\native\microsoft.data.sqlclient.sni.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-x64\native\microsoft.data.sqlclient.sni.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win-x86\native\microsoft.data.sqlclient.sni.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-x86\native\microsoft.data.sqlclient.sni.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-x64\native\microsoft.data.sqlclient.sni.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-arm64\native\microsoft.data.sqlclient.sni.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win-arm\native\microsoft.data.sqlclient.sni.dll
++LOG.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\autofillstrikedatabase\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\budgetdatabase\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\clientcertificates\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\commerce_subscription_db\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\discounts_db\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\optimization_guide_hint_cache_store\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\parcel_tracking_db\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\persistentorigintrials\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\autofillstrikedatabase\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\budgetdatabase\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\clientcertificates\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\commerce_subscription_db\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\discounts_db\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension state\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\optimization_guide_hint_cache_store\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\parcel_tracking_db\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\persistentorigintrials\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\session storage\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\site characteristics database\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\download service\entrydb\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\availabilitydb\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\eventdb\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\download service\entrydb\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\feature engagement tracker\availabilitydb\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\feature engagement tracker\eventdb\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\local storage\leveldb\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\metadata\log.old
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\sync data\leveldb\log.old
++f_000002
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\grshadercache\f_000002
++DIPS-shm
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dips-shm
++DIPS-wal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\dips-wal
++Microsoft.AspNetCore.StaticWebAssetEndpoints.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign\1.3.2\build\microsoft.aspnetcore.staticwebassetendpoints.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign.prolayout\1.3.1\build\microsoft.aspnetcore.staticwebassetendpoints.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign.charts\0.6.1\build\microsoft.aspnetcore.staticwebassetendpoints.props
++Microsoft.AspNetCore.StaticWebAssets.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign\1.3.2\build\microsoft.aspnetcore.staticwebassets.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign.prolayout\1.3.1\build\microsoft.aspnetcore.staticwebassets.props
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\antdesign.charts\0.6.1\build\microsoft.aspnetcore.staticwebassets.props
++Microsoft.WebTools.Aspire.targets
i:{20f450ba-a30f-**********************}:c:\program files\microsoft visual studio\2022\community\msbuild\microsoft\visualstudio\webtools.aspire\microsoft.webtools.aspire.targets
++Common.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.1774.30\build\common.targets
++ac20fb41-4607-469d-afce-cadc51463085
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\blob_storage\ac20fb41-4607-469d-afce-cadc51463085\
++Cache_Data
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\cache\cache_data\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\cache\cache_data\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\cache\cache_data\
++js
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\js\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\js\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\code cache\js\
++wasm
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\wasm\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\wasm\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\code cache\wasm\
++EntryDB
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\download service\entrydb\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\download service\entrydb\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\download service\entrydb\
++Files
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\download service\files\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\download service\files\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\download service\files\
++EdgeEDropSQLite.db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\edgeedrop\edgeedropsqlite.db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\edgeedrop\edgeedropsqlite.db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\edgeedrop\edgeedropsqlite.db
++EdgeEDropSQLite.db-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\edgeedrop\edgeedropsqlite.db-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\edgeedrop\edgeedropsqlite.db-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\edgeedrop\edgeedropsqlite.db-journal
++000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension rules\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension scripts\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension state\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\session storage\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\site characteristics database\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\local storage\leveldb\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\metadata\000003.log
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\sync data\leveldb\000003.log
++CURRENT
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension rules\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension scripts\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension state\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\session storage\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\site characteristics database\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\local storage\leveldb\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\metadata\current
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\sync data\leveldb\current
++MANIFEST-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension rules\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension scripts\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\extension state\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\session storage\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\site characteristics database\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension rules\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension scripts\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\extension state\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\session storage\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\site characteristics database\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared_proto_db\metadata\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\local storage\leveldb\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared_proto_db\metadata\manifest-000001
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\sync data\leveldb\manifest-000001
++AvailabilityDB
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\availabilitydb\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\availabilitydb\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\feature engagement tracker\availabilitydb\
++EventDB
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\eventdb\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\feature engagement tracker\eventdb\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\feature engagement tracker\eventdb\
++leveldb
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\local storage\leveldb\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\local storage\leveldb\
++Cookies
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\cookies
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\cookies
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\network\cookies
++Cookies-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\cookies-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\cookies-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\network\cookies-journal
++Network Persistent State
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\network persistent state
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\network persistent state
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\network\network persistent state
++NetworkDataMigrated
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\networkdatamigrated
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\safe browsing network\networkdatamigrated
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\networkdatamigrated
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\safe browsing network\networkdatamigrated
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\network\networkdatamigrated
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\safe browsing network\networkdatamigrated
++SCT Auditing Pending Reports
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\sct auditing pending reports
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\sct auditing pending reports
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\network\sct auditing pending reports
++Sdch Dictionaries
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\sdch dictionaries
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\sdch dictionaries
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\network\sdch dictionaries
++Trust Tokens
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\trust tokens
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\trust tokens
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\network\trust tokens
++Trust Tokens-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\trust tokens-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\trust tokens-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\network\trust tokens-journal
++Safe Browsing Cookies
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\safe browsing network\safe browsing cookies
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\safe browsing network\safe browsing cookies
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\safe browsing network\safe browsing cookies
++Safe Browsing Cookies-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\safe browsing network\safe browsing cookies-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\safe browsing network\safe browsing cookies-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\safe browsing network\safe browsing cookies-journal
++cache
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\cache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\cache\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared dictionary\cache\
++db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\db
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared dictionary\db
++db-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\db-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\db-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared dictionary\db-journal
++LevelDB
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\sync data\leveldb\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\sync data\leveldb\
++customSettings
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\remotedata\customsettings
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\remotedata\customsettings
++customSettings_F95BA787499AB4FA9EFFF472CE383A14
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\remotedata\customsettings_f95ba787499ab4fa9efff472ce383a14
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\remotedata\customsettings_f95ba787499ab4fa9efff472ce383a14
++edgeSettings
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\remotedata\edgesettings
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\remotedata\edgesettings
++edgeSettings_2.0-0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\remotedata\edgesettings_2.0-0
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\smartscreen\remotedata\edgesettings_2.0-0
++System.Diagnostics.EventLog.Messages.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\runtimes\win\lib\net8.0\system.diagnostics.eventlog.messages.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\publish\runtimes\win\lib\net8.0\system.diagnostics.eventlog.messages.dll
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\runtimes\win\lib\net8.0\system.diagnostics.eventlog.messages.dll
++dea3fc5d-7233-446b-9fff-8d77c7a7ed0a
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\blob_storage\dea3fc5d-7233-446b-9fff-8d77c7a7ed0a\
++Reporting and NEL
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\reporting and nel
++Reporting and NEL-journal
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\reporting and nel-journal
++TransportSecurity
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\network\transportsecurity
++8f6dc071-ba83-411b-a09f-8a14a02e3576
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\blob_storage\8f6dc071-ba83-411b-a09f-8a14a02e3576\
++wv2winrt.targets
i:{20f450ba-a30f-**********************}:c:\users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.1774.30\build\wv2winrt.targets
++index-dir
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\js\index-dir\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\wasm\index-dir\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\cache\index-dir\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\js\index-dir\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\wasm\index-dir\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\cache\index-dir\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\code cache\js\index-dir\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\code cache\wasm\index-dir\
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared dictionary\cache\index-dir\
++the-real-index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\js\index-dir\the-real-index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\wasm\index-dir\the-real-index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\debug\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\cache\index-dir\the-real-index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\js\index-dir\the-real-index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\code cache\wasm\index-dir\the-real-index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\britsystem.exe.webview2\ebwebview\default\shared dictionary\cache\index-dir\the-real-index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\code cache\js\index-dir\the-real-index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\code cache\wasm\index-dir\the-real-index
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\bin\release\net8.0-windows\webview2data\ebwebview\default\shared dictionary\cache\index-dir\the-real-index
++MineralStackedBarChartControl.resx
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\controls\mineralstackedbarchartcontrol.resx
++BritSystem.Controls.MineralStackedBarChartControl.resources
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\obj\release\net8.0-windows\britsystem.controls.mineralstackedbarchartcontrol.resources
++TestStackedBarChart.cs
i:{20f450ba-a30f-**********************}:c:\users\<USER>\desktop\brittleness index system\repos\britsystem\teststackedbarchart.cs
