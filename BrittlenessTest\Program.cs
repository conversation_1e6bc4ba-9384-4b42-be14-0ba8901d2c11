﻿using System;

namespace BrittlenessIndexTest
{
    /// <summary>
    /// 脆性指数计算测试程序
    /// 用于验证修复后的计算逻辑是否正确
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 脆性指数计算测试程序 ===");
            Console.WriteLine();

            // 测试数据 - 使用一些典型的岩石力学参数
            // 同时测试单位转换的影响
            var testData = new[]
            {
                new { Depth = 1000.0, Density_kg_m3 = 2500.0, Vp_km_s = 4.5, Vs_km_s = 2.8, Description = "测试点1" },
                new { Depth = 1000.5, Density_kg_m3 = 2520.0, Vp_km_s = 4.6, Vs_km_s = 2.9, Description = "测试点2" },
                new { Depth = 1001.0, Density_kg_m3 = 2540.0, Vp_km_s = 4.7, Vs_km_s = 3.0, Description = "测试点3" },
                new { Depth = 1001.2, Density_kg_m3 = 2560.0, Vp_km_s = 4.8, Vs_km_s = 3.1, Description = "测试点4" },
                new { Depth = 1002.0, Density_kg_m3 = 2580.0, Vp_km_s = 4.9, Vs_km_s = 3.2, Description = "测试点5" }
            };

            Console.WriteLine("使用修复后的完整计算流程：");
            Console.WriteLine("输入单位：密度(kg/m³), 速度(km/s) -> 转换为标准单位 -> 计算脆性指数");
            Console.WriteLine();

            // 先用一个具体例子验证公式
            Console.WriteLine("=== 公式验证示例 ===");
            double test_density_kg = 2500; // kg/m³
            double test_vp_km = 4.5; // km/s
            double test_vs_km = 2.8; // km/s

            double test_density_g = test_density_kg / 1000.0; // -> 2.5 g/cm³
            double test_vp_m = test_vp_km * 1000.0; // -> 4500 m/s
            double test_vs_m = test_vs_km * 1000.0; // -> 2800 m/s

            double test_vp2 = test_vp_m * test_vp_m; // 20,250,000
            double test_vs2 = test_vs_m * test_vs_m; // 7,840,000
            double test_numerator = 3 * test_vp2 - 4 * test_vs2; // 60,750,000 - 31,360,000 = 29,390,000
            double test_denominator = test_vp2 - test_vs2; // 20,250,000 - 7,840,000 = 12,410,000
            // 使用标准弹性力学公式
            double test_density_kg_m3 = test_density_g * 1000; // g/cm³ -> kg/m³
            double test_Ed = test_density_kg_m3 * test_vs2 * test_numerator / test_denominator / 1e9; // Pa -> GPa

            Console.WriteLine($"验证计算：ρ={test_density_g} g/cm³, Vp={test_vp_m} m/s, Vs={test_vs_m} m/s");
            Console.WriteLine($"Vp²={test_vp2:E2}, Vs²={test_vs2:E2}");
            Console.WriteLine($"分子={test_numerator:E2}, 分母={test_denominator:E2}");
            Console.WriteLine($"Ed = {test_density_kg_m3} × {test_vs2:E2} × {test_numerator:E2} / {test_denominator:E2} / 10^9 = {test_Ed:F2} GPa");
            Console.WriteLine();

            Console.WriteLine("深度\t\t密度(kg/m³)\tVp(km/s)\tVs(km/s)\tEd(GPa)\t\tEs(GPa)\t\tMuS\t\t脆性指数(%)");
            Console.WriteLine("---------------------------------------------------------------------------------------------");

            foreach (var data in testData)
            {
                // 单位转换
                double density_g_cm3 = data.Density_kg_m3 / 1000.0; // kg/m³ -> g/cm³
                double vp_m_s = data.Vp_km_s * 1000.0; // km/s -> m/s
                double vs_m_s = data.Vs_km_s * 1000.0; // km/s -> m/s

                // 计算动态杨氏模量（使用标准弹性力学公式）
                // Ed = ρ(kg/m³) × Vs^2(m/s) × (3Vp^2 - 4Vs^2) / (Vp^2 - Vs^2)
                double vp2 = vp_m_s * vp_m_s;
                double vs2 = vs_m_s * vs_m_s;
                double density_kg_m3 = density_g_cm3 * 1000; // g/cm³ -> kg/m³
                double Ed = density_kg_m3 * vs2 * (3 * vp2 - 4 * vs2) / (vp2 - vs2) / 1e9; // Pa -> GPa

                // 计算动态泊松比
                double MuD = (vp2 - 2 * vs2) / (2 * (vp2 - vs2));

                // 计算静态参数
                double Es = Ed * 0.5823 + 7.566;
                double MuS = MuD * 0.6648 + 0.0514;

                // 计算脆性指数
                double brittlenessIndex = CalculateBrittlenessIndexFixed(Es, MuS);

                Console.WriteLine($"{data.Depth:F1}\t\t{data.Density_kg_m3:F0}\t\t{data.Vp_km_s:F1}\t\t{data.Vs_km_s:F1}\t\t{Ed:F2}\t\t{Es:F2}\t\t{MuS:F3}\t\t{brittlenessIndex:F2}");
            }

            Console.WriteLine();
            Console.WriteLine("=== 修复前后对比 ===");
            Console.WriteLine();
            Console.WriteLine("修复前的问题：");
            Console.WriteLine("1. 动态杨氏模量公式错误：Ed = ρ * Vs^2 * (3Vp^2 - 4Vs^2) / ((Vp^2 - Vs^2) * 1000)");
            Console.WriteLine("2. 单位转换错误：未正确处理kg/m³到g/cm³，km/s到m/s的转换");
            Console.WriteLine("3. 使用动态范围计算脆性指数，导致结果不稳定");
            Console.WriteLine();
            Console.WriteLine("修复后的改进：");
            Console.WriteLine("1. 使用正确的文献公式：Ed = 10^-3 × ρ × Vs^2 × (3Vp^2 - 4Vs^2) / (Vp^2 - Vs^2)");
            Console.WriteLine("2. 正确的单位转换：ρ(g/cm³) = ρ(kg/m³) ÷ 1000, V(m/s) = V(km/s) × 1000");
            Console.WriteLine("3. 使用固定范围计算脆性指数：Es(5-80 GPa), MuS(0.1-0.4)");
            Console.WriteLine("4. 数据表结构简化：只显示深度列，去掉顶深和底深");

        }

        /// <summary>
        /// 修复后的脆性指数计算方法（固定范围）
        /// 与real.txt中的计算方法一致
        /// </summary>
        static double CalculateBrittlenessIndexFixed(double Es, double MuS)
        {
            // 使用固定范围
            double EsMin = 5.0;   // GPa
            double EsMax = 80.0;  // GPa
            double MuSMin = 0.1;
            double MuSMax = 0.4;

            // 归一化杨氏模量脆性指数
            double EBRIT = (Es - EsMin) / (EsMax - EsMin) * 100;
            EBRIT = Math.Max(0, Math.Min(100, EBRIT));

            // 归一化泊松比脆性指数
            double PBRIT = (MuSMax - MuS) / (MuSMax - MuSMin) * 100;
            PBRIT = Math.Max(0, Math.Min(100, PBRIT));

            // 综合脆性指数
            return (EBRIT + PBRIT) / 2;
        }

        /// <summary>
        /// 原来的Rickman公式（乘积方式）
        /// </summary>
        static double CalculateBrittlenessIndexRickman(double Es, double MuS)
        {
            double E_min = 10.0;  // GPa
            double E_max = 80.0;  // GPa
            double v_min = 0.15;
            double v_max = 0.35;

            double brittlenessIndex = ((Es - E_min) / (E_max - E_min)) *
                                    ((v_max - MuS) / (v_max - v_min));

            return Math.Max(0, Math.Min(1, brittlenessIndex));
        }

        /// <summary>
        /// 使用动态范围的计算方法（问题方法）
        /// </summary>
        static double CalculateBrittlenessIndexDynamic(double Es, double MuS, double EsMin, double EsMax, double MuSMin, double MuSMax)
        {
            // 归一化杨氏模量脆性指数
            double EBRIT = 0;
            if (EsMax > EsMin)
            {
                EBRIT = (Es - EsMin) / (EsMax - EsMin) * 100;
                EBRIT = Math.Max(0, Math.Min(100, EBRIT));
            }

            // 归一化泊松比脆性指数
            double PBRIT = 0;
            if (MuSMax > MuSMin)
            {
                PBRIT = (MuSMax - MuS) / (MuSMax - MuSMin) * 100;
                PBRIT = Math.Max(0, Math.Min(100, PBRIT));
            }

            // 综合脆性指数
            return (EBRIT + PBRIT) / 2;
        }
    }
}
