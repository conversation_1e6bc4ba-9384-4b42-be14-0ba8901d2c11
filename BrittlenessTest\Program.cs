﻿using System;

namespace BrittlenessIndexTest
{
    /// <summary>
    /// 脆性指数计算测试程序
    /// 用于验证修复后的计算逻辑是否正确
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 脆性指数计算测试程序 ===");
            Console.WriteLine();

            // 测试数据 - 使用一些典型的岩石力学参数
            var testData = new[]
            {
                new { Depth = 1000.0, Es = 25.5, MuS = 0.25, Description = "测试点1" },
                new { Depth = 1000.5, Es = 28.3, MuS = 0.23, Description = "测试点2" },
                new { Depth = 1001.0, Es = 30.1, MuS = 0.22, Description = "测试点3" },
                new { Depth = 1001.2, Es = 32.8, MuS = 0.21, Description = "测试点4" },
                new { Depth = 1002.0, Es = 35.2, MuS = 0.20, Description = "测试点5" }
            };

            Console.WriteLine("使用修复后的计算方法（固定范围）：");
            Console.WriteLine("参数范围：EsMin=5.0, EsMax=80.0, MuSMin=0.1, MuSMax=0.4");
            Console.WriteLine();
            Console.WriteLine("深度\t\tEs(GPa)\t\tMuS\t\t脆性指数(%)");
            Console.WriteLine("------------------------------------------------------------");

            foreach (var data in testData)
            {
                double brittlenessIndex = CalculateBrittlenessIndexFixed(data.Es, data.MuS);
                Console.WriteLine($"{data.Depth:F1}\t\t{data.Es:F1}\t\t{data.MuS:F3}\t\t{brittlenessIndex:F2}");
            }

            Console.WriteLine();
            Console.WriteLine("=== 对比不同计算方法的结果 ===");
            Console.WriteLine();

            // 对比原来的Rickman公式（乘积方式）
            Console.WriteLine("使用原Rickman公式（乘积方式）：");
            Console.WriteLine("参数范围：EsMin=10.0, EsMax=80.0, MuSMin=0.15, MuSMax=0.35");
            Console.WriteLine();
            Console.WriteLine("深度\t\tEs(GPa)\t\tMuS\t\t脆性指数");
            Console.WriteLine("------------------------------------------------------------");

            foreach (var data in testData)
            {
                double brittlenessIndex = CalculateBrittlenessIndexRickman(data.Es, data.MuS);
                Console.WriteLine($"{data.Depth:F1}\t\t{data.Es:F1}\t\t{data.MuS:F3}\t\t{brittlenessIndex:F3}");
            }

            Console.WriteLine();
            Console.WriteLine("=== 使用动态范围的计算结果（问题方法）===");
            Console.WriteLine();

            // 计算动态范围
            double esMin = double.MaxValue, esMax = double.MinValue;
            double muSMin = double.MaxValue, muSMax = double.MinValue;

            foreach (var data in testData)
            {
                if (data.Es < esMin) esMin = data.Es;
                if (data.Es > esMax) esMax = data.Es;
                if (data.MuS < muSMin) muSMin = data.MuS;
                if (data.MuS > muSMax) muSMax = data.MuS;
            }

            Console.WriteLine($"动态范围：EsMin={esMin:F1}, EsMax={esMax:F1}, MuSMin={muSMin:F3}, MuSMax={muSMax:F3}");
            Console.WriteLine();
            Console.WriteLine("深度\t\tEs(GPa)\t\tMuS\t\t脆性指数(%)");
            Console.WriteLine("------------------------------------------------------------");

            foreach (var data in testData)
            {
                double brittlenessIndex = CalculateBrittlenessIndexDynamic(data.Es, data.MuS, esMin, esMax, muSMin, muSMax);
                Console.WriteLine($"{data.Depth:F1}\t\t{data.Es:F1}\t\t{data.MuS:F3}\t\t{brittlenessIndex:F2}");
            }

            Console.WriteLine();
            Console.WriteLine("修复总结：");
            Console.WriteLine("1. 原系统使用动态范围导致脆性指数计算不稳定");
            Console.WriteLine("2. 修复后使用固定范围(Es: 5-80 GPa, MuS: 0.1-0.4)");
            Console.WriteLine("3. 计算公式改为平均值方式: (EBRIT + PBRIT) / 2");
            Console.WriteLine("4. 结果单位统一为百分比(0-100%)");
        }

        /// <summary>
        /// 修复后的脆性指数计算方法（固定范围）
        /// 与real.txt中的计算方法一致
        /// </summary>
        static double CalculateBrittlenessIndexFixed(double Es, double MuS)
        {
            // 使用固定范围
            double EsMin = 5.0;   // GPa
            double EsMax = 80.0;  // GPa
            double MuSMin = 0.1;
            double MuSMax = 0.4;

            // 归一化杨氏模量脆性指数
            double EBRIT = (Es - EsMin) / (EsMax - EsMin) * 100;
            EBRIT = Math.Max(0, Math.Min(100, EBRIT));

            // 归一化泊松比脆性指数
            double PBRIT = (MuSMax - MuS) / (MuSMax - MuSMin) * 100;
            PBRIT = Math.Max(0, Math.Min(100, PBRIT));

            // 综合脆性指数
            return (EBRIT + PBRIT) / 2;
        }

        /// <summary>
        /// 原来的Rickman公式（乘积方式）
        /// </summary>
        static double CalculateBrittlenessIndexRickman(double Es, double MuS)
        {
            double E_min = 10.0;  // GPa
            double E_max = 80.0;  // GPa
            double v_min = 0.15;
            double v_max = 0.35;

            double brittlenessIndex = ((Es - E_min) / (E_max - E_min)) *
                                    ((v_max - MuS) / (v_max - v_min));

            return Math.Max(0, Math.Min(1, brittlenessIndex));
        }

        /// <summary>
        /// 使用动态范围的计算方法（问题方法）
        /// </summary>
        static double CalculateBrittlenessIndexDynamic(double Es, double MuS, double EsMin, double EsMax, double MuSMin, double MuSMax)
        {
            // 归一化杨氏模量脆性指数
            double EBRIT = 0;
            if (EsMax > EsMin)
            {
                EBRIT = (Es - EsMin) / (EsMax - EsMin) * 100;
                EBRIT = Math.Max(0, Math.Min(100, EBRIT));
            }

            // 归一化泊松比脆性指数
            double PBRIT = 0;
            if (MuSMax > MuSMin)
            {
                PBRIT = (MuSMax - MuS) / (MuSMax - MuSMin) * 100;
                PBRIT = Math.Max(0, Math.Min(100, PBRIT));
            }

            // 综合脆性指数
            return (EBRIT + PBRIT) / 2;
        }
    }
}
