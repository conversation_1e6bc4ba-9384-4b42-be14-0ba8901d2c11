﻿using System;
using System.Linq;

namespace BrittlenessIndexTest
{
    /// <summary>
    /// 脆性指数计算测试程序
    /// 用于验证修复后的计算逻辑是否与公式计算.cs一致
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 脆性指数计算测试程序 ===");
            Console.WriteLine("验证修复后的计算逻辑是否与公式计算.cs一致");
            Console.WriteLine();

            // 测试数据 - 使用实际的时差数据（根据用户分析修正）
            // 注意：这里使用时差值（μs/m），需要转换为速度
            var testData = new[]
            {
                new { Depth = 1000.0, Density_kg_m3 = 2210.8, DT_us_m = 334.7, DTS_us_m = 554.5, Description = "1000m处数据" },
                new { Depth = 1000.1, Density_kg_m3 = 2211.2, DT_us_m = 315.1, DTS_us_m = 463.7, Description = "1000.1m处数据" },
                new { Depth = 1001.0, Density_kg_m3 = 2215.6, DT_us_m = 298.3, DTS_us_m = 425.8, Description = "1001m处数据" },
                new { Depth = 1001.2, Density_kg_m3 = 2216.4, DT_us_m = 289.1, DTS_us_m = 412.3, Description = "1001.2m处数据" },
                new { Depth = 1002.0, Density_kg_m3 = 2220.8, DT_us_m = 275.6, DTS_us_m = 395.2, Description = "1002m处数据" }
            };

            Console.WriteLine("修正后的计算流程（使用正确的时差转速度）：");
            Console.WriteLine();

            // 先验证一个具体的计算
            Console.WriteLine("=== 修正前后对比验证 ===");
            double testDensity = 2210.8; // kg/m³
            double testDT = 334.7; // μs/m
            double testDTS = 554.5; // μs/m

            Console.WriteLine($"输入：密度={testDensity} kg/m³, DT={testDT} μs/m, DTS={testDTS} μs/m");

            // 修正前（错误）：直接使用时差值作为速度
            Console.WriteLine("\n修正前（错误的计算）：");
            double wrongVp = testDT; // 错误：直接使用时差值
            double wrongVs = testDTS; // 错误：直接使用时差值
            Console.WriteLine($"错误的Vp={wrongVp} m/s, Vs={wrongVs} m/s (Vp < Vs，违背物理规律)");
            double wrongEd = CalculateDynamicYoungsModulusFromVelocity(testDensity / 1000.0, wrongVp, wrongVs);
            Console.WriteLine($"错误的Ed = {wrongEd:F2} GPa");

            // 修正后（正确）：时差转换为速度
            Console.WriteLine("\n修正后（正确的计算）：");
            double correctVp = 1000000.0 / testDT; // 正确：Vp = 10^6 / DT
            double correctVs = 1000000.0 / testDTS; // 正确：Vs = 10^6 / DTS
            Console.WriteLine($"正确的Vp={correctVp:F1} m/s, Vs={correctVs:F1} m/s (Vp > Vs，符合物理规律)");
            double correctEd = CalculateDynamicYoungsModulusFromVelocity(testDensity / 1000.0, correctVp, correctVs);
            Console.WriteLine($"正确的Ed = {correctEd:F4} GPa");
            Console.WriteLine();

            // 第一步：计算所有数据点的静态参数（使用修正后的逻辑）
            var results = new List<(double Depth, double Vp, double Vs, double Ed, double MuD, double Es, double MuS)>();

            Console.WriteLine("第一步：修正后的动态和静态参数计算");
            Console.WriteLine("深度\t\t密度(kg/m³)\tDT(μs/m)\tDTS(μs/m)\tVp(m/s)\t\tVs(m/s)\t\tEd(GPa)\t\tMuD\t\tEs(GPa)\t\tMuS");
            Console.WriteLine("-------------------------------------------------------------------------------------------------------------------------------");

            foreach (var data in testData)
            {
                // 正确的时差转速度计算
                double Vp = 1000000.0 / data.DT_us_m; // Vp = 10^6 / DT
                double Vs = 1000000.0 / data.DTS_us_m; // Vs = 10^6 / DTS

                // 验证物理规律：Vp > Vs
                if (Vp <= Vs)
                {
                    Console.WriteLine($"警告：深度{data.Depth}m处 Vp({Vp:F1}) <= Vs({Vs:F1})，违背物理规律！");
                }

                // 计算动态参数
                double Ed = CalculateDynamicYoungsModulusFromVelocity(data.Density_kg_m3 / 1000.0, Vp, Vs);
                double MuD = CalculateDynamicPoissonRatioFromVelocity(Vp, Vs);
                double Es = CalculateStaticYoungsModulus(Ed);
                double MuS = CalculateStaticPoissonRatio(MuD);

                results.Add((data.Depth, Vp, Vs, Ed, MuD, Es, MuS));
                Console.WriteLine($"{data.Depth:F1}\t\t{data.Density_kg_m3:F0}\t\t{data.DT_us_m:F1}\t\t{data.DTS_us_m:F1}\t\t{Vp:F1}\t\t{Vs:F1}\t\t{Ed:F4}\t\t{MuD:F4}\t\t{Es:F2}\t\t{MuS:F4}");
            }

            // 第二步：计算数据集的动态范围
            double esMin = results.Min(r => r.Es);
            double esMax = results.Max(r => r.Es);
            double muSMin = results.Min(r => r.MuS);
            double muSMax = results.Max(r => r.MuS);

            Console.WriteLine();
            Console.WriteLine("第二步：计算数据集动态范围");
            Console.WriteLine($"Es范围：{esMin:F2} - {esMax:F2} GPa");
            Console.WriteLine($"MuS范围：{muSMin:F4} - {muSMax:F4}");
            Console.WriteLine();

            // 第三步：使用动态范围计算脆性指数
            Console.WriteLine("第三步：使用动态范围计算脆性指数");
            Console.WriteLine("深度\t\tVp(m/s)\t\tVs(m/s)\t\tEs(GPa)\t\tMuS\t\tEBRIT(%)\tμBRIT(%)\t脆性指数(%)");
            Console.WriteLine("---------------------------------------------------------------------------------------------------------------");

            foreach (var result in results)
            {
                double brittlenessIndex = CalculateBrittlenessIndex(result.Es, result.MuS, esMin, esMax, muSMin, muSMax);

                // 计算分量用于验证
                double eBrit = (result.Es - esMin) / (esMax - esMin) * 100;
                double muBrit = (muSMax - result.MuS) / (muSMax - muSMin) * 100;

                Console.WriteLine($"{result.Depth:F1}\t\t{result.Vp:F1}\t\t{result.Vs:F1}\t\t{result.Es:F2}\t\t{result.MuS:F4}\t\t{eBrit:F2}\t\t{muBrit:F2}\t\t{brittlenessIndex:F2}");
            }

            Console.WriteLine();
            Console.WriteLine("=== 修复前后对比 ===");
            Console.WriteLine();
            Console.WriteLine("修复前的问题：");
            Console.WriteLine("1. 动态杨氏模量公式错误：Ed = ρ * Vs^2 * (3Vp^2 - 4Vs^2) / ((Vp^2 - Vs^2) * 1000)");
            Console.WriteLine("2. 单位转换错误：未正确处理kg/m³到g/cm³，km/s到m/s的转换");
            Console.WriteLine("3. 使用动态范围计算脆性指数，导致结果不稳定");
            Console.WriteLine();
            Console.WriteLine("修复后的改进：");
            Console.WriteLine("1. 使用正确的文献公式：Ed = 10^-3 × ρ × Vs^2 × (3Vp^2 - 4Vs^2) / (Vp^2 - Vs^2)");
            Console.WriteLine("2. 正确的单位转换：ρ(g/cm³) = ρ(kg/m³) ÷ 1000, V(m/s) = V(km/s) × 1000");
            Console.WriteLine("3. 使用固定范围计算脆性指数：Es(5-80 GPa), MuS(0.1-0.4)");
            Console.WriteLine("4. 数据表结构简化：只显示深度列，去掉顶深和底深");

        }

        /// <summary>
        /// 计算动态杨氏模量 (Ed) - 直接使用速度值（m/s）
        /// </summary>
        static double CalculateDynamicYoungsModulusFromVelocity(double densityGPerCm3, double vpMSec, double vsMSec)
        {
            // 计算公式
            double numerator = 3 * Math.Pow(vpMSec, 2) - 4 * Math.Pow(vsMSec, 2);
            double denominator = Math.Pow(vpMSec, 2) - Math.Pow(vsMSec, 2);

            // 防止除以零
            if (Math.Abs(denominator) < 1e-10)
                return 0;

            return 1e-3 * densityGPerCm3 * Math.Pow(vsMSec, 2) * (numerator / denominator);
        }

        /// <summary>
        /// 计算动态泊松比 (μd) - 直接使用速度值（m/s）
        /// </summary>
        static double CalculateDynamicPoissonRatioFromVelocity(double vpMSec, double vsMSec)
        {
            double vpSquared = Math.Pow(vpMSec, 2);
            double vsSquared = Math.Pow(vsMSec, 2);

            double numerator = vpSquared - 2 * vsSquared;
            double denominator = 2 * (vpSquared - vsSquared);

            // 防止除以零
            if (Math.Abs(denominator) < 1e-10)
                return 0;

            return numerator / denominator;
        }

        /// <summary>
        /// 计算动态杨氏模量 (Ed) - 与公式计算.cs完全一致
        /// </summary>
        static double CalculateDynamicYoungsModulus(double densityKgPerM3, double vpKmPerSec, double vsKmPerSec)
        {
            // 单位转换：kg/m³ → g/cm³ (除以1000)
            double densityGPerCm3 = densityKgPerM3 / 1000.0;

            // 单位转换：km/s → m/s (乘以1000)
            double vpMSec = vpKmPerSec * 1000.0;
            double vsMSec = vsKmPerSec * 1000.0;

            // 计算公式
            double numerator = 3 * Math.Pow(vpMSec, 2) - 4 * Math.Pow(vsMSec, 2);
            double denominator = Math.Pow(vpMSec, 2) - Math.Pow(vsMSec, 2);

            // 防止除以零
            if (Math.Abs(denominator) < 1e-10)
                return 0;

            // 完全按照公式计算.cs的实现（相信这是正确的）
            return 1e-3 * densityGPerCm3 * Math.Pow(vsMSec, 2) * (numerator / denominator);
        }

        /// <summary>
        /// 计算动态泊松比 (μd) - 与公式计算.cs完全一致
        /// </summary>
        static double CalculateDynamicPoissonRatio(double vpKmPerSec, double vsKmPerSec)
        {
            // 单位转换：km/s → m/s (乘以1000)，但泊松比计算中单位会抵消，可不转换
            double vpSquared = Math.Pow(vpKmPerSec, 2);
            double vsSquared = Math.Pow(vsKmPerSec, 2);

            double numerator = vpSquared - 2 * vsSquared;
            double denominator = 2 * (vpSquared - vsSquared);

            // 防止除以零
            if (Math.Abs(denominator) < 1e-10)
                return 0;

            return numerator / denominator;
        }

        /// <summary>
        /// 计算静态杨氏模量 (Es) - 与公式计算.cs完全一致
        /// </summary>
        static double CalculateStaticYoungsModulus(double dynamicYoungsModulusGPa)
        {
            return dynamicYoungsModulusGPa * 0.5823 + 7.566;
        }

        /// <summary>
        /// 计算静态泊松比 (μs) - 与公式计算.cs完全一致
        /// </summary>
        static double CalculateStaticPoissonRatio(double dynamicPoissonRatio)
        {
            return dynamicPoissonRatio * 0.6648 + 0.0514;
        }

        /// <summary>
        /// 计算脆性指数 (BRITe) - 与公式计算.cs完全一致
        /// </summary>
        static double CalculateBrittlenessIndex(
            double staticYoungsModulus,
            double staticPoissonRatio,
            double esMin,
            double esMax,
            double musMin,
            double musMax)
        {
            // 计算归一化杨氏模量脆性指数
            double eBrit;
            if (Math.Abs(esMax - esMin) < 1e-10)
                eBrit = 0;
            else
                eBrit = (staticYoungsModulus - esMin) / (esMax - esMin) * 100;

            // 计算归一化泊松比脆性指数
            double muBrit;
            if (Math.Abs(musMax - musMin) < 1e-10)
                muBrit = 0;
            else
                muBrit = (musMax - staticPoissonRatio) / (musMax - musMin) * 100;

            // 计算综合脆性指数
            return (eBrit + muBrit) / 2;
        }

        /// <summary>
        /// 修复后的脆性指数计算方法（固定范围）
        /// 与real.txt中的计算方法一致
        /// </summary>
        static double CalculateBrittlenessIndexFixed(double Es, double MuS)
        {
            // 使用固定范围
            double EsMin = 5.0;   // GPa
            double EsMax = 80.0;  // GPa
            double MuSMin = 0.1;
            double MuSMax = 0.4;

            // 归一化杨氏模量脆性指数
            double EBRIT = (Es - EsMin) / (EsMax - EsMin) * 100;
            EBRIT = Math.Max(0, Math.Min(100, EBRIT));

            // 归一化泊松比脆性指数
            double PBRIT = (MuSMax - MuS) / (MuSMax - MuSMin) * 100;
            PBRIT = Math.Max(0, Math.Min(100, PBRIT));

            // 综合脆性指数
            return (EBRIT + PBRIT) / 2;
        }

        /// <summary>
        /// 原来的Rickman公式（乘积方式）
        /// </summary>
        static double CalculateBrittlenessIndexRickman(double Es, double MuS)
        {
            double E_min = 10.0;  // GPa
            double E_max = 80.0;  // GPa
            double v_min = 0.15;
            double v_max = 0.35;

            double brittlenessIndex = ((Es - E_min) / (E_max - E_min)) *
                                    ((v_max - MuS) / (v_max - v_min));

            return Math.Max(0, Math.Min(1, brittlenessIndex));
        }

        /// <summary>
        /// 使用动态范围的计算方法（问题方法）
        /// </summary>
        static double CalculateBrittlenessIndexDynamic(double Es, double MuS, double EsMin, double EsMax, double MuSMin, double MuSMax)
        {
            // 归一化杨氏模量脆性指数
            double EBRIT = 0;
            if (EsMax > EsMin)
            {
                EBRIT = (Es - EsMin) / (EsMax - EsMin) * 100;
                EBRIT = Math.Max(0, Math.Min(100, EBRIT));
            }

            // 归一化泊松比脆性指数
            double PBRIT = 0;
            if (MuSMax > MuSMin)
            {
                PBRIT = (MuSMax - MuS) / (MuSMax - MuSMin) * 100;
                PBRIT = Math.Max(0, Math.Min(100, PBRIT));
            }

            // 综合脆性指数
            return (EBRIT + PBRIT) / 2;
        }
    }
}
