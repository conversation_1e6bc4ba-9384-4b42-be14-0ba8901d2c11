﻿using System;
using System.Linq;

namespace BrittlenessIndexTest
{
    /// <summary>
    /// 脆性指数计算测试程序
    /// 用于验证修复后的计算逻辑是否与公式计算.cs一致
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 脆性指数计算测试程序 ===");
            Console.WriteLine("验证修复后的计算逻辑是否与公式计算.cs一致");
            Console.WriteLine();

            // 测试数据 - 使用一些典型的岩石力学参数
            var testData = new[]
            {
                new { Depth = 1000.0, Density_kg_m3 = 2500.0, Vp_km_s = 4.5, Vs_km_s = 2.8, Description = "测试点1" },
                new { Depth = 1000.5, Density_kg_m3 = 2520.0, Vp_km_s = 4.6, Vs_km_s = 2.9, Description = "测试点2" },
                new { Depth = 1001.0, Density_kg_m3 = 2540.0, Vp_km_s = 4.7, Vs_km_s = 3.0, Description = "测试点3" },
                new { Depth = 1001.2, Density_kg_m3 = 2560.0, Vp_km_s = 4.8, Vs_km_s = 3.1, Description = "测试点4" },
                new { Depth = 1002.0, Density_kg_m3 = 2580.0, Vp_km_s = 4.9, Vs_km_s = 3.2, Description = "测试点5" }
            };

            Console.WriteLine("使用与公式计算.cs完全一致的计算流程：");
            Console.WriteLine();

            // 先验证一个具体的计算
            Console.WriteLine("=== 单位分析验证 ===");
            double testDensity = 2500; // kg/m³
            double testVp = 4.5; // km/s
            double testVs = 2.8; // km/s

            Console.WriteLine($"输入：密度={testDensity} kg/m³, Vp={testVp} km/s, Vs={testVs} km/s");

            // 按公式计算.cs的实现
            double Ed1 = CalculateDynamicYoungsModulus(testDensity, testVp, testVs);
            Console.WriteLine($"公式计算.cs实现：Ed = {Ed1:F2} GPa");

            // 按标准弹性力学公式
            double densityKg = testDensity;
            double vpMs = testVp * 1000;
            double vsMs = testVs * 1000;
            double Ed2 = densityKg * vsMs * vsMs * (3 * vpMs * vpMs - 4 * vsMs * vsMs) / (vpMs * vpMs - vsMs * vsMs) / 1e9;
            Console.WriteLine($"标准弹性力学公式：Ed = {Ed2:F2} GPa");

            // 如果公式计算.cs的结果需要额外转换
            double Ed3 = Ed1 / 1e9;
            Console.WriteLine($"公式计算.cs结果/10^9：Ed = {Ed3:F2} GPa");
            Console.WriteLine();

            // 第一步：计算所有数据点的静态参数
            var results = new List<(double Depth, double Ed, double MuD, double Es, double MuS)>();

            Console.WriteLine("第一步：计算动态和静态参数");
            Console.WriteLine("深度\t\t密度(kg/m³)\tVp(km/s)\tVs(km/s)\tEd(GPa)\t\tMuD\t\tEs(GPa)\t\tMuS");
            Console.WriteLine("---------------------------------------------------------------------------------------------");

            foreach (var data in testData)
            {
                // 使用公式计算.cs中的方法计算
                double Ed = CalculateDynamicYoungsModulus(data.Density_kg_m3, data.Vp_km_s, data.Vs_km_s);
                double MuD = CalculateDynamicPoissonRatio(data.Vp_km_s, data.Vs_km_s);
                double Es = CalculateStaticYoungsModulus(Ed);
                double MuS = CalculateStaticPoissonRatio(MuD);

                results.Add((data.Depth, Ed, MuD, Es, MuS));
                Console.WriteLine($"{data.Depth:F1}\t\t{data.Density_kg_m3:F0}\t\t{data.Vp_km_s:F1}\t\t{data.Vs_km_s:F1}\t\t{Ed:F2}\t\t{MuD:F4}\t\t{Es:F2}\t\t{MuS:F4}");
            }

            // 第二步：计算数据集的动态范围
            double esMin = results.Min(r => r.Es);
            double esMax = results.Max(r => r.Es);
            double muSMin = results.Min(r => r.MuS);
            double muSMax = results.Max(r => r.MuS);

            Console.WriteLine();
            Console.WriteLine("第二步：计算数据集动态范围");
            Console.WriteLine($"Es范围：{esMin:F2} - {esMax:F2} GPa");
            Console.WriteLine($"MuS范围：{muSMin:F4} - {muSMax:F4}");
            Console.WriteLine();

            // 第三步：使用动态范围计算脆性指数
            Console.WriteLine("第三步：使用动态范围计算脆性指数");
            Console.WriteLine("深度\t\tEs(GPa)\t\tMuS\t\tEBRIT(%)\tμBRIT(%)\t脆性指数(%)");
            Console.WriteLine("---------------------------------------------------------------------------------------------");

            foreach (var result in results)
            {
                double brittlenessIndex = CalculateBrittlenessIndex(result.Es, result.MuS, esMin, esMax, muSMin, muSMax);

                // 计算分量用于验证
                double eBrit = (result.Es - esMin) / (esMax - esMin) * 100;
                double muBrit = (muSMax - result.MuS) / (muSMax - muSMin) * 100;

                Console.WriteLine($"{result.Depth:F1}\t\t{result.Es:F2}\t\t{result.MuS:F4}\t\t{eBrit:F2}\t\t{muBrit:F2}\t\t{brittlenessIndex:F2}");
            }

            Console.WriteLine();
            Console.WriteLine("=== 修复前后对比 ===");
            Console.WriteLine();
            Console.WriteLine("修复前的问题：");
            Console.WriteLine("1. 动态杨氏模量公式错误：Ed = ρ * Vs^2 * (3Vp^2 - 4Vs^2) / ((Vp^2 - Vs^2) * 1000)");
            Console.WriteLine("2. 单位转换错误：未正确处理kg/m³到g/cm³，km/s到m/s的转换");
            Console.WriteLine("3. 使用动态范围计算脆性指数，导致结果不稳定");
            Console.WriteLine();
            Console.WriteLine("修复后的改进：");
            Console.WriteLine("1. 使用正确的文献公式：Ed = 10^-3 × ρ × Vs^2 × (3Vp^2 - 4Vs^2) / (Vp^2 - Vs^2)");
            Console.WriteLine("2. 正确的单位转换：ρ(g/cm³) = ρ(kg/m³) ÷ 1000, V(m/s) = V(km/s) × 1000");
            Console.WriteLine("3. 使用固定范围计算脆性指数：Es(5-80 GPa), MuS(0.1-0.4)");
            Console.WriteLine("4. 数据表结构简化：只显示深度列，去掉顶深和底深");

        }

        /// <summary>
        /// 计算动态杨氏模量 (Ed) - 与公式计算.cs完全一致
        /// </summary>
        static double CalculateDynamicYoungsModulus(double densityKgPerM3, double vpKmPerSec, double vsKmPerSec)
        {
            // 单位转换：kg/m³ → g/cm³ (除以1000)
            double densityGPerCm3 = densityKgPerM3 / 1000.0;

            // 单位转换：km/s → m/s (乘以1000)
            double vpMSec = vpKmPerSec * 1000.0;
            double vsMSec = vsKmPerSec * 1000.0;

            // 计算公式
            double numerator = 3 * Math.Pow(vpMSec, 2) - 4 * Math.Pow(vsMSec, 2);
            double denominator = Math.Pow(vpMSec, 2) - Math.Pow(vsMSec, 2);

            // 防止除以零
            if (Math.Abs(denominator) < 1e-10)
                return 0;

            // 完全按照公式计算.cs的实现（相信这是正确的）
            return 1e-3 * densityGPerCm3 * Math.Pow(vsMSec, 2) * (numerator / denominator);
        }

        /// <summary>
        /// 计算动态泊松比 (μd) - 与公式计算.cs完全一致
        /// </summary>
        static double CalculateDynamicPoissonRatio(double vpKmPerSec, double vsKmPerSec)
        {
            // 单位转换：km/s → m/s (乘以1000)，但泊松比计算中单位会抵消，可不转换
            double vpSquared = Math.Pow(vpKmPerSec, 2);
            double vsSquared = Math.Pow(vsKmPerSec, 2);

            double numerator = vpSquared - 2 * vsSquared;
            double denominator = 2 * (vpSquared - vsSquared);

            // 防止除以零
            if (Math.Abs(denominator) < 1e-10)
                return 0;

            return numerator / denominator;
        }

        /// <summary>
        /// 计算静态杨氏模量 (Es) - 与公式计算.cs完全一致
        /// </summary>
        static double CalculateStaticYoungsModulus(double dynamicYoungsModulusGPa)
        {
            return dynamicYoungsModulusGPa * 0.5823 + 7.566;
        }

        /// <summary>
        /// 计算静态泊松比 (μs) - 与公式计算.cs完全一致
        /// </summary>
        static double CalculateStaticPoissonRatio(double dynamicPoissonRatio)
        {
            return dynamicPoissonRatio * 0.6648 + 0.0514;
        }

        /// <summary>
        /// 计算脆性指数 (BRITe) - 与公式计算.cs完全一致
        /// </summary>
        static double CalculateBrittlenessIndex(
            double staticYoungsModulus,
            double staticPoissonRatio,
            double esMin,
            double esMax,
            double musMin,
            double musMax)
        {
            // 计算归一化杨氏模量脆性指数
            double eBrit;
            if (Math.Abs(esMax - esMin) < 1e-10)
                eBrit = 0;
            else
                eBrit = (staticYoungsModulus - esMin) / (esMax - esMin) * 100;

            // 计算归一化泊松比脆性指数
            double muBrit;
            if (Math.Abs(musMax - musMin) < 1e-10)
                muBrit = 0;
            else
                muBrit = (musMax - staticPoissonRatio) / (musMax - musMin) * 100;

            // 计算综合脆性指数
            return (eBrit + muBrit) / 2;
        }

        /// <summary>
        /// 修复后的脆性指数计算方法（固定范围）
        /// 与real.txt中的计算方法一致
        /// </summary>
        static double CalculateBrittlenessIndexFixed(double Es, double MuS)
        {
            // 使用固定范围
            double EsMin = 5.0;   // GPa
            double EsMax = 80.0;  // GPa
            double MuSMin = 0.1;
            double MuSMax = 0.4;

            // 归一化杨氏模量脆性指数
            double EBRIT = (Es - EsMin) / (EsMax - EsMin) * 100;
            EBRIT = Math.Max(0, Math.Min(100, EBRIT));

            // 归一化泊松比脆性指数
            double PBRIT = (MuSMax - MuS) / (MuSMax - MuSMin) * 100;
            PBRIT = Math.Max(0, Math.Min(100, PBRIT));

            // 综合脆性指数
            return (EBRIT + PBRIT) / 2;
        }

        /// <summary>
        /// 原来的Rickman公式（乘积方式）
        /// </summary>
        static double CalculateBrittlenessIndexRickman(double Es, double MuS)
        {
            double E_min = 10.0;  // GPa
            double E_max = 80.0;  // GPa
            double v_min = 0.15;
            double v_max = 0.35;

            double brittlenessIndex = ((Es - E_min) / (E_max - E_min)) *
                                    ((v_max - MuS) / (v_max - v_min));

            return Math.Max(0, Math.Min(1, brittlenessIndex));
        }

        /// <summary>
        /// 使用动态范围的计算方法（问题方法）
        /// </summary>
        static double CalculateBrittlenessIndexDynamic(double Es, double MuS, double EsMin, double EsMax, double MuSMin, double MuSMax)
        {
            // 归一化杨氏模量脆性指数
            double EBRIT = 0;
            if (EsMax > EsMin)
            {
                EBRIT = (Es - EsMin) / (EsMax - EsMin) * 100;
                EBRIT = Math.Max(0, Math.Min(100, EBRIT));
            }

            // 归一化泊松比脆性指数
            double PBRIT = 0;
            if (MuSMax > MuSMin)
            {
                PBRIT = (MuSMax - MuS) / (MuSMax - MuSMin) * 100;
                PBRIT = Math.Max(0, Math.Min(100, PBRIT));
            }

            // 综合脆性指数
            return (EBRIT + PBRIT) / 2;
        }
    }
}
