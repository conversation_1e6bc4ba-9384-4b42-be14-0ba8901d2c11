using System;
using EnhancedStaticRockMechanicsAnalysisSystem.Models;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Core
{
    /// <summary>
    /// 岩石力学参数计算器
    /// </summary>
    public class RockMechanicsCalculator
    {
        /// <summary>
        /// 计算脆性指数和相关参数
        /// </summary>
        /// <param name="density">密度 (g/cm³)</param>
        /// <param name="vpVelocity">纵波速度 (m/s)</param>
        /// <param name="vsVelocity">横波速度 (m/s)</param>
        /// <returns>计算结果</returns>
        public CalculationResult CalculateBrittleness(double density, double vpVelocity, double vsVelocity)
        {
            try
            {
                // 验证输入参数
                if (density <= 0)
                    return CalculationResult.CreateFailure("密度必须大于0");

                if (vpVelocity <= 0)
                    return CalculationResult.CreateFailure("纵波速度必须大于0");

                if (vsVelocity <= 0)
                    return CalculationResult.CreateFailure("横波速度必须大于0");

                if (vpVelocity <= vsVelocity)
                    return CalculationResult.CreateFailure("纵波速度必须大于横波速度");

                var result = new CalculationResult();

                // 记录输入参数
                result.AddInputParameter("密度", density);
                result.AddInputParameter("纵波速度", vpVelocity);
                result.AddInputParameter("横波速度", vsVelocity);

                // 转换密度单位：g/cm³ -> kg/m³
                double densityKgM3 = density * 1000;
                result.AddCalculationStep($"密度转换: {density} g/cm³ = {densityKgM3} kg/m³");

                // 计算动态弹性模量
                // 剪切模量 G = ρ * Vs²
                double shearModulus = densityKgM3 * Math.Pow(vsVelocity, 2) / 1e9; // 转换为GPa
                result.ShearModulus = shearModulus;
                result.AddCalculationStep($"剪切模量 G = ρ × Vs² = {densityKgM3} × {vsVelocity}² = {shearModulus:F2} GPa");

                // 体积模量 K = ρ * (Vp² - 4/3 * Vs²)
                double bulkModulus = densityKgM3 * (Math.Pow(vpVelocity, 2) - (4.0 / 3.0) * Math.Pow(vsVelocity, 2)) / 1e9; // 转换为GPa
                result.BulkModulus = bulkModulus;
                result.AddCalculationStep($"体积模量 K = ρ × (Vp² - 4/3×Vs²) = {bulkModulus:F2} GPa");

                // 杨氏模量 E = 9KG/(3K+G)
                double youngModulus = (9 * bulkModulus * shearModulus) / (3 * bulkModulus + shearModulus);
                result.YoungModulus = youngModulus;
                result.AddCalculationStep($"杨氏模量 E = 9KG/(3K+G) = {youngModulus:F2} GPa");

                // 泊松比 ν = (3K-2G)/(6K+2G)
                double poissonRatio = (3 * bulkModulus - 2 * shearModulus) / (6 * bulkModulus + 2 * shearModulus);
                result.PoissonRatio = poissonRatio;
                result.AddCalculationStep($"泊松比 ν = (3K-2G)/(6K+2G) = {poissonRatio:F3}");

                // 脆性指数计算 - 修正为与其他系统一致的计算方法
                // 使用与real.txt中相同的参数范围和计算公式
                double E_min = 5.0;   // GPa
                double E_max = 80.0;  // GPa
                double v_min = 0.1;
                double v_max = 0.4;

                // 归一化杨氏模量脆性指数
                // EBRIT = (Es - Emin) / (Emax - Emin) × 100%
                double EBRIT = (youngModulus - E_min) / (E_max - E_min) * 100;
                EBRIT = Math.Max(0, Math.Min(100, EBRIT)); // 限制在0-100%范围内

                // 归一化泊松比脆性指数
                // PBRIT = (μmax - μs) / (μmax - μmin) × 100%
                double PBRIT = (v_max - poissonRatio) / (v_max - v_min) * 100;
                PBRIT = Math.Max(0, Math.Min(100, PBRIT)); // 限制在0-100%范围内

                // 综合脆性指数
                // BRIT = (EBRIT + PBRIT) / 2
                double brittlenessIndex = (EBRIT + PBRIT) / 2;

                result.BrittlenessIndex = brittlenessIndex;
                result.AddCalculationStep($"归一化杨氏模量脆性指数 EBRIT = {EBRIT:F2}%");
                result.AddCalculationStep($"归一化泊松比脆性指数 PBRIT = {PBRIT:F2}%");
                result.AddCalculationStep($"综合脆性指数 BI = {brittlenessIndex:F2}%");

                // 验证结果
                if (!result.IsValidResult())
                {
                    return CalculationResult.CreateFailure("计算结果超出合理范围");
                }

                result.AddCalculationStep("计算完成");
                return result;
            }
            catch (Exception ex)
            {
                return CalculationResult.CreateFailure($"计算过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算脆性指数和相关参数（使用RockMechanicsDataPoint）
        /// </summary>
        /// <param name="dataPoint">岩石力学数据点</param>
        /// <returns>计算结果</returns>
        public CalculationResult CalculateBrittlenessIndex(RockMechanicsDataPoint dataPoint)
        {
            return CalculateBrittleness(dataPoint.Density, dataPoint.VpVelocity, dataPoint.VsVelocity);
        }

        /// <summary>
        /// 计算Vp/Vs比值
        /// </summary>
        /// <param name="vpVelocity">纵波速度</param>
        /// <param name="vsVelocity">横波速度</param>
        /// <returns>Vp/Vs比值</returns>
        public double CalculateVpVsRatio(double vpVelocity, double vsVelocity)
        {
            if (vsVelocity == 0)
                throw new ArgumentException("横波速度不能为0");

            return vpVelocity / vsVelocity;
        }

        /// <summary>
        /// 根据脆性指数评估岩石类型
        /// </summary>
        /// <param name="brittlenessIndex">脆性指数</param>
        /// <returns>岩石类型描述</returns>
        public string EvaluateRockType(double brittlenessIndex)
        {
            if (brittlenessIndex >= 0.8)
                return "极脆性岩石";
            else if (brittlenessIndex >= 0.6)
                return "高脆性岩石";
            else if (brittlenessIndex >= 0.4)
                return "中等脆性岩石";
            else if (brittlenessIndex >= 0.2)
                return "低脆性岩石";
            else
                return "韧性岩石";
        }

        /// <summary>
        /// 验证输入参数的合理性
        /// </summary>
        /// <param name="density">密度</param>
        /// <param name="vpVelocity">纵波速度</param>
        /// <param name="vsVelocity">横波速度</param>
        /// <returns>验证结果</returns>
        public (bool IsValid, string ErrorMessage) ValidateInputParameters(double density, double vpVelocity, double vsVelocity)
        {
            if (density < 1.5 || density > 5.0)
                return (false, "密度应在1.5-5.0 g/cm³范围内");

            if (vpVelocity < 1000 || vpVelocity > 8000)
                return (false, "纵波速度应在1000-8000 m/s范围内");

            if (vsVelocity < 500 || vsVelocity > 5000)
                return (false, "横波速度应在500-5000 m/s范围内");

            if (vpVelocity / vsVelocity < 1.4 || vpVelocity / vsVelocity > 2.5)
                return (false, "Vp/Vs比值应在1.4-2.5范围内");

            return (true, "参数验证通过");
        }
    }
}
