using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using EnhancedStaticRockMechanicsAnalysisSystem.Core;
using EnhancedStaticRockMechanicsAnalysisSystem.Models;
using EnhancedStaticRockMechanicsAnalysisSystem.Services;
using Newtonsoft.Json;
using NPOI.HSSF.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Forms
{
    public partial class StaticRockMechanicsForm : Form
    {
        private DataTable mechanicsData;
        private List<RockMechanicsDataPoint> dataPoints;
        private RockMechanicsCalculator calculator;
        private DataTable? originalMechanicsData;
        private string? currentExcelFile;
        private Chart? chartBrittleness; // 图表控件 - 完全参考原系统

        // 自适应缩放相关字段
        private readonly Size originalFormSize = new Size(1400, 900);
        private readonly Dictionary<Control, Rectangle> originalControlBounds = new Dictionary<Control, Rectangle>();
        private readonly Dictionary<Control, Font> originalControlFonts = new Dictionary<Control, Font>();

        // 对比数据管理器 - 暂时注释掉，使用简化版本
        // private UnifiedComparisonDataManager comparisonDataManager;

        // 列名识别字典 - 完全参考原系统BritSystem
        private readonly Dictionary<string, List<string>> columnPatterns = new Dictionary<string, List<string>>
        {
            ["深度"] = new List<string> { "深度", "depth", "depths", "顶深", "井深", "md", "tvd", "顶深/m", "深度/m", "深度(m)" },
            ["密度"] = new List<string> { "密度", "ρ", "rho", "rhob", "density", "den", "密度/(g/cm³)", "密度(g/cm³)", "岩石密度" },
            ["纵波速度"] = new List<string> { "纵波速度", "vp", "纵波", "p波", "dt", "纵波时差", "纵波速度/(m/s)", "纵波速度(m/s)", "vp(m/s)" },
            ["横波速度"] = new List<string> { "横波速度", "vs", "横波", "s波", "dts", "横波时差", "横波速度/(m/s)", "横波速度(m/s)", "vs(m/s)" }
        };

        public StaticRockMechanicsForm()
        {
            InitializeComponent();
            InitializeData();
            // InitializeScaling(); // 暂时禁用自适应缩放，使用设计器布局
            // InitializeComparisonManager(); // 暂时注释掉
            BindUnitSelectionEvents();
            UpdateParameterLabels();

            // 绑定Load事件来确保图表在窗体完全加载后初始化
            this.Load += StaticRockMechanicsForm_Load;
        }

        private void StaticRockMechanicsForm_Load(object sender, EventArgs e)
        {
            // 在窗体加载完成后初始化图表
            InitializeChart();
        }

        private void InitializeData()
        {
            mechanicsData = new DataTable();
            mechanicsData.Columns.Add("深度/m", typeof(double)); // 修改为只使用深度列
            mechanicsData.Columns.Add("密度/(g/cm³)", typeof(double));
            mechanicsData.Columns.Add("纵波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("横波速度/(m/s)", typeof(double));
            mechanicsData.Columns.Add("动态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("动态泊松比", typeof(double));
            mechanicsData.Columns.Add("静态杨氏模量/GPa", typeof(double));
            mechanicsData.Columns.Add("静态泊松比", typeof(double));
            mechanicsData.Columns.Add("脆性指数/%", typeof(double));

            dataPoints = new List<RockMechanicsDataPoint>();
            calculator = new RockMechanicsCalculator();

            // 绑定数据表到DataGridView
            if (dgvMechanicsData != null)
            {
                dgvMechanicsData.DataSource = mechanicsData;
                dgvMechanicsData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            }
        }

        private void InitializeChart()
        {
            try
            {
                // 创建图表控件 - 完全参考原系统BritSystem
                chartBrittleness = new Chart();
                chartBrittleness.Dock = DockStyle.Fill;
                chartBrittleness.BackColor = Color.FromArgb(60, 60, 60);
                chartBrittleness.ForeColor = Color.White;

                // 创建图表区域
                ChartArea chartArea = new ChartArea("MainArea");
                chartArea.BackColor = Color.FromArgb(60, 60, 60);
                chartArea.AxisX.LabelStyle.ForeColor = Color.White;
                chartArea.AxisY.LabelStyle.ForeColor = Color.White;
                chartArea.AxisX.LineColor = Color.White;
                chartArea.AxisY.LineColor = Color.White;
                chartArea.AxisX.MajorGrid.LineColor = Color.Gray;
                chartArea.AxisY.MajorGrid.LineColor = Color.Gray;
                chartArea.AxisX.Title = "脆性指数 (%)";
                chartArea.AxisY.Title = "深度 (m)";
                chartArea.AxisX.TitleForeColor = Color.White;
                chartArea.AxisY.TitleForeColor = Color.White;

                // Y轴反向显示（深度从上到下）
                chartArea.AxisY.IsReversed = true;

                chartBrittleness.ChartAreas.Add(chartArea);

                // 创建数据系列
                Series pointSeries = new Series("数据点");
                pointSeries.ChartType = SeriesChartType.Point;
                pointSeries.Color = Color.Red;
                pointSeries.MarkerStyle = MarkerStyle.Circle;
                pointSeries.MarkerSize = 8;
                chartBrittleness.Series.Add(pointSeries);

                Series lineSeries = new Series("连线");
                lineSeries.ChartType = SeriesChartType.Line;
                lineSeries.Color = Color.Blue;
                lineSeries.BorderWidth = 2;
                chartBrittleness.Series.Add(lineSeries);

                // 添加图表到pnlChart面板
                if (pnlChart != null)
                {
                    pnlChart.Controls.Add(chartBrittleness);
                    chartBrittleness.BringToFront();
                }

                LoggingService.Instance.Info("图表控件初始化完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"初始化图表控件失败: {ex.Message}");
            }
        }

        // 暂时注释掉，使用简化版本
        // private void InitializeComparisonManager()
        // {
        //     try
        //     {
        //         comparisonDataManager = new UnifiedComparisonDataManager();
        //         LoggingService.Instance.Info("对比数据管理器初始化成功");
        //     }
        //     catch (Exception ex)
        //     {
        //         LoggingService.Instance.Error($"对比数据管理器初始化失败: {ex.Message}");
        //     }
        // }

        private void BindUnitSelectionEvents()
        {
            try
            {
                // 设置默认选择：ρ、DT、DTS
                rbDensityRho.Checked = true;
                rbVelocityDt.Checked = true;
                rbVelocityDts.Checked = true;

                // 绑定密度单位选择事件
                rbDensityRho.CheckedChanged += UnitSelection_CheckedChanged;
                rbDensityRhob.CheckedChanged += UnitSelection_CheckedChanged;

                // 绑定纵波速度单位选择事件
                rbVelocityDt.CheckedChanged += UnitSelection_CheckedChanged;
                rbVelocityVp.CheckedChanged += UnitSelection_CheckedChanged;

                // 绑定横波速度单位选择事件
                rbVelocityDts.CheckedChanged += UnitSelection_CheckedChanged;
                rbVelocityVs.CheckedChanged += UnitSelection_CheckedChanged;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"绑定单位选择事件失败: {ex.Message}");
            }
        }

        private void UnitSelection_CheckedChanged(object sender, EventArgs e)
        {
            if (sender is RadioButton rb && rb.Checked)
            {
                UpdateParameterLabels();
            }
        }

        private void UpdateParameterLabels()
        {
            try
            {
                // 更新密度标签
                if (rbDensityRho.Checked)
                {
                    lblDensity.Text = "密度 ρ (g/cm³):";
                }
                else if (rbDensityRhob.Checked)
                {
                    lblDensity.Text = "密度 RHOB (g/cm³):";
                }

                // 更新纵波速度标签
                if (rbVelocityDt.Checked)
                {
                    lblVp.Text = "纵波时差 DT (μs/m):";
                }
                else if (rbVelocityVp.Checked)
                {
                    lblVp.Text = "纵波速度 Vp (m/s):";
                }

                // 更新横波速度标签
                if (rbVelocityDts.Checked)
                {
                    lblVs.Text = "横波时差 DTS (μs/m):";
                }
                else if (rbVelocityVs.Checked)
                {
                    lblVs.Text = "横波速度 Vs (m/s):";
                }
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"更新参数标签失败: {ex.Message}");
            }
        }

        private void btnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有导入的数据需要批量计算 - 完全参考原系统
                if (mechanicsData != null && mechanicsData.Rows.Count > 0)
                {
                    // 批量计算模式
                    CalculateBatchData();
                }
                else
                {
                    // 单个计算模式
                    CalculateSingleData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"计算脆性指数失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 单个数据计算 - 完全参考原系统
        /// </summary>
        private void CalculateSingleData()
        {
            // 获取输入参数 - 完全参考原系统
            if (!double.TryParse(txtDensity.Text, out double inputDensity) ||
                !double.TryParse(txtVp.Text, out double inputVp) ||
                !double.TryParse(txtVs.Text, out double inputVs))
            {
                MessageBox.Show("请输入有效的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 进行单位转换，统一为标准单位
            double density = ConvertDensityToStandard(inputDensity);
            double vp = ConvertVelocityToStandard(inputVp, true); // true表示纵波
            double vs = ConvertVelocityToStandard(inputVs, false); // false表示横波

            // 根据修正后的公式计算静态岩石力学参数 - 完全参考原系统
            var result = CalculateStaticRockMechanics(density, vp, vs);

            // 对于单个计算，使用固定范围计算脆性指数 - 完全参考原系统
            double EsMin = 5.0;   // GPa
            double EsMax = 80.0;  // GPa
            double MuSMin = 0.1;
            double MuSMax = 0.4;
            result.BrittlenessIndex = CalculateBrittlenessIndex(result.Es, result.MuS, EsMin, EsMax, MuSMin, MuSMax);

            // 显示计算结果 - 完全参考原系统
            lblCalculationResult.Text = $"Ed={result.Ed:F3}GPa, μd={result.MuD:F4}, Es={result.Es:F3}GPa, μs={result.MuS:F4}, BRIT={result.BrittlenessIndex:F2}%";

            // 添加到数据表 - 完全参考原系统
            DataRow newRow = mechanicsData.NewRow();
            newRow["深度/m"] = 0.0; // 默认值，用户可以在导入数据时修改
            newRow["密度/(g/cm³)"] = density;
            newRow["纵波速度/(m/s)"] = vp;
            newRow["横波速度/(m/s)"] = vs;
            newRow["动态杨氏模量/GPa"] = result.Ed;
            newRow["动态泊松比"] = result.MuD;
            newRow["静态杨氏模量/GPa"] = result.Es;
            newRow["静态泊松比"] = result.MuS;
            newRow["脆性指数/%"] = result.BrittlenessIndex;

            mechanicsData.Rows.Add(newRow);

            LoggingService.Instance.Info($"计算脆性指数: 密度={density}, Vp={vp}, Vs={vs}, 结果={result.BrittlenessIndex:F3}");
        }

        private void UpdateChart()
        {
            try
            {
                if (chartBrittleness == null || mechanicsData == null || mechanicsData.Rows.Count == 0)
                    return;

                // 清除现有数据
                chartBrittleness.Series[0].Points.Clear();
                chartBrittleness.Series[1].Points.Clear();

                // 智能识别列名 - 完全参考原系统
                string? depthColumnName = FindColumnByPattern(mechanicsData, "深度");
                string brittlenessColumnName = "脆性指数/%";

                // 如果没有找到深度列，使用深度列
                if (string.IsNullOrEmpty(depthColumnName))
                {
                    depthColumnName = "深度/m";
                }

                // 检查脆性指数列是否存在
                if (!mechanicsData.Columns.Contains(brittlenessColumnName))
                {
                    LoggingService.Instance.Warning("未找到脆性指数列，无法更新图表");
                    return;
                }

                // 添加数据点
                foreach (DataRow row in mechanicsData.Rows)
                {
                    try
                    {
                        double depth = Convert.ToDouble(row[depthColumnName]);
                        double brittleness = Convert.ToDouble(row[brittlenessColumnName]);

                        // 添加到散点系列
                        int pointIndex = chartBrittleness.Series[0].Points.AddXY(brittleness, depth);
                        chartBrittleness.Series[0].Points[pointIndex].ToolTip =
                            $"深度: {depth}m, 脆性指数: {brittleness:F2}%";

                        // 添加到连线系列
                        chartBrittleness.Series[1].Points.AddXY(brittleness, depth);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Warning($"处理数据行时出错: {ex.Message}");
                    }
                }

                // 调整轴范围
                if (mechanicsData.Rows.Count > 0)
                {
                    var depths = mechanicsData.AsEnumerable()
                        .Select(r => Convert.ToDouble(r[depthColumnName]))
                        .Where(d => !double.IsNaN(d) && !double.IsInfinity(d))
                        .ToList();

                    if (depths.Count > 0)
                    {
                        double minDepth = depths.Min() - 5;
                        double maxDepth = depths.Max() + 5;
                        chartBrittleness.ChartAreas[0].AxisY.Minimum = minDepth;
                        chartBrittleness.ChartAreas[0].AxisY.Maximum = maxDepth;
                    }
                }

                LoggingService.Instance.Info($"图表更新完成，显示 {mechanicsData.Rows.Count} 个数据点");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"更新图表失败: {ex.Message}");
            }
        }

        private void ClearInputs()
        {
            txtDensity.Clear();
            txtVp.Clear();
            txtVs.Clear();
            txtDensity.Focus();
        }



        private void btnImportData_Click(object sender, EventArgs e)
        {
            using (var ofd = new OpenFileDialog { Filter = "Excel文件|*.xls;*.xlsx" })
            {
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        currentExcelFile = ofd.FileName;
                        var ds = ReadExcelSheets(currentExcelFile);
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            var importedData = ds.Tables[0];

                            // 智能识别列名并映射到标准格式 - 完全参考原系统
                            var mappedData = MapImportedDataToStandardFormat(importedData);

                            if (mappedData != null)
                            {
                                mechanicsData = mappedData;
                                originalMechanicsData = mechanicsData.Copy();
                                dgvMechanicsData.DataSource = mechanicsData;

                                MessageBox.Show($"文件加载成功！共导入 {mechanicsData.Rows.Count} 行数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                LoggingService.Instance.Info($"Excel文件导入成功: {currentExcelFile}，共 {mechanicsData.Rows.Count} 行数据");
                            }
                            else
                            {
                                MessageBox.Show("未能识别Excel文件中的数据列，请检查文件格式！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        else
                        {
                            MessageBox.Show("文件内容为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"文件读取失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        LoggingService.Instance.Error($"Excel文件导入失败: {ex.Message}");
                    }
                }
            }
        }

        private void btnExportData_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以导出！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel文件|*.xlsx";
                saveFileDialog.Title = "导出数据到Excel文件";
                saveFileDialog.FileName = $"静态岩石力学参数数据_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    ExportExcelFile(saveFileDialog.FileName);
                    MessageBox.Show("数据导出成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void btnClearData_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据需要清空。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show("确定要清空所有数据吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    mechanicsData.Clear();
                    dataPoints.Clear();
                    dgvMechanicsData.DataSource = null;
                    dgvMechanicsData.DataSource = mechanicsData;

                    MessageBox.Show("数据已清空。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    LoggingService.Instance.Info("清空所有计算数据");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清空数据失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"清空数据失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 生成曲线按钮点击事件 - 完全参考原系统BritSystem
        /// </summary>
        private void btnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("请先导入数据或进行计算！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 检查是否有脆性指数数据
                if (!mechanicsData.Columns.Contains("脆性指数/%"))
                {
                    MessageBox.Show("请先计算脆性指数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 更新图表
                UpdateChart();

                MessageBox.Show("脆性指数曲线已生成！", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LoggingService.Instance.Info("脆性指数曲线生成成功");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成曲线时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"生成曲线失败: {ex.Message}");
            }
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void StaticRockMechanicsForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (mechanicsData.Rows.Count > 0)
            {
                DialogResult result = MessageBox.Show("当前有未保存的数据，确定要关闭吗？", "确认关闭",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                    return;
                }
            }

            LoggingService.Instance.Info("关闭静态岩石力学参数分析窗体");
        }

        // 添加缺失的事件处理方法
        private void btnLogout_Click(object sender, EventArgs e)
        {
            DialogResult result = MessageBox.Show("确定要退出登录吗？", "确认退出",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void btnEmergencyExit_Click(object sender, EventArgs e)
        {
            Application.Exit();
        }

        private void btnViewComparison_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("开始查看对比图");

                // 使用简化版本的对比图功能
                ShowSimpleComparisonChart();
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"查看对比图失败: {ex.Message}");
                MessageBox.Show($"查看对比图失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowSimpleComparisonChart()
        {
            try
            {
                // 检查临时文件是否存在
                string mineralDataPath = Path.Combine(Path.GetTempPath(), "BritSystem_MineralogicalData.json");
                string staticDataPath = Path.Combine(Path.GetTempPath(), "BritSystem_StaticRockMechanicsData.json");

                bool hasMineralData = File.Exists(mineralDataPath);
                bool hasStaticData = File.Exists(staticDataPath);

                if (!hasMineralData && !hasStaticData)
                {
                    // 如果没有找到数据，先保存当前数据
                    if (dataPoints.Count > 0)
                    {
                        SaveChartDataForComparison();
                        hasStaticData = true;
                    }
                    else
                    {
                        MessageBox.Show("没有找到对比数据！请先计算一些数据点，或在其他系统中保存图表数据。",
                            "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                }

                // 创建简化的对比图窗体
                var comparisonForm = new SimpleComparisonChartForm();
                comparisonForm.LoadComparisonData(mineralDataPath, staticDataPath, hasMineralData, hasStaticData);
                comparisonForm.ShowDialog();

                LoggingService.Instance.Info("对比图显示完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"显示对比图失败: {ex.Message}");
                MessageBox.Show($"显示对比图失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnEnhancedAnalysis_Click(object sender, EventArgs e)
        {
            try
            {
                LoggingService.Instance.Info("启动增强分析算法功能");

                if (dataPoints.Count == 0)
                {
                    MessageBox.Show("请先计算一些数据点，然后再进行增强分析。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 执行增强分析
                var analysisResult = await PerformEnhancedAnalysis();

                // 显示分析结果
                var resultForm = new Form()
                {
                    Text = "增强分析结果",
                    Size = new Size(600, 500),
                    StartPosition = FormStartPosition.CenterParent,
                    BackColor = Color.FromArgb(33, 33, 33),
                    ForeColor = Color.White
                };

                var textBox = new TextBox()
                {
                    Multiline = true,
                    ScrollBars = ScrollBars.Vertical,
                    Dock = DockStyle.Fill,
                    BackColor = Color.FromArgb(60, 60, 60),
                    ForeColor = Color.White,
                    Font = new Font("Microsoft YaHei UI", 10),
                    Text = analysisResult,
                    ReadOnly = true
                };

                resultForm.Controls.Add(textBox);
                resultForm.ShowDialog();

                LoggingService.Instance.Info("增强分析算法执行完成");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动增强分析功能失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"启动增强分析功能失败: {ex.Message}");
            }
        }

        private async Task<string> PerformEnhancedAnalysis()
        {
            return await Task.Run(() =>
            {
                var result = new System.Text.StringBuilder();
                result.AppendLine("=== 增强版静态岩石力学参数分析报告 ===\n");

                // 基础统计分析
                result.AppendLine("【基础统计分析】");
                result.AppendLine($"数据点总数: {dataPoints.Count}");
                result.AppendLine($"脆性指数范围: {dataPoints.Min(p => p.BrittlenessIndex):F2} - {dataPoints.Max(p => p.BrittlenessIndex):F2}");
                result.AppendLine($"脆性指数平均值: {dataPoints.Average(p => p.BrittlenessIndex):F2}");
                result.AppendLine($"杨氏模量范围: {dataPoints.Min(p => p.YoungModulus):F2} - {dataPoints.Max(p => p.YoungModulus):F2} GPa");
                result.AppendLine($"泊松比范围: {dataPoints.Min(p => p.PoissonRatio):F3} - {dataPoints.Max(p => p.PoissonRatio):F3}");
                result.AppendLine();

                // 岩石分类分析
                result.AppendLine("【岩石脆性分类分析】");
                var brittleCount = dataPoints.Count(p => p.BrittlenessIndex > 60);
                var moderateCount = dataPoints.Count(p => p.BrittlenessIndex >= 40 && p.BrittlenessIndex <= 60);
                var ductileCount = dataPoints.Count(p => p.BrittlenessIndex < 40);

                result.AppendLine($"高脆性岩石 (BI > 60): {brittleCount} 个 ({(double)brittleCount / dataPoints.Count * 100:F1}%)");
                result.AppendLine($"中等脆性岩石 (40 ≤ BI ≤ 60): {moderateCount} 个 ({(double)moderateCount / dataPoints.Count * 100:F1}%)");
                result.AppendLine($"低脆性岩石 (BI < 40): {ductileCount} 个 ({(double)ductileCount / dataPoints.Count * 100:F1}%)");
                result.AppendLine();

                // 相关性分析
                result.AppendLine("【参数相关性分析】");
                var densityBICorr = CalculateCorrelation(dataPoints.Select(p => p.Density).ToArray(),
                    dataPoints.Select(p => p.BrittlenessIndex).ToArray());
                var vpBICorr = CalculateCorrelation(dataPoints.Select(p => p.VpVelocity).ToArray(),
                    dataPoints.Select(p => p.BrittlenessIndex).ToArray());
                var vsBICorr = CalculateCorrelation(dataPoints.Select(p => p.VsVelocity).ToArray(),
                    dataPoints.Select(p => p.BrittlenessIndex).ToArray());

                result.AppendLine($"密度与脆性指数相关系数: {densityBICorr:F3}");
                result.AppendLine($"纵波速度与脆性指数相关系数: {vpBICorr:F3}");
                result.AppendLine($"横波速度与脆性指数相关系数: {vsBICorr:F3}");
                result.AppendLine();

                // 建议和结论
                result.AppendLine("【分析建议】");
                if (brittleCount > dataPoints.Count * 0.6)
                {
                    result.AppendLine("• 该区域岩石整体表现为高脆性特征，适合压裂改造");
                }
                else if (ductileCount > dataPoints.Count * 0.6)
                {
                    result.AppendLine("• 该区域岩石整体表现为低脆性特征，压裂改造效果可能有限");
                }
                else
                {
                    result.AppendLine("• 该区域岩石脆性特征分布较为均匀，建议分层分段压裂");
                }

                result.AppendLine($"• 分析完成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");

                return result.ToString();
            });
        }

        private double CalculateCorrelation(double[] x, double[] y)
        {
            if (x.Length != y.Length || x.Length == 0)
                return 0;

            double meanX = x.Average();
            double meanY = y.Average();

            double numerator = x.Zip(y, (xi, yi) => (xi - meanX) * (yi - meanY)).Sum();
            double denominatorX = Math.Sqrt(x.Sum(xi => Math.Pow(xi - meanX, 2)));
            double denominatorY = Math.Sqrt(y.Sum(yi => Math.Pow(yi - meanY, 2)));

            if (denominatorX == 0 || denominatorY == 0)
                return 0;

            return numerator / (denominatorX * denominatorY);
        }

        /// <summary>
        /// 保存图表数据用于对比
        /// </summary>
        private void SaveChartDataForComparison()
        {
            try
            {
                // 从数据点列表中提取数据
                var chartDataPoints = new List<object>();

                foreach (var dataPoint in dataPoints)
                {
                    chartDataPoints.Add(new
                    {
                        TopDepth = dataPoint.Density * 1000, // 使用密度作为深度的替代
                        BottomDepth = dataPoint.Density * 1000,
                        BrittleIndex = dataPoint.BrittlenessIndex
                    });
                }

                // 创建对比数据结构
                var comparisonData = new
                {
                    SystemName = "静态岩石力学参数法",
                    DataPoints = chartDataPoints,
                    SaveTime = DateTime.Now,
                    DataCount = chartDataPoints.Count
                };

                // 保存到临时文件
                string filePath = Path.Combine(Path.GetTempPath(), "BritSystem_StaticRockMechanicsData.json");
                string jsonData = JsonConvert.SerializeObject(comparisonData, Formatting.Indented);
                File.WriteAllText(filePath, jsonData);

                LoggingService.Instance.Info($"对比数据已保存到: {filePath}，共 {chartDataPoints.Count} 个数据点");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"保存对比数据失败: {ex.Message}");
                throw;
            }
        }

        #region 自适应缩放功能

        /// <summary>
        /// 初始化缩放功能，记录原始控件位置和字体
        /// </summary>
        private void InitializeScaling()
        {
            try
            {
                // 记录所有控件的原始位置和字体
                RecordOriginalBounds(this);

                // 绑定窗体大小改变事件
                this.Resize += StaticRockMechanicsForm_Resize;

                LoggingService.Instance.Info("自适应缩放功能初始化完成");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"初始化自适应缩放功能失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 递归记录控件的原始位置和字体
        /// </summary>
        private void RecordOriginalBounds(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                originalControlBounds[control] = control.Bounds;
                originalControlFonts[control] = control.Font;

                if (control.HasChildren)
                {
                    RecordOriginalBounds(control);
                }
            }
        }

        /// <summary>
        /// 窗体大小改变事件处理
        /// </summary>
        private void StaticRockMechanicsForm_Resize(object sender, EventArgs e)
        {
            if (originalControlBounds.Count > 0)
            {
                ResizeControls(this);
            }
        }

        /// <summary>
        /// 递归调整控件大小和位置
        /// </summary>
        private void ResizeControls(Control parent)
        {
            float scaleX = (float)this.ClientSize.Width / originalFormSize.Width;
            float scaleY = (float)this.ClientSize.Height / originalFormSize.Height;

            foreach (Control control in parent.Controls)
            {
                if (originalControlBounds.ContainsKey(control))
                {
                    Rectangle originalBounds = originalControlBounds[control];
                    Font originalFont = originalControlFonts[control];

                    // 调整控件位置和大小
                    control.Left = (int)(originalBounds.Left * scaleX);
                    control.Top = (int)(originalBounds.Top * scaleY);
                    control.Width = (int)(originalBounds.Width * scaleX);
                    control.Height = (int)(originalBounds.Height * scaleY);

                    // 调整字体大小
                    float newFontSize = originalFont.Size * Math.Min(scaleX, scaleY);
                    if (newFontSize > 6 && newFontSize < 72) // 限制字体大小范围
                    {
                        try
                        {
                            control.Font = new Font(originalFont.FontFamily, newFontSize, originalFont.Style);
                        }
                        catch
                        {
                            // 如果字体创建失败，保持原字体
                        }
                    }
                }

                if (control.HasChildren)
                {
                    ResizeControls(control);
                }
            }
        }

        /// <summary>
        /// 批量计算数据 - 完全参考原系统BritSystem
        /// </summary>
        private void CalculateBatchData()
        {
            try
            {
                if (mechanicsData == null || mechanicsData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以计算！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                int successCount = 0;
                int errorCount = 0;
                var results = new List<RockMechanicsResult>();
                var allEs = new List<double>();
                var allMuS = new List<double>();

                // 第一遍：计算所有数据的Es和MuS，用于确定动态范围
                for (int i = 0; i < mechanicsData.Rows.Count; i++)
                {
                    try
                    {
                        DataRow row = mechanicsData.Rows[i];

                        double density = Convert.ToDouble(row["密度/(g/cm³)"]);
                        double vp = Convert.ToDouble(row["纵波速度/(m/s)"]);
                        double vs = Convert.ToDouble(row["横波速度/(m/s)"]);

                        // 进行单位转换
                        double convertedDensity = ConvertDensityToStandard(density);
                        double convertedVp = ConvertVelocityToStandard(vp, true);
                        double convertedVs = ConvertVelocityToStandard(vs, false);

                        // 数据验证
                        if (convertedVp <= convertedVs)
                        {
                            LoggingService.Instance.Warning($"第{i + 1}行数据不合理：纵波速度({convertedVp})应大于横波速度({convertedVs})");
                            results.Add(new RockMechanicsResult()); // 添加空结果
                            errorCount++;
                            continue;
                        }

                        // 计算岩石力学参数
                        var result = CalculateStaticRockMechanics(convertedDensity, convertedVp, convertedVs);
                        results.Add(result);

                        if (result.Es > 0 && result.MuS > 0)
                        {
                            allEs.Add(result.Es);
                            allMuS.Add(result.MuS);
                            successCount++;
                        }
                        else
                        {
                            errorCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Warning($"第{i + 1}行数据计算失败: {ex.Message}");
                        results.Add(new RockMechanicsResult()); // 添加空结果
                        errorCount++;
                    }
                }

                // 使用固定范围计算脆性指数 - 修正为与real.txt一致的参数范围
                double EsMin = 5.0;   // GPa
                double EsMax = 80.0;  // GPa
                double MuSMin = 0.1;
                double MuSMax = 0.4;

                // 第二遍：使用固定范围计算脆性指数并保存结果
                for (int i = 0; i < mechanicsData.Rows.Count; i++)
                {
                    var result = results[i];
                    if (result.Es > 0) // 有效结果
                    {
                        // 使用固定范围计算脆性指数
                        result.BrittlenessIndex = CalculateBrittlenessIndex(result.Es, result.MuS, EsMin, EsMax, MuSMin, MuSMax);

                        // 保存计算结果
                        mechanicsData.Rows[i]["动态杨氏模量/GPa"] = result.Ed;
                        mechanicsData.Rows[i]["动态泊松比"] = result.MuD;
                        mechanicsData.Rows[i]["静态杨氏模量/GPa"] = result.Es;
                        mechanicsData.Rows[i]["静态泊松比"] = result.MuS;
                        mechanicsData.Rows[i]["脆性指数/%"] = result.BrittlenessIndex;
                    }
                }

                // 更新数据表格显示
                dgvMechanicsData.DataSource = null;
                dgvMechanicsData.DataSource = mechanicsData;

                // 更新图表
                UpdateChart();

                MessageBox.Show($"批量计算完成！\n成功: {successCount} 条\n失败: {errorCount} 条",
                    "计算结果", MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoggingService.Instance.Info($"批量计算完成: 成功 {successCount} 条，失败 {errorCount} 条");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"批量计算失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                LoggingService.Instance.Error($"批量计算失败: {ex.Message}");
            }
        }

        #endregion

        #region 岩石力学计算方法

        /// <summary>
        /// 根据原系统公式计算静态岩石力学参数
        /// </summary>
        /// <param name="density">岩石密度 (g/cm³)</param>
        /// <param name="vp">纵波速度 (m/s)</param>
        /// <param name="vs">横波速度 (m/s)</param>
        /// <returns>计算结果</returns>
        private RockMechanicsResult CalculateStaticRockMechanics(double density, double vp, double vs)
        {
            var result = new RockMechanicsResult();

            // 数据验证：检查Vp > √2 * Vs
            if (vp <= Math.Sqrt(2) * vs)
            {
                // 数据异常，但仍继续计算，只是标记异常
                System.Diagnostics.Debug.WriteLine($"警告: Vp({vp}) <= √2·Vs({vs * Math.Sqrt(2):F2})，数据可能异常");
            }

            // 计算动态杨氏模量 Ed (GPa)
            // 使用标准弹性力学公式：Ed = ρ × Vs^2 × (3Vp^2 - 4Vs^2) / (Vp^2 - Vs^2)
            // 输入：ρ (g/cm³), Vp (m/s), Vs (m/s)
            // 输出：Ed (GPa)
            double vp2 = vp * vp;
            double vs2 = vs * vs;
            double denominator = vp2 - vs2;

            // 转换密度单位：g/cm³ -> kg/m³
            double densityKgM3 = density * 1000;

            // 计算动态杨氏模量（结果为Pa）
            result.Ed = densityKgM3 * vs2 * (3 * vp2 - 4 * vs2) / denominator;

            // 转换单位：Pa -> GPa
            result.Ed = result.Ed / 1e9;

            // 计算动态泊松比 μd
            // μd = (Vp^2 - 2Vs^2) / (2(Vp^2 - Vs^2))
            result.MuD = (vp2 - 2 * vs2) / (2 * denominator);

            // 计算静态杨氏模量 Es (GPa)
            // Es = Ed × 0.5823 + 7.566
            result.Es = result.Ed * 0.5823 + 7.566;

            // 计算静态泊松比 μs
            // μs = μd × 0.6648 + 0.0514
            result.MuS = result.MuD * 0.6648 + 0.0514;

            // 脆性指数计算将在批量计算时使用动态范围
            // 这里先设置为0，在批量计算时会重新计算
            result.BrittlenessIndex = 0;

            return result;
        }

        /// <summary>
        /// 使用动态范围计算脆性指数
        /// </summary>
        /// <param name="Es">静态杨氏模量</param>
        /// <param name="MuS">静态泊松比</param>
        /// <param name="EsMin">数据集中Es的最小值</param>
        /// <param name="EsMax">数据集中Es的最大值</param>
        /// <param name="MuSMin">数据集中MuS的最小值</param>
        /// <param name="MuSMax">数据集中MuS的最大值</param>
        /// <returns>脆性指数</returns>
        private double CalculateBrittlenessIndex(double Es, double MuS, double EsMin, double EsMax, double MuSMin, double MuSMax)
        {
            // 归一化杨氏模量脆性指数
            // EBRIT = (Es - Emin) / (Emax - Emin) × 100%
            double EBRIT = 0;
            if (EsMax > EsMin)
            {
                EBRIT = (Es - EsMin) / (EsMax - EsMin) * 100;
                EBRIT = Math.Max(0, Math.Min(100, EBRIT)); // 限制在0-100%范围内
            }

            // 归一化泊松比脆性指数
            // PBRIT = (μmax - μs) / (μmax - μmin) × 100%
            double PBRIT = 0;
            if (MuSMax > MuSMin)
            {
                PBRIT = (MuSMax - MuS) / (MuSMax - MuSMin) * 100;
                PBRIT = Math.Max(0, Math.Min(100, PBRIT)); // 限制在0-100%范围内
            }

            // 综合脆性指数
            // BRIT = (EBRIT + PBRIT) / 2
            double BRIT = (EBRIT + PBRIT) / 2;

            return BRIT;
        }

        #endregion

        #region 单位转换方法

        /// <summary>
        /// 将密度转换为标准单位 (g/cm³)
        /// </summary>
        private double ConvertDensityToStandard(double inputValue)
        {
            // 根据用户说明，系统中的密度实际单位是kg/m³，需要转换为g/cm³
            // 转换公式：ρ(g/cm³) = ρ(kg/m³) ÷ 1000
            return inputValue / 1000.0;
        }

        /// <summary>
        /// 将速度转换为标准单位 (m/s)
        /// </summary>
        /// <param name="inputValue">输入值</param>
        /// <param name="isVp">是否为纵波速度</param>
        /// <returns>转换后的速度值</returns>
        private double ConvertVelocityToStandard(double inputValue, bool isVp)
        {
            if (isVp) // 纵波速度
            {
                if (rbVelocityDt.Checked)
                {
                    // DT (μs/m) 转换为 Vp (m/s)
                    // Vp = 1,000,000 / DT
                    return 1000000.0 / inputValue;
                }
                else
                {
                    // 根据用户说明，系统中的速度实际单位是km/s，需要转换为m/s
                    // 转换公式：V(m/s) = V(km/s) × 1000
                    return inputValue * 1000.0;
                }
            }
            else // 横波速度
            {
                if (rbVelocityDts.Checked)
                {
                    // DTS (μs/m) 转换为 Vs (m/s)
                    // Vs = 1,000,000 / DTS
                    return 1000000.0 / inputValue;
                }
                else
                {
                    // 根据用户说明，系统中的速度实际单位是km/s，需要转换为m/s
                    // 转换公式：V(m/s) = V(km/s) × 1000
                    return inputValue * 1000.0;
                }
            }
        }

        #endregion

        #region Excel读写方法 - 完全参考原系统BritSystem

        /// <summary>
        /// 读取Excel文件 - 完全参考原系统BritSystem
        /// </summary>
        private DataSet ReadExcelSheets(string filePath)
        {
            try
            {
                DataSet dataSet = new DataSet();

                using (FileStream stream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook = null;

                    if (Path.GetExtension(filePath).ToLower() == ".xls")
                    {
                        workbook = new HSSFWorkbook(stream);
                    }
                    else
                    {
                        workbook = new XSSFWorkbook(stream);
                    }

                    for (int i = 0; i < workbook.NumberOfSheets; i++)
                    {
                        ISheet sheet = workbook.GetSheetAt(i);
                        DataTable dataTable = new DataTable(sheet.SheetName);

                        // 读取表头
                        IRow headerRow = sheet.GetRow(0);
                        if (headerRow != null)
                        {
                            for (int j = 0; j < headerRow.LastCellNum; j++)
                            {
                                ICell cell = headerRow.GetCell(j);
                                string columnName = cell?.ToString() ?? $"Column{j + 1}";
                                dataTable.Columns.Add(columnName, typeof(object));
                            }
                        }

                        // 读取数据行
                        for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
                        {
                            IRow row = sheet.GetRow(rowIndex);
                            if (row != null)
                            {
                                DataRow dataRow = dataTable.NewRow();
                                for (int cellIndex = 0; cellIndex < dataTable.Columns.Count; cellIndex++)
                                {
                                    ICell cell = row.GetCell(cellIndex);
                                    if (cell != null)
                                    {
                                        switch (cell.CellType)
                                        {
                                            case CellType.Numeric:
                                                dataRow[cellIndex] = cell.NumericCellValue;
                                                break;
                                            case CellType.String:
                                                dataRow[cellIndex] = cell.StringCellValue;
                                                break;
                                            case CellType.Boolean:
                                                dataRow[cellIndex] = cell.BooleanCellValue;
                                                break;
                                            case CellType.Formula:
                                                try
                                                {
                                                    dataRow[cellIndex] = cell.NumericCellValue;
                                                }
                                                catch
                                                {
                                                    dataRow[cellIndex] = cell.StringCellValue;
                                                }
                                                break;
                                            default:
                                                dataRow[cellIndex] = cell.ToString();
                                                break;
                                        }
                                    }
                                }
                                dataTable.Rows.Add(dataRow);
                            }
                        }

                        dataSet.Tables.Add(dataTable);
                    }
                }

                return dataSet;
            }
            catch (Exception ex)
            {
                throw new Exception($"读取Excel文件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 智能识别列名 - 完全参考原系统BritSystem
        /// </summary>
        private string? FindColumnByPattern(DataTable dataTable, string patternKey)
        {
            if (!columnPatterns.ContainsKey(patternKey))
                return null;

            var patterns = columnPatterns[patternKey];

            foreach (DataColumn column in dataTable.Columns)
            {
                string columnName = column.ColumnName.Trim().ToLower();
                foreach (string pattern in patterns)
                {
                    if (columnName.Contains(pattern.ToLower()))
                    {
                        return column.ColumnName;
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// 将导入的数据映射到标准格式 - 完全参考原系统BritSystem
        /// </summary>
        private DataTable? MapImportedDataToStandardFormat(DataTable importedData)
        {
            try
            {
                // 智能识别列名
                string? depthColumn = FindColumnByPattern(importedData, "深度");
                string? densityColumn = FindColumnByPattern(importedData, "密度");
                string? vpColumn = FindColumnByPattern(importedData, "纵波速度");
                string? vsColumn = FindColumnByPattern(importedData, "横波速度");

                // 检查必需的列是否存在
                if (string.IsNullOrEmpty(densityColumn) || string.IsNullOrEmpty(vpColumn) || string.IsNullOrEmpty(vsColumn))
                {
                    LoggingService.Instance.Warning($"缺少必需的列: 密度={densityColumn}, 纵波速度={vpColumn}, 横波速度={vsColumn}");
                    return null;
                }

                // 创建标准格式的数据表
                DataTable standardData = new DataTable();
                standardData.Columns.Add("深度/m", typeof(double)); // 修改为只使用深度列
                standardData.Columns.Add("密度/(g/cm³)", typeof(double));
                standardData.Columns.Add("纵波速度/(m/s)", typeof(double));
                standardData.Columns.Add("横波速度/(m/s)", typeof(double));
                standardData.Columns.Add("动态杨氏模量/GPa", typeof(double));
                standardData.Columns.Add("动态泊松比", typeof(double));
                standardData.Columns.Add("静态杨氏模量/GPa", typeof(double));
                standardData.Columns.Add("静态泊松比", typeof(double));
                standardData.Columns.Add("脆性指数/%", typeof(double));

                // 映射数据
                foreach (DataRow sourceRow in importedData.Rows)
                {
                    try
                    {
                        DataRow newRow = standardData.NewRow();

                        // 深度数据
                        if (!string.IsNullOrEmpty(depthColumn) && sourceRow[depthColumn] != DBNull.Value)
                        {
                            double depth = Convert.ToDouble(sourceRow[depthColumn]);
                            newRow["深度/m"] = depth;
                        }
                        else
                        {
                            newRow["深度/m"] = 0.0;
                        }

                        // 密度数据
                        if (sourceRow[densityColumn] != DBNull.Value)
                        {
                            newRow["密度/(g/cm³)"] = Convert.ToDouble(sourceRow[densityColumn]);
                        }

                        // 纵波速度数据
                        if (sourceRow[vpColumn] != DBNull.Value)
                        {
                            newRow["纵波速度/(m/s)"] = Convert.ToDouble(sourceRow[vpColumn]);
                        }

                        // 横波速度数据
                        if (sourceRow[vsColumn] != DBNull.Value)
                        {
                            newRow["横波速度/(m/s)"] = Convert.ToDouble(sourceRow[vsColumn]);
                        }

                        // 初始化计算结果列为0
                        newRow["动态杨氏模量/GPa"] = 0.0;
                        newRow["动态泊松比"] = 0.0;
                        newRow["静态杨氏模量/GPa"] = 0.0;
                        newRow["静态泊松比"] = 0.0;
                        newRow["脆性指数/%"] = 0.0;

                        standardData.Rows.Add(newRow);
                    }
                    catch (Exception ex)
                    {
                        LoggingService.Instance.Warning($"映射数据行时出错: {ex.Message}");
                    }
                }

                LoggingService.Instance.Info($"数据映射完成，共 {standardData.Rows.Count} 行有效数据");
                return standardData;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"数据映射失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 导出Excel文件 - 完全参考原系统BritSystem
        /// </summary>
        private void ExportExcelFile(string filePath)
        {
            try
            {
                IWorkbook workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet("静态岩石力学参数数据");

                // 创建表头
                IRow headerRow = sheet.CreateRow(0);
                for (int i = 0; i < mechanicsData.Columns.Count; i++)
                {
                    headerRow.CreateCell(i).SetCellValue(mechanicsData.Columns[i].ColumnName);
                }

                // 写入数据
                for (int i = 0; i < mechanicsData.Rows.Count; i++)
                {
                    IRow dataRow = sheet.CreateRow(i + 1);
                    for (int j = 0; j < mechanicsData.Columns.Count; j++)
                    {
                        var cellValue = mechanicsData.Rows[i][j];
                        if (cellValue != null && cellValue != DBNull.Value)
                        {
                            if (double.TryParse(cellValue.ToString(), out double numValue))
                            {
                                dataRow.CreateCell(j).SetCellValue(numValue);
                            }
                            else
                            {
                                dataRow.CreateCell(j).SetCellValue(cellValue.ToString());
                            }
                        }
                    }
                }

                // 自动调整列宽
                for (int i = 0; i < mechanicsData.Columns.Count; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 保存文件
                using (FileStream fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fs);
                }

                LoggingService.Instance.Info($"数据导出成功: {filePath}，共 {mechanicsData.Rows.Count} 行数据");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"导出Excel文件失败: {ex.Message}");
                throw;
            }
        }

        #endregion
    }
}
