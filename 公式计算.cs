using System;

public class RockMechanicsCalculator
{
    /// <summary>
    /// 计算动态杨氏模量 (Ed)
    /// 文献公式：Ed = 10^-3 × ρ × Vs² × (3Vp² - 4Vs²) / (Vp² - Vs²)
    /// </summary>
    /// <param name="densityKgPerM3">密度 (kg/m³)</param>
    /// <param name="vpKmPerSec">纵波速度 (km/s)</param>
    /// <param name="vsKmPerSec">横波速度 (km/s)</param>
    /// <returns>动态杨氏模量 (GPa)</returns>
    public static double CalculateDynamicYoungsModulus(double densityKgPerM3, double vpKmPerSec, double vsKmPerSec)
    {
        // 单位转换：kg/m³ → g/cm³ (除以1000)
        double densityGPerCm3 = densityKgPerM3 / 1000.0;
        
        // 单位转换：km/s → m/s (乘以1000)
        double vpMSec = vpKmPerSec * 1000.0;
        double vsMSec = vsKmPerSec * 1000.0;
        
        // 计算公式
        double numerator = 3 * Math.Pow(vpMSec, 2) - 4 * Math.Pow(vsMSec, 2);
        double denominator = Math.Pow(vpMSec, 2) - Math.Pow(vsMSec, 2);
        
        // 防止除以零
        if (Math.Abs(denominator) < 1e-10)
            return 0;
            
        return 1e-3 * densityGPerCm3 * Math.Pow(vsMSec, 2) * (numerator / denominator);
    }
    
    /// <summary>
    /// 计算动态泊松比 (μd)
    /// 文献公式：μd = (Vp² - 2Vs²) / [2(Vp² - Vs²)]
    /// </summary>
    /// <param name="vpKmPerSec">纵波速度 (km/s)</param>
    /// <param name="vsKmPerSec">横波速度 (km/s)</param>
    /// <returns>动态泊松比 (无单位)</returns>
    public static double CalculateDynamicPoissonRatio(double vpKmPerSec, double vsKmPerSec)
    {
        // 单位转换：km/s → m/s (乘以1000)，但泊松比计算中单位会抵消，可不转换
        double vpSquared = Math.Pow(vpKmPerSec, 2);
        double vsSquared = Math.Pow(vsKmPerSec, 2);
        
        double numerator = vpSquared - 2 * vsSquared;
        double denominator = 2 * (vpSquared - vsSquared);
        
        // 防止除以零
        if (Math.Abs(denominator) < 1e-10)
            return 0;
            
        return numerator / denominator;
    }
    
    /// <summary>
    /// 计算静态杨氏模量 (Es)
    /// 文献公式：Es = Ed × 0.5823 + 7.566
    /// </summary>
    /// <param name="dynamicYoungsModulusGPa">动态杨氏模量 (GPa)</param>
    /// <returns>静态杨氏模量 (GPa)</returns>
    public static double CalculateStaticYoungsModulus(double dynamicYoungsModulusGPa)
    {
        return dynamicYoungsModulusGPa * 0.5823 + 7.566;
    }
    
    /// <summary>
    /// 计算静态泊松比 (μs)
    /// 文献公式：μs = μd × 0.6648 + 0.0514
    /// </summary>
    /// <param name="dynamicPoissonRatio">动态泊松比 (无单位)</param>
    /// <returns>静态泊松比 (无单位)</returns>
    public static double CalculateStaticPoissonRatio(double dynamicPoissonRatio)
    {
        return dynamicPoissonRatio * 0.6648 + 0.0514;
    }
    
    /// <summary>
    /// 计算脆性指数 (BRITe)
    /// 文献公式：BRITe = (EBRIT + μBRIT) / 2
    /// 其中：
    /// EBRIT = (Es - Es_min) / (Es_max - Es_min) × 100%
    /// μBRIT = (μs_max - μs) / (μs_max - μs_min) × 100%
    /// </summary>
    /// <param name="staticYoungsModulus">静态杨氏模量 (GPa)</param>
    /// <param name="staticPoissonRatio">静态泊松比 (无单位)</param>
    /// <param name="esMin">数据集中静态杨氏模量最小值 (GPa)</param>
    /// <param name="esMax">数据集中静态杨氏模量最大值 (GPa)</param>
    /// <param name="musMin">数据集中静态泊松比最小值</param>
    /// <param name="musMax">数据集中静态泊松比最大值</param>
    /// <returns>脆性指数 (%)</returns>
    public static double CalculateBrittlenessIndex(
        double staticYoungsModulus, 
        double staticPoissonRatio,
        double esMin, 
        double esMax, 
        double musMin, 
        double musMax)
    {
        // 计算归一化杨氏模量脆性指数
        double eBrit;
        if (Math.Abs(esMax - esMin) < 1e-10)
            eBrit = 0;
        else
            eBrit = (staticYoungsModulus - esMin) / (esMax - esMin) * 100;
        
        // 计算归一化泊松比脆性指数
        double muBrit;
        if (Math.Abs(musMax - musMin) < 1e-10)
            muBrit = 0;
        else
            muBrit = (musMax - staticPoissonRatio) / (musMax - musMin) * 100;
        
        // 计算综合脆性指数
        return (eBrit + muBrit) / 2;
    }
}
