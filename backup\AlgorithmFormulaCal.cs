﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using System.Globalization;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.HSSF.UserModel;
using System.Windows.Forms.DataVisualization.Charting;

namespace BritSystem
{
    public partial class AlgorithmFormulaCal : Form
    {
        // 添加一个按钮，用于显示所有可用列
        private Button btnShowColumns;
        private DataTable _sourceData;  // 原始数据表
        private DataTable _resultData;   // 计算结果数据表
        private List<string> _brittleColumns = new List<string>();
        private List<string> _ductileColumns = new List<string>();
        private ComboBox cboRowSelector;  // 行选择下拉框
        private string _lastLoadedFilePath = string.Empty;  // 最后加载的文件路径

        // 添加列名映射字典
        private Dictionary<string, string> _columnMappings = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);

        // 添加全局映射配置
        private Dictionary<string, List<string>> _globalBrittleMappings = new Dictionary<string, List<string>>();
        private Dictionary<string, List<string>> _globalDuctileMappings = new Dictionary<string, List<string>>();

        // 添加顶深和底深列索引
        private int topDepthIndex = -1;
        private int bottomDepthIndex = -1;
        private string topDepthColumnName = string.Empty;
        private string bottomDepthColumnName = string.Empty;

        // 这些控件已经在设计器中创建
        // 变量已经在Designer.cs文件中声明

        // 数据点类，用于存储计算结果
        private class BrittlenessDataPoint
        {
            public string GeoID { get; set; }        // 地质点唯一标识
            public double TopDepth { get; set; }     // 顶深
            public double BottomDepth { get; set; }  // 底深
            public double BrittleIndex { get; set; } // 脆性指数
            public List<string> BrittleMinerals { get; set; } = new List<string>(); // 脆性矿物列表
            public List<string> DuctileMinerals { get; set; } = new List<string>(); // 塑性矿物列表
            public int RowIndex { get; set; } = -1;  // 原始数据行索引

            // 新增：列名映射字典
            public Dictionary<string, string> ColumnMappings { get; set; } = new Dictionary<string, string>();

            // 生成唯一的GeoID
            public void GenerateGeoID()
            {
                // 使用深度和脆性指数生成唯一的GeoID
                GeoID = $"GEO_{TopDepth:F2}_{BottomDepth:F2}_{BrittleIndex:F4}";
                // 添加哈希校验保证唯一性
                GeoID += $"_{Math.Abs(GeoID.GetHashCode()):X8}";
                System.Diagnostics.Debug.WriteLine($"生成GeoID: {GeoID}");
            }

            // 新增：生成包含映射信息的GeoID
            public void GenerateGeoIDWithMappings(Dictionary<string, string> brittleMappings, Dictionary<string, string> ductileMappings)
            {
                // 基本GeoID生成
                GenerateGeoID();

                // 保存列名映射
                ColumnMappings.Clear();

                // 添加脆性矿物映射
                foreach (var mapping in brittleMappings)
                {
                    ColumnMappings[mapping.Key] = mapping.Value;
                }

                // 添加塑性矿物映射
                foreach (var mapping in ductileMappings)
                {
                    ColumnMappings[mapping.Key] = mapping.Value;
                }

                System.Diagnostics.Debug.WriteLine($"GeoID {GeoID} 包含 {ColumnMappings.Count} 个列名映射");
            }
        }

        private List<BrittlenessDataPoint> _dataPoints = new List<BrittlenessDataPoint>();

        public AlgorithmFormulaCal()
        {
            InitializeComponent();

            // 初始化列表
            _brittleColumns = new List<string>();
            _ductileColumns = new List<string>();
            _dataPoints = new List<BrittlenessDataPoint>();

            // 初始化全局列名映射
            InitializeGlobalMappings();

            // 初始化列名映射
            InitializeColumnMappings();


            // 初始化全局映射
            InitializeGlobalMappings();

            // 创建一个空的数据表用于测试
            _sourceData = new DataTable();
            _sourceData.Columns.Add("石英", typeof(double));
            _sourceData.Columns.Add("长石", typeof(double));
            _sourceData.Columns.Add("碳酸盐", typeof(double));
            _sourceData.Columns.Add("黏土", typeof(double));
            _sourceData.Columns.Add("其他矿物", typeof(double));

            // 添加一些测试数据
            _sourceData.Rows.Add(30.5, 25.2, 15.8, 20.3, 8.2);

            // 初始化结果数据表
            _resultData = new DataTable();
            InitializeResultDataTable();

            // 初始化右键菜单
            InitializeContextMenus();

            // 初始化行选择下拉框
            InitializeRowSelector();
        }

        public AlgorithmFormulaCal(DataTable sourceData)
        {
            InitializeComponent();

            // 初始化列表
            _brittleColumns = new List<string>();
            _ductileColumns = new List<string>();
            _dataPoints = new List<BrittlenessDataPoint>();

            // 初始化全局列名映射
            InitializeGlobalMappings();

            // 初始化列名映射
            InitializeColumnMappings();

            // 初始化全局映射
            InitializeGlobalMappings();


            _sourceData = sourceData ?? new DataTable();
            _resultData = new DataTable();

            // 初始化右键菜单
            InitializeContextMenus();

            // 初始化结果数据表
            InitializeResultDataTable();

            // 初始化行选择下拉框
            InitializeRowSelector();

            // 加载列名
            LoadColumnNames();
        }



        // 初始化右键菜单
        private void InitializeContextMenus()
        {
            // 添加右键菜单
            var brittleContextMenu = new ContextMenuStrip();
            brittleContextMenu.Items.Add("移除", null, (s, e) =>
            {
                if (lstBrittleColumns.SelectedItem != null)
                {
                    string itemText = lstBrittleColumns.SelectedItem.ToString();
                    _brittleColumns.Remove(itemText);
                    lstBrittleColumns.Items.Remove(lstBrittleColumns.SelectedItem);

                    // 将移除的项目添加回可用列表
                    if (!lstAvailableColumns.Items.Contains(itemText))
                    {
                        lstAvailableColumns.Items.Add(itemText);
                    }

                    System.Diagnostics.Debug.WriteLine($"右键菜单移除脆性矿物: {itemText}");

                    // 立即高亮显示选中的列
                    if (dgvResult.DataSource == _sourceData)
                    {
                        HighlightSelectedColumns();
                    }
                }
            });
            lstBrittleColumns.ContextMenuStrip = brittleContextMenu;

            var ductileContextMenu = new ContextMenuStrip();
            ductileContextMenu.Items.Add("移除", null, (s, e) =>
            {
                if (lstDuctileColumns.SelectedItem != null)
                {
                    string itemText = lstDuctileColumns.SelectedItem.ToString();
                    _ductileColumns.Remove(itemText);
                    lstDuctileColumns.Items.Remove(lstDuctileColumns.SelectedItem);

                    // 将移除的项目添加回可用列表
                    if (!lstAvailableColumns.Items.Contains(itemText))
                    {
                        lstAvailableColumns.Items.Add(itemText);
                    }

                    System.Diagnostics.Debug.WriteLine($"右键菜单移除塑性矿物: {itemText}");

                    // 立即高亮显示选中的列
                    if (dgvResult.DataSource == _sourceData)
                    {
                        HighlightSelectedColumns();
                    }
                }
            });
            lstDuctileColumns.ContextMenuStrip = ductileContextMenu;
        }

        // 窗体加载事件
        private void AlgorithmFormulaCal_Load(object sender, EventArgs e)
        {
            LoadColumnNames();

            // 初始化结果数据表
            InitializeResultDataTable();

            // 添加窗体大小改变事件
            this.Resize += AlgorithmFormulaCal_Resize;

            // 设置顶深和底深列选择按钮的事件处理方法
            btnTopDepth.Click += BtnTopDepth_Click;
            btnBottomDepth.Click += BtnBottomDepth_Click;

            // 初始化标签文本
            lblTopDepth.Text = "未选择";
            lblTopDepth.ForeColor = Color.Red;

            lblBottomDepth.Text = "未选择";
            lblBottomDepth.ForeColor = Color.Red;

            // 设置测试数据按钮事件
            btnTestData.Click += BtnTestData_Click;

            // 设置显示数据按钮事件
            btnShowData.Click += BtnShowData_Click;

            // 设置列名映射按钮事件
            btnMapColumns.Click += BtnMapColumns_Click;

            // 初始化时调整控件位置
            AdjustControlPositions();
        }

        // 初始化结果数据表
        // ... existing code ...

        // 初始化结果数据表
        private void InitializeResultDataTable()
        {
            try
            {
                // 清空结果表
                _resultData.Clear();
                _resultData.Columns.Clear();

                // 添加基本列
                _resultData.Columns.Add("GeoID", typeof(string));
                _resultData.Columns.Add("顶深/m", typeof(double));  // 修改列名为"顶深/m"
                _resultData.Columns.Add("底深/m", typeof(double));  // 修改列名为"底深/m"
                _resultData.Columns.Add("脆性指数", typeof(double));

                // 添加脆性矿物列
                foreach (string columnName in _brittleColumns)
                {
                    if (!_resultData.Columns.Contains(columnName))
                    {
                        _resultData.Columns.Add(columnName, typeof(double));
                    }
                }

                // 添加塑性矿物列
                foreach (string columnName in _ductileColumns)
                {
                    if (!_resultData.Columns.Contains(columnName))
                    {
                        _resultData.Columns.Add(columnName, typeof(double));
                    }
                }

                // 设置数据源
                dgvResult.DataSource = _resultData;

                System.Diagnostics.Debug.WriteLine($"结果数据表初始化完成，共 {_resultData.Columns.Count} 列");
                System.Diagnostics.Debug.WriteLine("结果表列名:");
                foreach (DataColumn col in _resultData.Columns)
                {
                    System.Diagnostics.Debug.WriteLine($"  - {col.ColumnName}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化结果数据表时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"初始化结果数据表时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }

        // 窗体大小改变事件
        private void AlgorithmFormulaCal_Resize(object sender, EventArgs e)
        {
            AdjustControlPositions();
        }

        // 列名映射按钮点击事件
        private void BtnMapColumns_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否已加载数据
                if (_sourceData == null || _sourceData.Columns.Count == 0)
                {
                    MessageBox.Show("请先加载数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 显示列名映射对话框
                ShowColumnMappingDialog(_sourceData);

                // 更新顶深和底深标签
                if (!string.IsNullOrEmpty(topDepthColumnName))
                {
                    lblTopDepth.Text = topDepthColumnName;
                    lblTopDepth.ForeColor = Color.Green;
                }

                if (!string.IsNullOrEmpty(bottomDepthColumnName))
                {
                    lblBottomDepth.Text = bottomDepthColumnName;
                    lblBottomDepth.ForeColor = Color.Green;
                }

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"列名映射更新: 顶深={topDepthColumnName}({topDepthIndex}), 底深={bottomDepthColumnName}({bottomDepthIndex})");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"列名映射按钮点击事件出错: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"列名映射出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 调整控件位置
        private void AdjustControlPositions()
        {
            try
            {
                // 输出调试信息
                System.Diagnostics.Debug.WriteLine("开始调整控件位置");

                // 计算窗口宽度和高度的比例
                float widthRatio = (float)this.ClientSize.Width / 1107f; // 原始设计宽度
                float heightRatio = (float)this.ClientSize.Height / 898f; // 原始设计高度

                // 使用相同的缩放比例，保持布局比例
                float ratio = Math.Min(widthRatio, heightRatio);

                // 计算居中偏移量
                int offsetX = (int)((this.ClientSize.Width - 1107 * ratio) / 2);
                int offsetY = (int)((this.ClientSize.Height - 898 * ratio) / 2);

                // 左侧面板
                if (leftPanel != null)
                {
                    leftPanel.Location = new Point(offsetX, offsetY);
                    leftPanel.Width = (int)(489 * ratio);
                    leftPanel.Height = (int)(898 * ratio);
                }

                // 右侧面板
                if (rightPanel != null)
                {
                    rightPanel.Location = new Point(offsetX + (int)(489 * ratio), offsetY);
                    rightPanel.Width = (int)(618 * ratio);
                    rightPanel.Height = (int)(898 * ratio);
                }

                // 可用列标签和列表
                if (lblAvailable != null)
                    lblAvailable.Location = new Point((int)(24 * ratio), (int)(24 * ratio));

                if (lstAvailableColumns != null)
                {
                    lstAvailableColumns.Location = new Point((int)(24 * ratio), (int)(60 * ratio));
                    lstAvailableColumns.Size = new Size((int)(440 * ratio), (int)(194 * ratio));
                }

                // 脆性矿物标签和列表
                if (lblBrittle != null)
                    lblBrittle.Location = new Point((int)(24 * ratio), (int)(288 * ratio));

                if (lstBrittleColumns != null)
                {
                    lstBrittleColumns.Location = new Point((int)(13 * ratio), (int)(340 * ratio));
                    lstBrittleColumns.Size = new Size((int)(451 * ratio), (int)(194 * ratio));
                }

                // 塑性矿物标签和列表
                if (lblDuctile != null)
                    lblDuctile.Location = new Point((int)(24 * ratio), (int)(603 * ratio));

                if (lstDuctileColumns != null)
                {
                    lstDuctileColumns.Location = new Point((int)(24 * ratio), (int)(651 * ratio));
                    lstDuctileColumns.Size = new Size((int)(440 * ratio), (int)(194 * ratio));
                }

                // 按钮
                if (btnAddBrittle != null)
                {
                    btnAddBrittle.Location = new Point((int)(367 * ratio), (int)(282 * ratio));
                    btnAddBrittle.Size = new Size((int)(98 * ratio), (int)(36 * ratio));
                }

                if (btnRemoveBrittle != null)
                {
                    btnRemoveBrittle.Location = new Point((int)(257 * ratio), (int)(282 * ratio));
                    btnRemoveBrittle.Size = new Size((int)(98 * ratio), (int)(36 * ratio));
                }

                if (btnAddDuctile != null)
                {
                    btnAddDuctile.Location = new Point((int)(367 * ratio), (int)(597 * ratio));
                    btnAddDuctile.Size = new Size((int)(98 * ratio), (int)(36 * ratio));
                }

                if (btnRemoveDuctile != null)
                {
                    btnRemoveDuctile.Location = new Point((int)(257 * ratio), (int)(598 * ratio));
                    btnRemoveDuctile.Size = new Size((int)(98 * ratio), (int)(36 * ratio));
                }

                // 右侧面板控件
                if (formulaPanel != null)
                {
                    formulaPanel.Location = new Point((int)(24 * ratio), (int)(24 * ratio));
                    formulaPanel.Size = new Size((int)(585 * ratio), (int)(96 * ratio));
                }

                if (btnLoadData != null)
                {
                    btnLoadData.Location = new Point((int)(24 * ratio), (int)(144 * ratio));
                    btnLoadData.Size = new Size((int)(183 * ratio), (int)(48 * ratio));
                }

                if (btnCalculate != null && rightPanel != null)
                {
                    btnCalculate.Location = new Point((int)(rightPanel.Width - (183 * ratio) - (24 * ratio)), (int)(144 * ratio));
                    btnCalculate.Size = new Size((int)(183 * ratio), (int)(48 * ratio));
                }

                if (resultLabel != null)
                    resultLabel.Location = new Point((int)(24 * ratio), (int)(216 * ratio));

                if (dgvResult != null && rightPanel != null)
                {
                    dgvResult.Location = new Point((int)(24 * ratio), (int)(252 * ratio));
                    dgvResult.Size = new Size((int)(rightPanel.Width - (48 * ratio)), (int)(rightPanel.Height - (252 * ratio) - (80 * ratio)));
                }

                // 测试数据按钮
                if (btnTestData != null)
                {
                    btnTestData.Location = new Point((int)(225 * ratio), (int)(144 * ratio));
                    btnTestData.Size = new Size((int)(183 * ratio), (int)(48 * ratio));
                }

                // 显示数据按钮
                if (btnShowData != null)
                {
                    btnShowData.Location = new Point((int)(24 * ratio), (int)(204 * ratio));
                    btnShowData.Size = new Size((int)(183 * ratio), (int)(48 * ratio));
                }

                // 可视化按钮
                if (btnVisualize != null)
                {
                    btnVisualize.Location = new Point((int)(225 * ratio), (int)(204 * ratio));
                    btnVisualize.Size = new Size((int)(183 * ratio), (int)(48 * ratio));
                }

                // 保存数据按钮
                if (btnSaveData != null && rightPanel != null)
                {
                    btnSaveData.Location = new Point((int)(rightPanel.Width - (183 * ratio) - (24 * ratio)), (int)(rightPanel.Height - (60 * ratio)));
                    btnSaveData.Size = new Size((int)(183 * ratio), (int)(48 * ratio));
                }

                // 更新行选择下拉框位置
                if (cboRowSelector != null && lblAvailable != null)
                {
                    cboRowSelector.Location = new Point(lblAvailable.Right + (int)(10 * ratio), lblAvailable.Top);
                    cboRowSelector.Size = new Size((int)(80 * ratio), (int)(25 * ratio));
                }

                // 更新顶深和底深列控件位置和大小
                if (btnTopDepth != null && lblTopDepth != null && btnBottomDepth != null && lblBottomDepth != null)
                {
                    // 设置位置和大小，保持比例
                    btnTopDepth.Location = new Point((int)(24 * ratio), (int)(500 * ratio));
                    btnTopDepth.Size = new Size((int)(120 * ratio), (int)(30 * ratio));

                    lblTopDepth.Location = new Point((int)(btnTopDepth.Right + (10 * ratio)), (int)(btnTopDepth.Top + (5 * ratio)));
                    lblTopDepth.Size = new Size((int)(200 * ratio), (int)(20 * ratio));

                    btnBottomDepth.Location = new Point((int)(24 * ratio), (int)(btnTopDepth.Bottom + (10 * ratio)));
                    btnBottomDepth.Size = new Size((int)(120 * ratio), (int)(30 * ratio));

                    lblBottomDepth.Location = new Point((int)(btnBottomDepth.Right + (10 * ratio)), (int)(btnBottomDepth.Top + (5 * ratio)));
                    lblBottomDepth.Size = new Size((int)(200 * ratio), (int)(20 * ratio));
                }

                // 调整列名映射按钮位置
                if (btnMapColumns != null)
                {
                    btnMapColumns.Location = new Point((int)(24 * ratio), (int)(btnBottomDepth.Bottom + (20 * ratio)));
                    btnMapColumns.Size = new Size((int)(120 * ratio), (int)(30 * ratio));
                }

                System.Diagnostics.Debug.WriteLine("控件位置调整完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"调整控件位置时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }

        // 显示源数据按钮事件
        private void BtnShowData_Click(object sender, EventArgs e)
        {
            try
            {
                if (_sourceData == null || _sourceData.Rows.Count == 0)
                {
                    MessageBox.Show("源数据表为空或没有行数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 输出所有列名，帮助调试
                PrintAllColumnNames();

                // 将源数据显示在结果表格中
                dgvResult.DataSource = null;
                dgvResult.DataSource = _sourceData;

                // 设置列头可见
                dgvResult.ColumnHeadersVisible = true;
                dgvResult.EnableHeadersVisualStyles = false;

                // 高亮显示选中的列
                HighlightSelectedColumns();

                // 更新结果标签
                resultLabel.Text = $"源数据: 共{_sourceData.Rows.Count}行, {_sourceData.Columns.Count}列";

                System.Diagnostics.Debug.WriteLine($"显示源数据: {_sourceData.Rows.Count}行, {_sourceData.Columns.Count}列");

                // 强制刷新UI
                Application.DoEvents();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示源数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"显示源数据时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }

        // 生成测试数据按钮事件
        private void BtnTestData_Click(object sender, EventArgs e)
        {
            try
            {
                // 清空当前数据
                _sourceData = new DataTable();
                _brittleColumns.Clear();
                _ductileColumns.Clear();
                lstBrittleColumns.Items.Clear();
                lstDuctileColumns.Items.Clear();
                lstAvailableColumns.Items.Clear();
                _dataPoints.Clear();
                _resultData.Rows.Clear();

                // 创建测试数据表
                _sourceData.Columns.Add("顶深", typeof(double));
                _sourceData.Columns.Add("底深", typeof(double));
                _sourceData.Columns.Add("石英", typeof(double));
                _sourceData.Columns.Add("长石", typeof(double));
                _sourceData.Columns.Add("碳酸盐", typeof(double));
                _sourceData.Columns.Add("黏土", typeof(double));
                _sourceData.Columns.Add("其他矿物", typeof(double));

                // 添加测试数据
                Random random = new Random();
                int rowCount = 20;
                System.Diagnostics.Debug.WriteLine($"Generating {rowCount} rows of test data");

                for (int i = 0; i < rowCount; i++)
                {
                    double topDepth = 1000 + i * 10;
                    double bottomDepth = topDepth + 10;
                    double quartz = random.Next(10, 40);
                    double feldspar = random.Next(5, 30);
                    double carbonate = random.Next(5, 25);
                    double clay = random.Next(10, 35);
                    double other = random.Next(1, 10);

                    try
                    {
                        _sourceData.Rows.Add(topDepth, bottomDepth, quartz, feldspar, carbonate, clay, other);
                        System.Diagnostics.Debug.WriteLine($"Added row {i}: {topDepth}, {bottomDepth}, {quartz}, {feldspar}, {carbonate}, {clay}, {other}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error adding row {i}: {ex.Message}");
                    }
                }

                // 验证数据是否正确添加
                System.Diagnostics.Debug.WriteLine($"After adding test data, _sourceData has {_sourceData.Rows.Count} rows");

                // 如果没有成功添加任何行，尝试另一种方式
                if (_sourceData.Rows.Count == 0)
                {
                    // ... existing code ...
                }

                // 再次验证数据
                if (_sourceData.Rows.Count == 0)
                {
                    MessageBox.Show("无法生成测试数据，请尝试手动导入数据。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 更新行选择下拉框
                UpdateRowSelectorItems();

                // 加载列名
                LoadColumnNames();

                // 自动选择顶深和底深列
                for (int i = 0; i < _sourceData.Columns.Count; i++)
                {
                    string columnName = _sourceData.Columns[i].ColumnName;
                    if (columnName.Contains("顶深"))
                    {
                        topDepthIndex = i;
                        topDepthColumnName = columnName;
                        lblTopDepth.Text = columnName;
                        lblTopDepth.ForeColor = Color.Green;
                        System.Diagnostics.Debug.WriteLine($"自动选择顶深列: {columnName}, 索引: {i}");
                    }
                    else if (columnName.Contains("底深"))
                    {
                        bottomDepthIndex = i;
                        bottomDepthColumnName = columnName;
                        lblBottomDepth.Text = columnName;
                        lblBottomDepth.ForeColor = Color.Green;
                        System.Diagnostics.Debug.WriteLine($"自动选择底深列: {columnName}, 索引: {i}");
                    }
                }

                // ... existing code ...
            }
            catch (Exception ex)
            {
                // ... existing code ...
            }
        }

        // 加载数据按钮事件
        private void btnLoadData_Click(object sender, EventArgs e)
        {
            try
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "Excel文件|*.xlsx;*.xls|所有文件|*.*";
                    openFileDialog.Title = "选择Excel数据文件";

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        string filePath = openFileDialog.FileName;
                        _lastLoadedFilePath = filePath;

                        // 加载Excel数据
                        _sourceData = LoadExcelData(filePath);

                        if (_sourceData != null && _sourceData.Rows.Count > 0)
                        {
                            // 输出调试信息
                            System.Diagnostics.Debug.WriteLine($"成功加载数据: {_sourceData.Rows.Count}行, {_sourceData.Columns.Count}列");

                            // 输出所有列名，帮助调试
                            PrintAllColumnNames();

                            // 初始化全局映射
                            InitializeGlobalMappings();

                            // 初始化列名映射
                            InitializeColumnMappings();

                            // 加载列名
                            LoadColumnNames();

                            // 显示源数据
                            dgvResult.DataSource = _sourceData;

                            // 更新结果标签
                            resultLabel.Text = $"源数据: 共{_sourceData.Rows.Count}行, {_sourceData.Columns.Count}列";

                            MessageBox.Show($"成功加载数据: {_sourceData.Rows.Count}行, {_sourceData.Columns.Count}列", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        else
                        {
                            MessageBox.Show("加载数据失败或数据为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载数据时出错: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"加载数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 从文件加载数据
        private void LoadDataFromFile(string filePath)
        {
            try
            {
                // 清空当前数据
                _sourceData = new DataTable();
                _brittleColumns.Clear();
                _ductileColumns.Clear();
                lstBrittleColumns.Items.Clear();
                lstDuctileColumns.Items.Clear();
                lstAvailableColumns.Items.Clear();
                _dataPoints.Clear();
                _resultData.Rows.Clear();

                // 根据文件扩展名选择加载方法
                string extension = Path.GetExtension(filePath).ToLower();
                if (extension == ".xlsx" || extension == ".xls")
                {
                    LoadExcelData(filePath);
                }
                else if (extension == ".csv")
                {
                    LoadCsvData(filePath);
                }
                else
                {
                    MessageBox.Show("不支持的文件格式，请选择Excel或CSV文件。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 验证数据是否正确加载
                if (_sourceData == null)
                {
                    MessageBox.Show("数据加载失败，请检查文件格式。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"Loaded data from {filePath}");
                System.Diagnostics.Debug.WriteLine($"Columns: {_sourceData.Columns.Count}, Rows: {_sourceData.Rows.Count}");

                // 检查是否有行数据
                if (_sourceData.Rows.Count == 0)
                {
                    // 提示用户文件中没有数据行
                    DialogResult result = MessageBox.Show("导入的文件没有数据行。是否手动添加测试数据？",
                        "警告", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                    if (result == DialogResult.Yes)
                    {
                        // 调用生成测试数据方法
                        BtnTestData_Click(null, null);
                    }
                    else
                    {
                        MessageBox.Show("请确保导入的文件包含数据行。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }

                // 加载列名
                LoadColumnNames();

                // 显示数据加载成功信息
                MessageBox.Show($"数据结构加载成功\n\n列数: {_sourceData.Columns.Count}\n行数: {_sourceData.Rows.Count}",
                    "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading data from file: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"加载数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 从CSV文件加载数据
        private void LoadCsvData(string filePath)
        {
            try
            {
                // 保存文件路径
                _lastLoadedFilePath = filePath;
                using (var reader = new System.IO.StreamReader(filePath))
                {
                    // 读取所有行
                    List<string> allLines = new List<string>();
                    while (!reader.EndOfStream)
                    {
                        string line = reader.ReadLine();
                        if (!string.IsNullOrEmpty(line))
                        {
                            allLines.Add(line);
                        }
                    }

                    if (allLines.Count == 0) return;
                    if (allLines.Count == 1) // 只有一行，直接作为列名
                    {
                        string headerLine = allLines[0];
                        string[] headers = headerLine.Split(',');

                        // 创建列
                        foreach (string header in headers)
                        {
                            string columnName = header.Trim('"').Trim();
                            if (!string.IsNullOrWhiteSpace(columnName))
                            {
                                _sourceData.Columns.Add(columnName, typeof(double));
                            }
                        }
                        return; // 没有数据行，直接返回
                    }

                    // 默认使用第一行作为列名，从第二行开始读取数据
                    int headerIndex = 0;
                    int dataStartIndex = 1;

                    // 尝试查找数据行的开始位置
                    bool foundDataRow = false;
                    for (int i = 1; i < allLines.Count; i++)
                    {
                        string line = allLines[i];
                        string[] values = line.Split(',');
                        int numericCount = 0;
                        int totalNonEmptyCount = 0;

                        // 检查这一行中数字的比例
                        foreach (string value in values)
                        {
                            string trimmedValue = value.Trim('"').Trim();
                            if (!string.IsNullOrWhiteSpace(trimmedValue))
                            {
                                totalNonEmptyCount++;
                                if (double.TryParse(trimmedValue, out _))
                                {
                                    numericCount++;
                                }
                            }
                        }

                        // 如果这一行中非空值的至少有80%是数字，则认为是数据行
                        if (totalNonEmptyCount > 0 && (double)numericCount / totalNonEmptyCount >= 0.8)
                        {
                            dataStartIndex = i;
                            headerIndex = i - 1; // 列名在数据行之前的一行
                            foundDataRow = true;
                            break;
                        }
                    }

                    // 如果没有找到符合条件的数据行，使用默认值
                    if (!foundDataRow && allLines.Count > 1)
                    {
                        // 输出调试信息
                        System.Diagnostics.Debug.WriteLine("No data row found, using default values");
                    }

                    // 获取列名行
                    string headerLineText = allLines[headerIndex];
                    string[] headerTexts = headerLineText.Split(',');

                    // 创建列
                    foreach (string header in headerTexts)
                    {
                        string columnName = header.Trim('"').Trim();
                        if (!string.IsNullOrWhiteSpace(columnName))
                        {
                            _sourceData.Columns.Add(columnName, typeof(double));
                        }
                    }

                    // 输出调试信息
                    System.Diagnostics.Debug.WriteLine($"Header index: {headerIndex}, Data start index: {dataStartIndex}");
                    System.Diagnostics.Debug.WriteLine($"Columns found: {_sourceData.Columns.Count}");

                    // 读取数据行
                    for (int i = dataStartIndex; i < allLines.Count; i++)
                    {
                        string dataLine = allLines[i];
                        string[] values = dataLine.Split(',');
                        DataRow row = _sourceData.NewRow();

                        for (int j = 0; j < values.Length && j < _sourceData.Columns.Count; j++)
                        {
                            string value = values[j].Trim('"').Trim();
                            if (double.TryParse(value, out double numValue))
                            {
                                row[j] = numValue;
                            }
                            else
                            {
                                row[j] = DBNull.Value;
                            }
                        }

                        _sourceData.Rows.Add(row);
                    }

                    // 输出调试信息
                    System.Diagnostics.Debug.WriteLine($"Rows loaded: {_sourceData.Rows.Count}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in LoadFromCsv: {ex.Message}");
                MessageBox.Show($"加载CSV文件时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 从Excel文件加载数据
        private DataTable LoadExcelData(string filePath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"开始加载Excel文件: {filePath}");
                DataTable dataTable = new DataTable();

                // 创建Excel工作簿
                IWorkbook workbook;
                using (FileStream file = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    // 根据文件扩展名确定Excel版本
                    if (Path.GetExtension(filePath).ToLower() == ".xlsx")
                        workbook = new XSSFWorkbook(file);
                    else
                        workbook = new HSSFWorkbook(file);
                }

                // 获取第一个工作表
                ISheet sheet = workbook.GetSheetAt(0);
                System.Diagnostics.Debug.WriteLine($"读取工作表: {sheet.SheetName}");

                // 获取行数和列数
                int rowCount = sheet.LastRowNum + 1;
                int columnCount = 0;

                // 找出最大列数
                for (int i = 0; i < rowCount; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row != null && row.LastCellNum > columnCount)
                        columnCount = row.LastCellNum;
                }

                System.Diagnostics.Debug.WriteLine($"Excel文件行数: {rowCount}, 列数: {columnCount}");

                // 尝试自动检测表头行
                int headerRowIndex = DetectHeaderRow(sheet);
                System.Diagnostics.Debug.WriteLine($"检测到表头行索引: {headerRowIndex}");

                // 获取列名
                IRow headerRow = sheet.GetRow(headerRowIndex);
                if (headerRow != null)
                {
                    for (int i = 0; i < columnCount; i++)
                    {
                        ICell cell = headerRow.GetCell(i);
                        string columnName = cell?.ToString() ?? $"Column{i + 1}";
                        columnName = columnName.Trim();

                        if (!string.IsNullOrWhiteSpace(columnName))
                        {
                            // 检查列名是否已存在，如果存在则添加后缀
                            if (dataTable.Columns.Contains(columnName))
                            {
                                int suffix = 1;
                                string newColumnName = $"{columnName}_{suffix}";
                                while (dataTable.Columns.Contains(newColumnName))
                                {
                                    suffix++;
                                    newColumnName = $"{columnName}_{suffix}";
                                }
                                columnName = newColumnName;
                            }

                            dataTable.Columns.Add(columnName, typeof(string));
                            System.Diagnostics.Debug.WriteLine($"添加列名: {columnName}");
                        }
                        else
                        {
                            // 对于空列名，使用默认名称
                            string defaultColumnName = $"Column{i + 1}";
                            dataTable.Columns.Add(defaultColumnName, typeof(string));
                            System.Diagnostics.Debug.WriteLine($"添加默认列名: {defaultColumnName}");
                        }
                    }
                }
                else
                {
                    // 如果没有列名行，使用默认列名
                    for (int i = 0; i < columnCount; i++)
                    {
                        dataTable.Columns.Add($"Column{i + 1}", typeof(string));
                    }
                    System.Diagnostics.Debug.WriteLine("未找到表头行，使用默认列名");
                }

                // 从表头行之后开始读取数据
                int dataStartRowIndex = headerRowIndex + 1;
                int validRowCount = 0;

                for (int i = dataStartRowIndex; i < rowCount; i++)
                {
                    IRow row = sheet.GetRow(i);
                    if (row != null)
                    {
                        DataRow dataRow = dataTable.NewRow();
                        bool hasData = false;

                        for (int j = 0; j < dataTable.Columns.Count; j++)
                        {
                            ICell cell = row.GetCell(j);
                            if (cell != null)
                            {
                                string cellValue = cell.ToString();
                                dataRow[j] = cellValue;

                                if (!string.IsNullOrWhiteSpace(cellValue))
                                    hasData = true;
                            }
                        }

                        if (hasData)
                        {
                            dataTable.Rows.Add(dataRow);
                            validRowCount++;
                        }
                    }
                }
                System.Diagnostics.Debug.WriteLine($"添加了 {validRowCount} 行有效数据");

                // 关闭工作簿
                workbook.Close();

                // 验证数据表完整性
                ValidateDataTable(dataTable);

                // 处理空值和无效数据
                ProcessNullValues(dataTable);

                // 加载完成后输出所有列名
                PrintAllColumnNames(dataTable);

                System.Diagnostics.Debug.WriteLine($"Excel数据加载完成: {dataTable.Rows.Count}行, {dataTable.Columns.Count}列");
                return dataTable;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载Excel数据时出错: {ex.Message}\n{ex.StackTrace}");
                throw;
            }
        }

        // 自动检测表头行
        private int DetectHeaderRow(ISheet sheet)
        {
            System.Diagnostics.Debug.WriteLine("开始检测表头行...");

            // 优先查找包含深度关键词的行
            for (int i = 0; i < Math.Min(5, sheet.LastRowNum + 1); i++)
            {
                IRow row = sheet.GetRow(i);
                if (row == null) continue;

                for (int j = 0; j < row.LastCellNum; j++)
                {
                    ICell cell = row.GetCell(j);
                    if (cell == null) continue;

                    string cellValue = cell.ToString().Trim();
                    if (cellValue.Contains("顶深") || cellValue.Contains("底深") ||
                        cellValue.Contains("顶深度") || cellValue.Contains("底深度"))
                    {
                        System.Diagnostics.Debug.WriteLine($"检测到表头行 {i}：包含深度关键词 '{cellValue}' 在列 {j}");
                        return i;
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine("未找到包含深度关键词的行，使用原检测逻辑");

            // 默认使用第一行作为表头
            int headerRowIndex = 0;

            // 检查前10行，找到最可能的表头行
            for (int i = 0; i < Math.Min(10, sheet.LastRowNum + 1); i++)
            {
                IRow row = sheet.GetRow(i);
                if (row == null) continue;

                // 计算非空单元格数量
                int nonEmptyCells = 0;
                int numericCells = 0;

                for (int j = 0; j < row.LastCellNum; j++)
                {
                    ICell cell = row.GetCell(j);
                    if (cell != null && !string.IsNullOrWhiteSpace(cell.ToString()))
                    {
                        nonEmptyCells++;

                        // 检查是否为数字
                        if (cell.CellType == CellType.Numeric ||
                            (cell.CellType == CellType.String && double.TryParse(cell.ToString(), out _)))
                        {
                            numericCells++;
                        }
                    }
                }

                // 如果这一行有足够的非空单元格，且大部分不是数字，可能是表头行
                if (nonEmptyCells > 0 && (double)numericCells / nonEmptyCells < 0.5)
                {
                    headerRowIndex = i;

                    // 检查下一行是否全是数字，如果是，更有可能当前行是表头
                    if (i < sheet.LastRowNum)
                    {
                        IRow nextRow = sheet.GetRow(i + 1);
                        if (nextRow != null)
                        {
                            int nextRowNumericCells = 0;
                            int nextRowNonEmptyCells = 0;

                            for (int j = 0; j < nextRow.LastCellNum; j++)
                            {
                                ICell cell = nextRow.GetCell(j);
                                if (cell != null && !string.IsNullOrWhiteSpace(cell.ToString()))
                                {
                                    nextRowNonEmptyCells++;

                                    if (cell.CellType == CellType.Numeric ||
                                        (cell.CellType == CellType.String && double.TryParse(cell.ToString(), out _)))
                                    {
                                        nextRowNumericCells++;
                                    }
                                }
                            }

                            if (nextRowNonEmptyCells > 0 && (double)nextRowNumericCells / nextRowNonEmptyCells > 0.7)
                            {
                                // 下一行大部分是数字，当前行很可能是表头
                                System.Diagnostics.Debug.WriteLine($"行 {i} 可能是表头，因为下一行大部分是数字");
                                return i;
                            }
                        }
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"使用行 {headerRowIndex} 作为表头");
            return headerRowIndex;
        }

        // 验证数据表完整性
        private void ValidateDataTable(DataTable dataTable)
        {
            if (dataTable == null || dataTable.Columns.Count == 0)
            {
                throw new Exception("数据表为空或没有列");
            }

            System.Diagnostics.Debug.WriteLine("验证数据表完整性...");
            System.Diagnostics.Debug.WriteLine("当前所有列名:");
            foreach (DataColumn col in dataTable.Columns)
            {
                System.Diagnostics.Debug.WriteLine($"  - {col.ColumnName}");
            }

            // 模糊匹配深度列
            bool foundTop = false;
            bool foundBottom = false;

            // 顶深和底深的可能名称
            string[] topDepthKeywords = new string[] { "顶深", "顶深度", "深度上限", "上限深度", "深度起点" };
            string[] bottomDepthKeywords = new string[] { "底深", "底深度", "深度下限", "下限深度", "深度终点" };

            // 查找顶深列
            foreach (DataColumn col in dataTable.Columns)
            {
                // 检查顶深
                foreach (string keyword in topDepthKeywords)
                {
                    if (col.ColumnName.Contains(keyword))
                    {
                        topDepthColumnName = col.ColumnName;
                        topDepthIndex = dataTable.Columns.IndexOf(col);
                        foundTop = true;
                        System.Diagnostics.Debug.WriteLine($"找到顶深列: {col.ColumnName}, 索引: {topDepthIndex}");
                        break;
                    }
                }
                if (foundTop) break;
            }

            // 查找底深列
            foreach (DataColumn col in dataTable.Columns)
            {
                // 检查底深
                foreach (string keyword in bottomDepthKeywords)
                {
                    if (col.ColumnName.Contains(keyword))
                    {
                        bottomDepthColumnName = col.ColumnName;
                        bottomDepthIndex = dataTable.Columns.IndexOf(col);
                        foundBottom = true;
                        System.Diagnostics.Debug.WriteLine($"找到底深列: {col.ColumnName}, 索引: {bottomDepthIndex}");
                        break;
                    }
                }
                if (foundBottom) break;
            }

            // 如果未找到，尝试自动修复默认列名
            bool autoFixed = false;

            // 尝试查找可能的数值列
            List<int> numericColumnIndices = new List<int>();
            if (!foundTop || !foundBottom)
            {
                System.Diagnostics.Debug.WriteLine("未找到深度列，尝试识别数值列...");

                // 查找前几行都是数值的列
                for (int colIndex = 0; colIndex < dataTable.Columns.Count; colIndex++)
                {
                    int numericCount = 0;
                    int rowsToCheck = Math.Min(5, dataTable.Rows.Count);

                    for (int rowIndex = 0; rowIndex < rowsToCheck; rowIndex++)
                    {
                        string cellValue = dataTable.Rows[rowIndex][colIndex].ToString();
                        if (double.TryParse(cellValue, out _))
                        {
                            numericCount++;
                        }
                    }

                    // 如果大部分行都是数值，可能是深度列
                    if (numericCount >= rowsToCheck * 0.8)
                    {
                        numericColumnIndices.Add(colIndex);
                        System.Diagnostics.Debug.WriteLine($"列 {colIndex} ({dataTable.Columns[colIndex].ColumnName}) 可能是数值列");
                    }
                }
            }

            // 如果找到了数值列，尝试自动修复
            if (numericColumnIndices.Count >= 2 && (!foundTop || !foundBottom))
            {
                // 假设前两个数值列是顶深和底深
                if (!foundTop)
                {
                    int colIndex = numericColumnIndices[0];
                    string oldName = dataTable.Columns[colIndex].ColumnName;
                    dataTable.Columns[colIndex].ColumnName = "顶深";
                    topDepthColumnName = "顶深";
                    topDepthIndex = colIndex;
                    foundTop = true;
                    autoFixed = true;
                    System.Diagnostics.Debug.WriteLine($"自动修复: 将列 {colIndex} 从 '{oldName}' 重命名为 '顶深'");
                }

                if (!foundBottom)
                {
                    int colIndex = numericColumnIndices[foundTop ? 1 : 0];
                    string oldName = dataTable.Columns[colIndex].ColumnName;
                    dataTable.Columns[colIndex].ColumnName = "底深";
                    bottomDepthColumnName = "底深";
                    bottomDepthIndex = colIndex;
                    foundBottom = true;
                    autoFixed = true;
                    System.Diagnostics.Debug.WriteLine($"自动修复: 将列 {colIndex} 从 '{oldName}' 重命名为 '底深'");
                }
            }

            // 如果自动修复成功，显示提示
            if (autoFixed)
            {
                string msg = $"已自动修复列名：\n顶深列: {topDepthColumnName} (索引: {topDepthIndex})\n底深列: {bottomDepthColumnName} (索引: {bottomDepthIndex})";
                System.Diagnostics.Debug.WriteLine(msg);
                MessageBox.Show(msg, "列名修复", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }

            // 如果仍然没有找到必需列，显示警告
            List<string> missingColumns = new List<string>();
            if (!foundTop) missingColumns.Add("顶深");
            if (!foundBottom) missingColumns.Add("底深");

            if (missingColumns.Count > 0)
            {
                string message = $"数据表缺少以下必需列:\n{string.Join("\n", missingColumns)}\n\n请确保数据表包含这些列，或者使用\"映射列名\"功能手动指定。";
                System.Diagnostics.Debug.WriteLine(message);

                // 提示用户手动映射列名
                DialogResult result = MessageBox.Show(message + "\n\n是否要手动映射列名？",
                    "缺少必需列", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    // 显示列名映射对话框
                    ShowColumnMappingDialog(dataTable);
                }
            }

            System.Diagnostics.Debug.WriteLine("数据表验证完成");
        }

        // 显示列名映射对话框
        private void ShowColumnMappingDialog(DataTable dataTable)
        {
            try
            {
                // 创建一个简单的表单用于映射列名
                Form mappingForm = new Form
                {
                    Text = "列名映射",
                    Size = new Size(400, 250),
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    StartPosition = FormStartPosition.CenterParent,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                // 创建标签和下拉框
                Label lblTopDepth = new Label
                {
                    Text = "选择顶深列:",
                    Location = new Point(20, 20),
                    Size = new Size(100, 20)
                };

                ComboBox cboTopDepth = new ComboBox
                {
                    Location = new Point(130, 20),
                    Size = new Size(200, 20),
                    DropDownStyle = ComboBoxStyle.DropDownList
                };

                Label lblBottomDepth = new Label
                {
                    Text = "选择底深列:",
                    Location = new Point(20, 60),
                    Size = new Size(100, 20)
                };

                ComboBox cboBottomDepth = new ComboBox
                {
                    Location = new Point(130, 60),
                    Size = new Size(200, 20),
                    DropDownStyle = ComboBoxStyle.DropDownList
                };

                // 添加所有列名到下拉框
                foreach (DataColumn col in dataTable.Columns)
                {
                    cboTopDepth.Items.Add(col.ColumnName);
                    cboBottomDepth.Items.Add(col.ColumnName);
                }

                // 如果已经有选择，设置为默认值
                if (!string.IsNullOrEmpty(topDepthColumnName) && cboTopDepth.Items.Contains(topDepthColumnName))
                {
                    cboTopDepth.SelectedItem = topDepthColumnName;
                }

                if (!string.IsNullOrEmpty(bottomDepthColumnName) && cboBottomDepth.Items.Contains(bottomDepthColumnName))
                {
                    cboBottomDepth.SelectedItem = bottomDepthColumnName;
                }

                // 添加确定和取消按钮
                Button btnOK = new Button
                {
                    Text = "确定",
                    DialogResult = DialogResult.OK,
                    Location = new Point(130, 150),
                    Size = new Size(80, 30)
                };

                Button btnCancel = new Button
                {
                    Text = "取消",
                    DialogResult = DialogResult.Cancel,
                    Location = new Point(250, 150),
                    Size = new Size(80, 30)
                };

                // 添加控件到表单
                mappingForm.Controls.Add(lblTopDepth);
                mappingForm.Controls.Add(cboTopDepth);
                mappingForm.Controls.Add(lblBottomDepth);
                mappingForm.Controls.Add(cboBottomDepth);
                mappingForm.Controls.Add(btnOK);
                mappingForm.Controls.Add(btnCancel);

                // 显示对话框
                if (mappingForm.ShowDialog() == DialogResult.OK)
                {
                    // 更新列名和索引
                    if (cboTopDepth.SelectedItem != null)
                    {
                        topDepthColumnName = cboTopDepth.SelectedItem.ToString();
                        topDepthIndex = dataTable.Columns.IndexOf(dataTable.Columns[topDepthColumnName]);
                        System.Diagnostics.Debug.WriteLine($"手动映射顶深列: {topDepthColumnName}, 索引: {topDepthIndex}");
                    }

                    if (cboBottomDepth.SelectedItem != null)
                    {
                        bottomDepthColumnName = cboBottomDepth.SelectedItem.ToString();
                        bottomDepthIndex = dataTable.Columns.IndexOf(dataTable.Columns[bottomDepthColumnName]);
                        System.Diagnostics.Debug.WriteLine($"手动映射底深列: {bottomDepthColumnName}, 索引: {bottomDepthIndex}");
                    }

                    // 显示确认消息
                    MessageBox.Show($"列名映射已更新:\n顶深列: {topDepthColumnName}\n底深列: {bottomDepthColumnName}",
                        "映射成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"显示列名映射对话框时出错: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"显示列名映射对话框时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 处理空值和无效数据
        private void ProcessNullValues(DataTable dataTable)
        {
            if (dataTable == null || dataTable.Rows.Count == 0)
                return;

            System.Diagnostics.Debug.WriteLine("处理空值和无效数据...");
            int nullValueCount = 0;

            foreach (DataRow row in dataTable.Rows)
            {
                foreach (DataColumn column in dataTable.Columns)
                {
                    if (row[column] == DBNull.Value || string.IsNullOrWhiteSpace(row[column].ToString()))
                    {
                        row[column] = 0;
                        nullValueCount++;
                    }
                    else if (column.DataType == typeof(string))
                    {
                        string value = row[column].ToString();
                        double result;
                        if (!double.TryParse(value, out result))
                        {
                            // 尝试清理字符串
                            value = value.Replace("%", "").Trim();
                            if (double.TryParse(value, out result))
                            {
                                row[column] = result;
                            }
                            else
                            {
                                row[column] = 0;
                                nullValueCount++;
                                System.Diagnostics.Debug.WriteLine($"无法解析值: {value}，已替换为0");
                            }
                        }
                    }
                }
            }

            System.Diagnostics.Debug.WriteLine($"处理了 {nullValueCount} 个空值或无效数据");
        }



        // 从截图中添加示例数据
        private void AddSampleDataFromScreenshot()
        {
            try
            {
                // 确保数据表已创建
                if (_sourceData == null)
                {
                    _sourceData = new DataTable();
                }

                // 如果没有列，添加必要的列
                if (_sourceData.Columns.Count == 0)
                {
                    _sourceData.Columns.Add("黏土矿物总量%", typeof(double));
                    _sourceData.Columns.Add("石英%", typeof(double));
                    _sourceData.Columns.Add("白云石%", typeof(double));
                    _sourceData.Columns.Add("菱铁矿%", typeof(double));
                    _sourceData.Columns.Add("斜长石%", typeof(double));
                    _sourceData.Columns.Add("钾长石（正长石）%", typeof(double));
                    _sourceData.Columns.Add("脆性矿物含量/%", typeof(double));
                    _sourceData.Columns.Add("高岭石(K)", typeof(double));
                    _sourceData.Columns.Add("绿泥石(C)", typeof(double));
                    _sourceData.Columns.Add("伊利石(It)", typeof(double));
                    _sourceData.Columns.Add("伊/蒙间层矿物(I/S)", typeof(double));
                }

                // 添加截图中的数据
                DataRow row1 = _sourceData.NewRow();
                row1["黏土矿物总量%"] = 38.47;
                row1["石英%"] = 35.01;
                row1["白云石%"] = 0;
                row1["菱铁矿%"] = 0;
                row1["斜长石%"] = 19.25;
                row1["钾长石（正长石）%"] = 5.96;
                row1["脆性矿物含量/%"] = 60.22;
                row1["高岭石(K)"] = 5;
                row1["绿泥石(C)"] = 23;
                _sourceData.Rows.Add(row1);

                DataRow row2 = _sourceData.NewRow();
                row2["黏土矿物总量%"] = 31.44;
                row2["石英%"] = 31.26;
                row2["白云石%"] = 0;
                row2["菱铁矿%"] = 0;
                row2["斜长石%"] = 29.05;
                row2["钾长石（正长石）%"] = 8.25;
                row2["脆性矿物含量/%"] = 68.56;
                row2["高岭石(K)"] = 4;
                row2["绿泥石(C)"] = 12;
                _sourceData.Rows.Add(row2);

                DataRow row3 = _sourceData.NewRow();
                row3["黏土矿物总量%"] = 29;
                row3["石英%"] = 32.34;
                row3["白云石%"] = 11.06;
                row3["菱铁矿%"] = 0;
                row3["斜长石%"] = 11.1;
                row3["钾长石（正长石）%"] = 6.49;
                row3["脆性矿物含量/%"] = 60.99;
                row3["高岭石(K)"] = 3;
                row3["绿泥石(C)"] = 11;
                _sourceData.Rows.Add(row3);

                DataRow row4 = _sourceData.NewRow();
                row4["黏土矿物总量%"] = 40.7;
                row4["石英%"] = 31.42;
                row4["白云石%"] = 0;
                row4["菱铁矿%"] = 0;
                row4["斜长石%"] = 20.91;
                row4["钾长石（正长石）%"] = 6.97;
                row4["脆性矿物含量/%"] = 59.3;
                row4["高岭石(K)"] = 3;
                row4["绿泥石(C)"] = 11;
                _sourceData.Rows.Add(row4);

                DataRow row5 = _sourceData.NewRow();
                row5["黏土矿物总量%"] = 23.27;
                row5["石英%"] = 36.96;
                row5["白云石%"] = 10.63;
                row5["菱铁矿%"] = 0;
                row5["斜长石%"] = 29.13;
                row5["钾长石（正长石）%"] = 0;
                row5["脆性矿物含量/%"] = 76.72;
                row5["高岭石(K)"] = 4;
                row5["绿泥石(C)"] = 13;
                _sourceData.Rows.Add(row5);

                // 自动选择脆性和塑性矿物列
                _brittleColumns.Clear();
                _ductileColumns.Clear();
                lstBrittleColumns.Items.Clear();
                lstDuctileColumns.Items.Clear();

                // 脆性矿物：石英、长石类
                if (_sourceData.Columns.Contains("石英%"))
                {
                    _brittleColumns.Add("石英%");
                    lstBrittleColumns.Items.Add("石英%");
                }
                if (_sourceData.Columns.Contains("斜长石%"))
                {
                    _brittleColumns.Add("斜长石%");
                    lstBrittleColumns.Items.Add("斜长石%");
                }
                if (_sourceData.Columns.Contains("钾长石（正长石）%"))
                {
                    _brittleColumns.Add("钾长石（正长石）%");
                    lstBrittleColumns.Items.Add("钾长石（正长石）%");
                }

                // 塑性矿物：黏土、碳酸盐类
                if (_sourceData.Columns.Contains("黏土矿物总量%"))
                {
                    _ductileColumns.Add("黏土矿物总量%");
                    lstDuctileColumns.Items.Add("黏土矿物总量%");
                }
                if (_sourceData.Columns.Contains("白云石%"))
                {
                    _ductileColumns.Add("白云石%");
                    lstDuctileColumns.Items.Add("白云石%");
                }
                if (_sourceData.Columns.Contains("菱铁矿%"))
                {
                    _ductileColumns.Add("菱铁矿%");
                    lstDuctileColumns.Items.Add("菱铁矿%");
                }

                // 更新可用列列表
                LoadColumnNames();

                MessageBox.Show($"已成功添加 {_sourceData.Rows.Count} 行示例数据。", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加示例数据时出错: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"添加示例数据时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 双击可用列事件
        private void lstAvailableColumns_DoubleClick(object sender, EventArgs e)
        {
            if (lstAvailableColumns.SelectedItem != null)
            {
                string columnName = lstAvailableColumns.SelectedItem.ToString();

                if (!string.IsNullOrEmpty(columnName))
                {
                    if (ModifierKeys == Keys.Control)
                    {
                        // 添加到塑性列表
                        _ductileColumns.Add(columnName);
                        lstDuctileColumns.Items.Add(columnName);
                        lstAvailableColumns.Items.Remove(lstAvailableColumns.SelectedItem);
                        System.Diagnostics.Debug.WriteLine($"双击添加塑性矿物: {columnName}");

                        // 立即高亮显示选中的列
                        if (dgvResult.DataSource == _sourceData)
                        {
                            HighlightSelectedColumns();
                        }
                    }
                    else
                    {
                        // 添加到脆性列表
                        _brittleColumns.Add(columnName);
                        lstBrittleColumns.Items.Add(columnName);
                        lstAvailableColumns.Items.Remove(lstAvailableColumns.SelectedItem);
                        System.Diagnostics.Debug.WriteLine($"双击添加脆性矿物: {columnName}");

                        // 立即高亮显示选中的列
                        if (dgvResult.DataSource == _sourceData)
                        {
                            HighlightSelectedColumns();
                        }
                    }
                }
            }
        }

        // 添加脆性矿物按钮事件
        private void btnAddBrittle_Click(object sender, EventArgs e)
        {
            try
            {
                if (lstAvailableColumns.SelectedItem != null)
                {
                    string selectedColumn = lstAvailableColumns.SelectedItem.ToString();

                    // 检查是否已在脆性列表中
                    if (!_brittleColumns.Contains(selectedColumn))
                    {
                        // 如果在塑性列表中，先移除
                        if (_ductileColumns.Contains(selectedColumn))
                        {
                            _ductileColumns.Remove(selectedColumn);
                            lstDuctileColumns.Items.Remove(selectedColumn);
                        }

                        // 添加到脆性列表
                        _brittleColumns.Add(selectedColumn);
                        lstBrittleColumns.Items.Add(selectedColumn);

                        System.Diagnostics.Debug.WriteLine($"已添加脆性矿物: {selectedColumn}");

                        // 立即高亮显示选中的列
                        if (dgvResult.DataSource == _sourceData)
                        {
                            HighlightSelectedColumns();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加脆性矿物时出错: {ex.Message}");
            }
        }

        // 添加塑性矿物按钮事件
        private void btnAddDuctile_Click(object sender, EventArgs e)
        {
            try
            {
                if (lstAvailableColumns.SelectedItem != null)
                {
                    string selectedColumn = lstAvailableColumns.SelectedItem.ToString();

                    // 添加到塑性矿物列表
                    if (!_ductileColumns.Contains(selectedColumn))
                    {
                        _ductileColumns.Add(selectedColumn);
                        lstDuctileColumns.Items.Add(selectedColumn);

                        // 从可用列表中移除
                        lstAvailableColumns.Items.Remove(selectedColumn);

                        System.Diagnostics.Debug.WriteLine($"已添加塑性矿物: {selectedColumn}");

                        // 立即高亮显示选中的列
                        HighlightSelectedColumns();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加塑性矿物时出错: {ex.Message}");
            }
        }

        // 移除脆性矿物按钮事件
        private void btnRemoveBrittle_Click(object sender, EventArgs e)
        {
            try
            {
                if (lstBrittleColumns.SelectedItem != null)
                {
                    string selectedColumn = lstBrittleColumns.SelectedItem.ToString();

                    // 从脆性矿物列表中移除
                    _brittleColumns.Remove(selectedColumn);
                    lstBrittleColumns.Items.Remove(selectedColumn);

                    // 添加回可用列表
                    if (!lstAvailableColumns.Items.Contains(selectedColumn))
                    {
                        lstAvailableColumns.Items.Add(selectedColumn);
                    }

                    System.Diagnostics.Debug.WriteLine($"已移除脆性矿物: {selectedColumn}");

                    // 立即高亮显示选中的列
                    HighlightSelectedColumns();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"移除脆性矿物时出错: {ex.Message}");
            }
        }

        // 移除塑性矿物按钮事件
        private void btnRemoveDuctile_Click(object sender, EventArgs e)
        {
            try
            {
                if (lstDuctileColumns.SelectedItem != null)
                {
                    string selectedColumn = lstDuctileColumns.SelectedItem.ToString();

                    // 从塑性矿物列表中移除
                    _ductileColumns.Remove(selectedColumn);
                    lstDuctileColumns.Items.Remove(selectedColumn);

                    // 添加回可用列表
                    if (!lstAvailableColumns.Items.Contains(selectedColumn))
                    {
                        lstAvailableColumns.Items.Add(selectedColumn);
                    }

                    System.Diagnostics.Debug.WriteLine($"已移除塑性矿物: {selectedColumn}");

                    // 立即高亮显示选中的列
                    HighlightSelectedColumns();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"移除塑性矿物时出错: {ex.Message}");
            }
        }



        // 初始化行选择下拉框
        private void InitializeRowSelector()
        {
            try
            {
                // 创建下拉框
                cboRowSelector = new ComboBox();
                cboRowSelector.DropDownStyle = ComboBoxStyle.DropDownList;
                cboRowSelector.Font = new Font("微软雅黑", 9F);
                cboRowSelector.FormattingEnabled = true;
                cboRowSelector.Name = "cboRowSelector";
                cboRowSelector.Size = new Size(80, 25);

                // 添加到窗体
                this.Controls.Add(cboRowSelector);

                // 检查leftPanel是否已初始化
                if (leftPanel != null)
                {
                    // 将下拉框移动到左侧面板
                    cboRowSelector.Parent = leftPanel;

                    // 检查lblAvailable是否已初始化
                    if (lblAvailable != null)
                    {
                        // 设置位置（在lblAvailable旁边）
                        cboRowSelector.Location = new Point(lblAvailable.Right + 10, lblAvailable.Top);
                    }
                    else
                    {
                        // 如果lblAvailable未初始化，使用默认位置
                        cboRowSelector.Location = new Point(100, 24);
                        System.Diagnostics.Debug.WriteLine("lblAvailable未初始化，使用默认位置");
                    }
                }
                else
                {
                    // 如果leftPanel未初始化，使用默认位置
                    cboRowSelector.Location = new Point(100, 24);
                    System.Diagnostics.Debug.WriteLine("leftPanel未初始化，使用默认位置");
                }

                // 添加事件处理
                cboRowSelector.SelectedIndexChanged += CboRowSelector_SelectedIndexChanged;

                // 更新下拉框选项
                UpdateRowSelectorItems();

                System.Diagnostics.Debug.WriteLine("行选择下拉框初始化成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化行选择下拉框时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }

        // 更新行选择下拉框的选项
        private void UpdateRowSelectorItems()
        {
            try
            {
                // 检查cboRowSelector是否已初始化
                if (cboRowSelector == null)
                {
                    System.Diagnostics.Debug.WriteLine("行选择下拉框未初始化");
                    return;
                }

                // 保存当前选中项
                object selectedItem = cboRowSelector.SelectedItem;

                // 清空下拉框
                cboRowSelector.Items.Clear();

                // 添加行选项（从第1行开始，而不是从数据行开始）
                // 固定添加前4行（表头行）
                for (int i = 0; i < 4; i++)
                {
                    cboRowSelector.Items.Add($"第{i + 1}行");
                }

                // 如果有数据行，继续添加
                if (_sourceData != null && _sourceData.Rows.Count > 0)
                {
                    for (int i = 0; i < _sourceData.Rows.Count; i++)
                    {
                        cboRowSelector.Items.Add($"第{i + 5}行"); // 从第5行开始是数据行
                    }
                }

                // 恢复选中项，如果不存在则选择第一项
                if (selectedItem != null && cboRowSelector.Items.Contains(selectedItem))
                {
                    cboRowSelector.SelectedItem = selectedItem;
                }
                else if (cboRowSelector.Items.Count > 0)
                {
                    cboRowSelector.SelectedIndex = 0;
                }

                System.Diagnostics.Debug.WriteLine($"更新行选择下拉框，共{cboRowSelector.Items.Count}项");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新行选择下拉框时出错: {ex.Message}");
            }
        }

        // 行选择下拉框选择变更事件
        private void CboRowSelector_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (cboRowSelector.SelectedIndex < 0)
                    return;

                // 获取选中的行索引
                int selectedIndex = cboRowSelector.SelectedIndex;

                System.Diagnostics.Debug.WriteLine($"选择了第{selectedIndex + 1}行");

                // 根据选择的行加载列名
                if (selectedIndex < 4) // 表头行（第1-4行）
                {
                    // 从Excel文件中加载指定行的列名
                    LoadHeaderRowAsColumns(selectedIndex);
                }
                else // 数据行（第5行及以后）
                {
                    // 从数据表中加载列名
                    int dataRowIndex = selectedIndex - 4; // 转换为数据表中的行索引
                    if (_sourceData != null && dataRowIndex < _sourceData.Rows.Count)
                    {
                        LoadDataRowAsColumns(dataRowIndex);

                        // 更新数据网格视图，显示选中的行
                        if (dgvResult.DataSource == _sourceData)
                        {
                            // 如果当前显示的是源数据，选中对应行
                            if (dgvResult.Rows.Count > dataRowIndex)
                            {
                                dgvResult.ClearSelection();
                                dgvResult.Rows[dataRowIndex].Selected = true;
                                dgvResult.FirstDisplayedScrollingRowIndex = dataRowIndex;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"行选择下拉框选择变更时出错: {ex.Message}");
            }
        }

        // 从Excel文件的表头行加载列名
        private void LoadHeaderRowAsColumns(int headerRowIndex)
        {
            try
            {
                // 清空可用列列表
                lstAvailableColumns.Items.Clear();

                // 获取Excel文件路径
                string excelFilePath = _lastLoadedFilePath;

                if (string.IsNullOrEmpty(excelFilePath) || !File.Exists(excelFilePath))
                {
                    MessageBox.Show("未找到Excel文件，请先导入数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 打开Excel文件
                IWorkbook workbook = null;
                using (FileStream fs = new FileStream(excelFilePath, FileMode.Open, FileAccess.Read))
                {
                    if (excelFilePath.EndsWith(".xlsx"))
                        workbook = new XSSFWorkbook(fs);
                    else if (excelFilePath.EndsWith(".xls"))
                        workbook = new HSSFWorkbook(fs);
                    else
                    {
                        MessageBox.Show("不支持的文件格式，请使用.xlsx或.xls格式。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                }

                // 获取第一个工作表
                ISheet sheet = workbook.GetSheetAt(0);

                // 获取指定的表头行
                IRow headerRow = sheet.GetRow(headerRowIndex);

                if (headerRow == null)
                {
                    MessageBox.Show($"未找到第{headerRowIndex + 1}行数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 遍历表头行的所有单元格
                for (int i = 0; i < headerRow.LastCellNum; i++)
                {
                    ICell cell = headerRow.GetCell(i);
                    if (cell != null)
                    {
                        string cellValue = cell.ToString();
                        if (!string.IsNullOrWhiteSpace(cellValue))
                        {
                            // 检查是否已经在脆性或塑性列表中
                            if (!_brittleColumns.Contains(cellValue) && !_ductileColumns.Contains(cellValue))
                            {
                                lstAvailableColumns.Items.Add(cellValue);
                                System.Diagnostics.Debug.WriteLine($"从第{headerRowIndex + 1}行添加列名: {cellValue}");
                            }
                        }
                    }
                }

                // 关闭工作簿
                workbook.Close();

                // 如果当前显示的是源数据表，立即高亮显示选中的列
                if (dgvResult.DataSource == _sourceData)
                {
                    HighlightSelectedColumns();
                    System.Diagnostics.Debug.WriteLine("加载列名后立即高亮显示选中的列");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从表头行加载列名时出错: {ex.Message}");
                MessageBox.Show($"从表头行加载列名时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 从数据表中的行加载列名
        private void LoadDataRowAsColumns(int dataRowIndex)
        {
            try
            {
                // 清空可用列列表
                lstAvailableColumns.Items.Clear();

                // 如果没有数据，直接返回
                if (_sourceData == null || _sourceData.Columns.Count == 0)
                    return;

                // 获取选中的行
                DataRow selectedRow = _sourceData.Rows[dataRowIndex];

                // 添加列到可用列列表（排除已经添加到脆性或塑性列表的列）
                foreach (DataColumn column in _sourceData.Columns)
                {
                    string columnName = column.ColumnName;

                    // 如果列名不在脆性或塑性列表中，添加到可用列表
                    if (!_brittleColumns.Contains(columnName) && !_ductileColumns.Contains(columnName))
                    {
                        // 显示列名和对应的值
                        object cellValue = selectedRow[columnName];
                        string displayText = $"{columnName} [{cellValue}]";
                        lstAvailableColumns.Items.Add(displayText);

                        System.Diagnostics.Debug.WriteLine($"添加列到可用列表: {displayText}");
                    }
                }

                // 如果当前显示的是源数据表，立即高亮显示选中的列
                if (dgvResult.DataSource == _sourceData)
                {
                    HighlightSelectedColumns();
                    System.Diagnostics.Debug.WriteLine("加载列名后立即高亮显示选中的列");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"从数据行加载列名时出错: {ex.Message}");
            }
        }

        // 辅助方法：从显示文本中提取列名
        private string ExtractColumnName(string displayText)
        {
            try
            {
                // 提取方括号前的列名部分
                int bracketIndex = displayText.IndexOf(" [");
                if (bracketIndex > 0)
                {
                    return displayText.Substring(0, bracketIndex);
                }

                // 如果没有方括号，返回整个文本
                return displayText;
            }
            catch
            {
                return displayText;
            }
        }

        // 加载数据列名并高亮显示选中列
        private void LoadColumnNamesAndHighlight()
        {
            try
            {
                // 清空列表
                lstAvailableColumns.Items.Clear();

                // 检查源数据表是否存在
                if (_sourceData == null || _sourceData.Columns.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("源数据表为空或没有列");
                    return;
                }

                // 创建列名映射字典，用于存储原始列名和显示列名的映射关系
                Dictionary<string, string> columnDisplayNames = new Dictionary<string, string>();

                // 添加列名到可用列表
                foreach (DataColumn column in _sourceData.Columns)
                {
                    string columnName = column.ColumnName;
                    string displayName = columnName;

                    // 检查是否有自定义标题
                    if (!string.IsNullOrEmpty(column.Caption) && column.Caption != columnName)
                    {
                        displayName = $"{columnName} ({column.Caption})";
                    }

                    // 添加到列表
                    lstAvailableColumns.Items.Add(displayName);

                    // 添加到映射字典
                    columnDisplayNames[displayName] = columnName;

                    System.Diagnostics.Debug.WriteLine($"列名: {columnName}, 显示名: {displayName}, 索引: {column.Ordinal}");
                }

                // 尝试智能匹配脆性矿物和塑性矿物列
                MatchMineralColumns();

                // 高亮显示选中列
                HighlightSelectedColumns();

                System.Diagnostics.Debug.WriteLine($"加载列名并高亮显示完成，可用列: {lstAvailableColumns.Items.Count}, 脆性矿物: {_brittleColumns.Count}, 塑性矿物: {_ductileColumns.Count}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载列名时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"加载列名时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }
        // 智能匹配矿物列
        private void MatchMineralColumns()
        {
            try
            {
                // 清空列名映射字典
                _columnMappings.Clear();

                // 创建矿物名称与可能的列名映射
                Dictionary<string, List<string>> mineralNameMappings = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase)
        {
            // 脆性矿物
            { "石英%", new List<string> { "石英", "石英含量", "石英%", "SiO2", "二氧化硅", "Quartz", "Column18", "Column19" } },
            { "白云石%", new List<string> { "白云石", "白云石含量", "白云石%", "Dolomite", "Column20", "Column21" } },
            { "菱铁矿%", new List<string> { "菱铁矿", "菱铁矿含量", "菱铁矿%", "Siderite", "Column22", "Column23" } },
            { "斜长石%", new List<string> { "斜长石", "斜长石含量", "斜长石%", "Plagioclase", "Column24", "Column25" } },
            
            // 塑性矿物
            { "黏土矿物总量%", new List<string> { "黏土矿物总量", "黏土矿物总量%", "黏土矿物", "黏土含量", "Clay", "Clay Minerals", "Column43", "Column44" } }
        };

                // 遍历源数据表的所有列
                foreach (DataColumn column in _sourceData.Columns)
                {
                    string columnName = column.ColumnName;
                    string columnCaption = column.Caption;

                    // 检查每个矿物名称
                    foreach (var mapping in mineralNameMappings)
                    {
                        string mineralName = mapping.Key;
                        List<string> possibleNames = mapping.Value;

                        // 检查列名或标题是否匹配任何可能的名称
                        bool isMatch = possibleNames.Any(name =>
                            columnName.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0 ||
                            (columnCaption != null && columnCaption.IndexOf(name, StringComparison.OrdinalIgnoreCase) >= 0));

                        if (isMatch)
                        {
                            // 添加到映射字典
                            _columnMappings[mineralName] = columnName;
                            System.Diagnostics.Debug.WriteLine($"找到矿物列匹配: {mineralName} -> {columnName}");

                            // 如果是脆性矿物，添加到脆性矿物列表
                            if (mineralName == "石英%" || mineralName == "白云石%" || mineralName == "菱铁矿%" || mineralName == "斜长石%")
                            {
                                if (!_brittleColumns.Contains(mineralName) && !lstBrittleColumns.Items.Contains(mineralName))
                                {
                                    _brittleColumns.Add(mineralName);
                                    lstBrittleColumns.Items.Add(mineralName);
                                    System.Diagnostics.Debug.WriteLine($"自动添加脆性矿物: {mineralName}");
                                }
                            }
                            // 如果是塑性矿物，添加到塑性矿物列表
                            else if (mineralName == "黏土矿物总量%")
                            {
                                if (!_ductileColumns.Contains(mineralName) && !lstDuctileColumns.Items.Contains(mineralName))
                                {
                                    _ductileColumns.Add(mineralName);
                                    lstDuctileColumns.Items.Add(mineralName);
                                    System.Diagnostics.Debug.WriteLine($"自动添加塑性矿物: {mineralName}");
                                }
                            }

                            break;
                        }
                    }
                }

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"矿物列匹配完成，共找到 {_columnMappings.Count} 个匹配");
                foreach (var mapping in _columnMappings)
                {
                    System.Diagnostics.Debug.WriteLine($"  - {mapping.Key} -> {mapping.Value}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"匹配矿物列时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }
        // 修改LoadColumnNames方法，调用新的组合方法
        private void LoadColumnNames()
        {
            LoadColumnNamesAndHighlight();
        }

        // 自动识别并添加常见的脆性和塑性矿物列
        // 自动识别并分类矿物列
        private void AutoClassifyMinerals()
        {
            try
            {
                // 清空现有分类
                _brittleColumns.Clear();
                _ductileColumns.Clear();
                lstBrittleColumns.Items.Clear();
                lstDuctileColumns.Items.Clear();

                // 遍历所有可用列
                foreach (string columnName in lstAvailableColumns.Items)
                {
                    bool classified = false;

                    // 检查是否为脆性矿物
                    foreach (var mapping in _globalBrittleMappings)
                    {
                        if (mapping.Value.Any(pattern => columnName.Contains(pattern, StringComparison.OrdinalIgnoreCase)))
                        {
                            if (!_brittleColumns.Contains(columnName))
                            {
                                _brittleColumns.Add(columnName);
                                lstBrittleColumns.Items.Add(columnName);
                                classified = true;
                                break;
                            }
                        }
                    }

                    // 如果未分类为脆性矿物，检查是否为塑性矿物
                    if (!classified)
                    {
                        foreach (var mapping in _globalDuctileMappings)
                        {
                            if (mapping.Value.Any(pattern => columnName.Contains(pattern, StringComparison.OrdinalIgnoreCase)))
                            {
                                if (!_ductileColumns.Contains(columnName))
                                {
                                    _ductileColumns.Add(columnName);
                                    lstDuctileColumns.Items.Add(columnName);
                                    classified = true;
                                    break;
                                }
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"自动分类完成: 脆性矿物 {_brittleColumns.Count} 个, 塑性矿物 {_ductileColumns.Count} 个");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自动分类矿物时出错: {ex.Message}");
            }
        }
        // 自动检测矿物列
        private void AutoDetectMineralColumns()
        {
            try
            {
                // 清空现有选择
                _brittleColumns.Clear();
                _ductileColumns.Clear();
                lstBrittleColumns.Items.Clear();
                lstDuctileColumns.Items.Clear();

                System.Diagnostics.Debug.WriteLine("开始自动检测矿物列...");

                // 检查每个全局脆性矿物映射
                foreach (var mapping in _globalBrittleMappings)
                {
                    string standardName = mapping.Key;
                    string actualName = FindActualColumnName(standardName, true);

                    if (actualName != null && !_brittleColumns.Contains(standardName))
                    {
                        _brittleColumns.Add(standardName);
                        lstBrittleColumns.Items.Add(standardName);
                        System.Diagnostics.Debug.WriteLine($"自动添加脆性矿物: {standardName} (实际列名: {actualName})");
                    }
                }

                // 检查每个全局塑性矿物映射
                foreach (var mapping in _globalDuctileMappings)
                {
                    string standardName = mapping.Key;
                    string actualName = FindActualColumnName(standardName, false);

                    if (actualName != null && !_ductileColumns.Contains(standardName))
                    {
                        _ductileColumns.Add(standardName);
                        lstDuctileColumns.Items.Add(standardName);
                        System.Diagnostics.Debug.WriteLine($"自动添加塑性矿物: {standardName} (实际列名: {actualName})");
                    }
                }

                // 自动检测顶深和底深列
                foreach (DataColumn column in _sourceData.Columns)
                {
                    string colName = column.ColumnName.ToLower();

                    if (colName.Contains("顶深"))
                    {
                        topDepthColumnName = column.ColumnName;
                        topDepthIndex = _sourceData.Columns.IndexOf(column);
                        lblTopDepth.Text = column.ColumnName;
                        lblTopDepth.ForeColor = Color.Green;
                        System.Diagnostics.Debug.WriteLine($"自动设置顶深列: {column.ColumnName}");
                    }
                    else if (colName.Contains("底深"))
                    {
                        bottomDepthColumnName = column.ColumnName;
                        bottomDepthIndex = _sourceData.Columns.IndexOf(column);
                        lblBottomDepth.Text = column.ColumnName;
                        lblBottomDepth.ForeColor = Color.Green;
                        System.Diagnostics.Debug.WriteLine($"自动设置底深列: {column.ColumnName}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"自动检测完成: 找到{_brittleColumns.Count}个脆性矿物和{_ductileColumns.Count}个塑性矿物");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自动检测矿物列时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }
        // 查找顶深和底深列
        private void FindDepthColumns()
        {
            try
            {
                // 如果已经找到了顶深和底深列，则不需要再次查找
                if (topDepthIndex >= 0 && bottomDepthIndex >= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"已经找到顶深列({topDepthColumnName})和底深列({bottomDepthColumnName})，不需要再次查找");
                    return;
                }

                if (_sourceData == null || _sourceData.Columns.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("数据表为空或没有列，无法查找深度列");
                    return;
                }

                // 定义可能的顶深和底深列名
                string[] topDepthKeywords = new string[] { "顶深", "顶深度", "Top Depth", "TopDepth" };
                string[] bottomDepthKeywords = new string[] { "底深", "底深度", "Bottom Depth", "BottomDepth" };

                // 查找顶深列
                if (topDepthIndex < 0)
                {
                    // 首先使用关键词直接查找
                    foreach (string keyword in topDepthKeywords)
                    {
                        for (int i = 0; i < _sourceData.Columns.Count; i++)
                        {
                            string columnName = _sourceData.Columns[i].ColumnName;
                            if (columnName.Contains(keyword))
                            {
                                topDepthIndex = i;
                                topDepthColumnName = columnName;
                                System.Diagnostics.Debug.WriteLine($"使用关键词 '{keyword}' 找到顶深列: {columnName}, 索引: {i}");
                                lblTopDepth.Text = topDepthColumnName;
                                lblTopDepth.ForeColor = Color.Green;
                                break;
                            }
                        }
                        if (topDepthIndex >= 0) break;
                    }

                    // 如果还是找不到，尝试使用映射查找
                    if (topDepthIndex < 0)
                    {
                        foreach (string keyword in topDepthKeywords)
                        {
                            if (_columnMappings.TryGetValue(keyword, out string mappedName))
                            {
                                for (int i = 0; i < _sourceData.Columns.Count; i++)
                                {
                                    string columnName = _sourceData.Columns[i].ColumnName;
                                    if (columnName.Contains(mappedName))
                                    {
                                        topDepthIndex = i;
                                        topDepthColumnName = columnName;
                                        System.Diagnostics.Debug.WriteLine($"使用映射 '{keyword}' -> '{mappedName}' 找到顶深列: {columnName}, 索引: {i}");
                                        lblTopDepth.Text = topDepthColumnName;
                                        lblTopDepth.ForeColor = Color.Green;
                                        break;
                                    }
                                }
                                if (topDepthIndex >= 0) break;
                            }
                        }
                    }
                }

                // 查找底深列
                if (bottomDepthIndex < 0)
                {
                    // 首先使用关键词直接查找
                    foreach (string keyword in bottomDepthKeywords)
                    {
                        for (int i = 0; i < _sourceData.Columns.Count; i++)
                        {
                            string columnName = _sourceData.Columns[i].ColumnName;
                            if (columnName.Contains(keyword))
                            {
                                bottomDepthIndex = i;
                                bottomDepthColumnName = columnName;
                                System.Diagnostics.Debug.WriteLine($"使用关键词 '{keyword}' 找到底深列: {columnName}, 索引: {i}");
                                lblBottomDepth.Text = bottomDepthColumnName;
                                lblBottomDepth.ForeColor = Color.Green;
                                break;
                            }
                        }
                        if (bottomDepthIndex >= 0) break;
                    }

                    // 如果还是找不到，尝试使用映射查找
                    if (bottomDepthIndex < 0)
                    {
                        foreach (string keyword in bottomDepthKeywords)
                        {
                            if (_columnMappings.TryGetValue(keyword, out string mappedName))
                            {
                                for (int i = 0; i < _sourceData.Columns.Count; i++)
                                {
                                    string columnName = _sourceData.Columns[i].ColumnName;
                                    if (columnName.Contains(mappedName))
                                    {
                                        bottomDepthIndex = i;
                                        bottomDepthColumnName = columnName;
                                        System.Diagnostics.Debug.WriteLine($"使用映射 '{keyword}' -> '{mappedName}' 找到底深列: {columnName}, 索引: {i}");
                                        lblBottomDepth.Text = bottomDepthColumnName;
                                        lblBottomDepth.ForeColor = Color.Green;
                                        break;
                                    }
                                }
                                if (bottomDepthIndex >= 0) break;
                            }
                        }
                    }
                }

                // 输出查找结果
                if (topDepthIndex >= 0)
                    System.Diagnostics.Debug.WriteLine($"最终选择的顶深列: {topDepthColumnName}, 索引: {topDepthIndex}");
                else
                    System.Diagnostics.Debug.WriteLine("未找到顶深列");

                if (bottomDepthIndex >= 0)
                    System.Diagnostics.Debug.WriteLine($"最终选择的底深列: {bottomDepthColumnName}, 索引: {bottomDepthIndex}");
                else
                    System.Diagnostics.Debug.WriteLine("未找到底深列");
            }
            catch (Exception ex)
            {
                // 使用调试输出替代弹窗
                System.Diagnostics.Debug.WriteLine($"查找深度列时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }

        // 自动分类列
        private void AutoCategorizeColumns()
        {
            // 脆性矿物关键词
            string[] brittleKeywords = new string[] {
                "壳质", "壳质组", "壳质组/%",
                "石英", "长石", "正长石", "斑状长石", "斑状正长石",
                "碳酸盐", "方角石", "白云石", "莱石", "菱镜石"
            };

            // 塑性矿物关键词
            string[] ductileKeywords = new string[] {
                "腐泥", "腐泥组", "腐泥组/%",
                "镜质", "镜质组", "镜质组/%",
                "惺质", "惺质组", "惺质组/%",
                "黏土", "黏土矿物", "伊利石", "蚀石", "高岭石"
            };

            // 清空当前选择
            _brittleColumns.Clear();
            _ductileColumns.Clear();
            lstBrittleColumns.Items.Clear();
            lstDuctileColumns.Items.Clear();

            // 遍历可用列
            foreach (var item in lstAvailableColumns.Items)
            {
                string columnName = item.ToString();

                // 检查是否包含脆性矿物关键词
                foreach (string keyword in brittleKeywords)
                {
                    if (columnName.Contains(keyword))
                    {
                        if (!_brittleColumns.Contains(columnName))
                        {
                            _brittleColumns.Add(columnName);
                            lstBrittleColumns.Items.Add(columnName);
                        }
                        break;
                    }
                }

                // 检查是否包含塑性矿物关键词
                foreach (string keyword in ductileKeywords)
                {
                    if (columnName.Contains(keyword))
                    {
                        if (!_ductileColumns.Contains(columnName))
                        {
                            _ductileColumns.Add(columnName);
                            lstDuctileColumns.Items.Add(columnName);
                        }
                        break;
                    }
                }
            }
        }

        // 移动选中列
        private void MoveSelected(ListBox source, ListBox target, List<string> targetList)
        {
            if (source.SelectedItems.Count == 0) return;

            List<object> selectedItems = new List<object>();
            foreach (var item in source.SelectedItems)
            {
                selectedItems.Add(item);
            }

            foreach (var item in selectedItems)
            {
                string itemText = item.ToString();
                if (!target.Items.Contains(itemText))
                {
                    target.Items.Add(itemText);
                    targetList.Add(itemText);

                    // 从源列表中移除项目
                    source.Items.Remove(itemText);
                }
            }
        }

        // 移除选中列，返回是否成功移除
        private bool RemoveSelected(ListBox source, List<string> sourceList)
        {
            if (source.SelectedItems.Count == 0) return false;

            List<object> selectedItems = new List<object>();
            foreach (var item in source.SelectedItems)
            {
                selectedItems.Add(item);
            }

            bool removed = false;
            foreach (var item in selectedItems)
            {
                string itemText = item.ToString();
                source.Items.Remove(itemText);
                sourceList.Remove(itemText);
                removed = true;

                // 将移除的项目添加回可用列表
                if (!lstAvailableColumns.Items.Contains(itemText))
                {
                    lstAvailableColumns.Items.Add(itemText);
                }

                System.Diagnostics.Debug.WriteLine($"已移除列: {itemText}");
            }

            return removed;
        }

        // 查找实际列名 - 单参数版本
        private string FindActualColumnName(string columnName)
        {
            // 移除列名中的%和空格，统一小写比较
            string cleanColumnName = columnName.Replace("%", "").Trim().ToLower();

            // 如果列名直接存在于数据源中，直接返回
            if (_sourceData.Columns.Contains(columnName))
            {
                return columnName;
            }

            // 尝试清理后的精确匹配
            foreach (DataColumn col in _sourceData.Columns)
            {
                string cleanDataCol = col.ColumnName.Replace("%", "").Trim().ToLower();
                if (cleanDataCol == cleanColumnName)
                {
                    System.Diagnostics.Debug.WriteLine($"通过清理匹配找到列: '{columnName}' -> '{col.ColumnName}'");
                    return col.ColumnName; // 返回实际列名
                }
            }

            // 检查映射字典
            if (_columnMappings.TryGetValue(columnName, out string mappedName))
            {
                if (_sourceData.Columns.Contains(mappedName))
                {
                    System.Diagnostics.Debug.WriteLine($"通过映射字典找到列: '{columnName}' -> '{mappedName}'");
                    return mappedName;
                }

                // 尝试清理后的映射匹配
                string cleanMappedName = mappedName.Replace("%", "").Trim().ToLower();
                foreach (DataColumn col in _sourceData.Columns)
                {
                    string cleanDataCol = col.ColumnName.Replace("%", "").Trim().ToLower();
                    if (cleanDataCol == cleanMappedName)
                    {
                        System.Diagnostics.Debug.WriteLine($"通过清理映射找到列: '{columnName}' -> '{mappedName}' -> '{col.ColumnName}'");
                        return col.ColumnName;
                    }
                }
            }

            // 尝试在全局映射中查找
            // 先检查脆性矿物映射
            foreach (var mapping in _globalBrittleMappings)
            {
                if (mapping.Value.Contains(columnName, StringComparer.OrdinalIgnoreCase))
                {
                    // 尝试找到数据源中存在的任何一个映射名
                    foreach (var possibleName in mapping.Value)
                    {
                        if (_sourceData.Columns.Contains(possibleName))
                        {
                            System.Diagnostics.Debug.WriteLine($"通过脆性全局映射找到列: '{columnName}' -> '{possibleName}'");
                            return possibleName;
                        }

                        // 尝试清理后的映射匹配
                        string cleanPossibleName = possibleName.Replace("%", "").Trim().ToLower();
                        foreach (DataColumn col in _sourceData.Columns)
                        {
                            string cleanDataCol = col.ColumnName.Replace("%", "").Trim().ToLower();
                            if (cleanDataCol == cleanPossibleName)
                            {
                                System.Diagnostics.Debug.WriteLine($"通过清理脆性全局映射找到列: '{columnName}' -> '{possibleName}' -> '{col.ColumnName}'");
                                return col.ColumnName;
                            }
                        }
                    }
                }
            }

            // 再检查塑性矿物映射
            foreach (var mapping in _globalDuctileMappings)
            {
                if (mapping.Value.Contains(columnName, StringComparer.OrdinalIgnoreCase))
                {
                    // 尝试找到数据源中存在的任何一个映射名
                    foreach (var possibleName in mapping.Value)
                    {
                        if (_sourceData.Columns.Contains(possibleName))
                        {
                            System.Diagnostics.Debug.WriteLine($"通过塑性全局映射找到列: '{columnName}' -> '{possibleName}'");
                            return possibleName;
                        }

                        // 尝试清理后的映射匹配
                        string cleanPossibleName = possibleName.Replace("%", "").Trim().ToLower();
                        foreach (DataColumn col in _sourceData.Columns)
                        {
                            string cleanDataCol = col.ColumnName.Replace("%", "").Trim().ToLower();
                            if (cleanDataCol == cleanPossibleName)
                            {
                                System.Diagnostics.Debug.WriteLine($"通过清理塑性全局映射找到列: '{columnName}' -> '{possibleName}' -> '{col.ColumnName}'");
                                return col.ColumnName;
                            }
                        }
                    }
                }
            }

            // 尝试模糊匹配 - 检查列名是否包含关键词
            foreach (DataColumn column in _sourceData.Columns)
            {
                string cleanDataCol = column.ColumnName.Replace("%", "").Trim().ToLower();
                if (cleanDataCol.Contains(cleanColumnName) || cleanColumnName.Contains(cleanDataCol))
                {
                    System.Diagnostics.Debug.WriteLine($"通过模糊匹配找到列: '{columnName}' -> '{column.ColumnName}'");
                    return column.ColumnName;
                }
            }

            // 如果都找不到，返回原始列名
            System.Diagnostics.Debug.WriteLine($"警告: 找不到列 '{columnName}' 的匹配");
            return columnName;
        }

        // 查找实际列名 - 双参数版本（添加是否为脆性矿物的标志）
        private string FindActualColumnName(string columnName, bool isBrittle)
        {
            // 移除列名中的%和空格，统一小写比较
            string cleanColumnName = columnName.Replace("%", "").Trim().ToLower();

            // 如果列名直接存在于数据源中，直接返回
            if (_sourceData.Columns.Contains(columnName))
            {
                return columnName;
            }

            // 尝试清理后的精确匹配
            foreach (DataColumn col in _sourceData.Columns)
            {
                string cleanDataCol = col.ColumnName.Replace("%", "").Trim().ToLower();
                if (cleanDataCol == cleanColumnName)
                {
                    System.Diagnostics.Debug.WriteLine($"通过清理匹配找到列: '{columnName}' -> '{col.ColumnName}'");
                    return col.ColumnName; // 返回实际列名
                }
            }

            // 检查映射字典
            if (_columnMappings.TryGetValue(columnName, out string mappedName))
            {
                if (_sourceData.Columns.Contains(mappedName))
                {
                    System.Diagnostics.Debug.WriteLine($"通过映射字典找到列: '{columnName}' -> '{mappedName}'");
                    return mappedName;
                }

                // 尝试清理后的映射匹配
                string cleanMappedName = mappedName.Replace("%", "").Trim().ToLower();
                foreach (DataColumn col in _sourceData.Columns)
                {
                    string cleanDataCol = col.ColumnName.Replace("%", "").Trim().ToLower();
                    if (cleanDataCol == cleanMappedName)
                    {
                        System.Diagnostics.Debug.WriteLine($"通过清理映射找到列: '{columnName}' -> '{mappedName}' -> '{col.ColumnName}'");
                        return col.ColumnName;
                    }
                }
            }

            // 根据是否为脆性矿物选择不同的映射字典
            var mappingDict = isBrittle ? _globalBrittleMappings : _globalDuctileMappings;
            string mineralType = isBrittle ? "脆性" : "塑性";

            // 在选定的映射字典中查找
            foreach (var mapping in mappingDict)
            {
                if (mapping.Key.Equals(columnName, StringComparison.OrdinalIgnoreCase) ||
                    mapping.Value.Contains(columnName, StringComparer.OrdinalIgnoreCase))
                {
                    // 尝试找到数据源中存在的任何一个映射名
                    foreach (var possibleName in mapping.Value)
                    {
                        if (_sourceData.Columns.Contains(possibleName))
                        {
                            System.Diagnostics.Debug.WriteLine($"通过{mineralType}全局映射找到列: '{columnName}' -> '{possibleName}'");
                            return possibleName;
                        }

                        // 尝试清理后的映射匹配
                        string cleanPossibleName = possibleName.Replace("%", "").Trim().ToLower();
                        foreach (DataColumn col in _sourceData.Columns)
                        {
                            string cleanDataCol = col.ColumnName.Replace("%", "").Trim().ToLower();
                            if (cleanDataCol == cleanPossibleName)
                            {
                                System.Diagnostics.Debug.WriteLine($"通过清理{mineralType}全局映射找到列: '{columnName}' -> '{possibleName}' -> '{col.ColumnName}'");
                                return col.ColumnName;
                            }
                        }
                    }
                }
            }

            // 尝试模糊匹配 - 检查列名是否包含关键词
            foreach (DataColumn column in _sourceData.Columns)
            {
                string cleanDataCol = column.ColumnName.Replace("%", "").Trim().ToLower();
                if (cleanDataCol.Contains(cleanColumnName) || cleanColumnName.Contains(cleanDataCol))
                {
                    System.Diagnostics.Debug.WriteLine($"通过模糊匹配找到列: '{columnName}' -> '{column.ColumnName}'");
                    return column.ColumnName;
                }
            }

            // 如果都找不到，返回原始列名
            System.Diagnostics.Debug.WriteLine($"警告: 找不到列 '{columnName}' 的匹配");
            return columnName;
        }
        // 顶深和底深按钮点击事件
        private void BtnTopDepth_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建列选择窗体
                Form columnSelectForm = new Form
                {
                    Text = "选择顶深列",
                    Size = new Size(300, 400),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                // 创建列表框
                ListBox lstColumns = new ListBox
                {
                    Dock = DockStyle.Fill,
                    SelectionMode = SelectionMode.One
                };

                // 添加可用列
                foreach (var item in lstAvailableColumns.Items)
                {
                    lstColumns.Items.Add(item);
                }

                // 添加确定按钮
                Button btnOK = new Button
                {
                    Text = "确定",
                    Dock = DockStyle.Bottom,
                    DialogResult = DialogResult.OK
                };

                // 添加控件到窗体
                columnSelectForm.Controls.Add(lstColumns);
                columnSelectForm.Controls.Add(btnOK);

                // 显示窗体
                if (columnSelectForm.ShowDialog() == DialogResult.OK && lstColumns.SelectedItem != null)
                {
                    // 设置顶深列名
                    topDepthColumnName = lstColumns.SelectedItem.ToString();
                    lblTopDepth.Text = topDepthColumnName;
                    lblTopDepth.ForeColor = Color.Green;
                    System.Diagnostics.Debug.WriteLine($"已选择顶深列: {topDepthColumnName}");

                    // 立即高亮显示选中的列
                    if (dgvResult.DataSource == _sourceData)
                    {
                        HighlightSelectedColumns();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择顶深列时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"选择顶深列时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }

        private void BtnBottomDepth_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建列选择窗体
                Form columnSelectForm = new Form
                {
                    Text = "选择底深列",
                    Size = new Size(300, 400),
                    StartPosition = FormStartPosition.CenterParent,
                    FormBorderStyle = FormBorderStyle.FixedDialog,
                    MaximizeBox = false,
                    MinimizeBox = false
                };

                // 创建列表框
                ListBox lstColumns = new ListBox
                {
                    Dock = DockStyle.Fill,
                    SelectionMode = SelectionMode.One
                };

                // 添加可用列
                foreach (var item in lstAvailableColumns.Items)
                {
                    lstColumns.Items.Add(item);
                }

                // 添加确定按钮
                Button btnOK = new Button
                {
                    Text = "确定",
                    Dock = DockStyle.Bottom,
                    DialogResult = DialogResult.OK
                };

                // 添加控件到窗体
                columnSelectForm.Controls.Add(lstColumns);
                columnSelectForm.Controls.Add(btnOK);

                // 显示窗体
                if (columnSelectForm.ShowDialog() == DialogResult.OK && lstColumns.SelectedItem != null)
                {
                    // 设置底深列名
                    bottomDepthColumnName = lstColumns.SelectedItem.ToString();
                    lblBottomDepth.Text = bottomDepthColumnName;
                    lblBottomDepth.ForeColor = Color.Green;
                    System.Diagnostics.Debug.WriteLine($"已选择底深列: {bottomDepthColumnName}");

                    // 立即高亮显示选中的列
                    if (dgvResult.DataSource == _sourceData)
                    {
                        HighlightSelectedColumns();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"选择底深列时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"选择底深列时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }



        // 计算按钮点击事件
        // ... existing code ...

        // 修改计算按钮点击事件，使用选择的列名而不是硬编码的名称
        private void btnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否已选择顶深和底深列
                if (string.IsNullOrEmpty(topDepthColumnName) || string.IsNullOrEmpty(bottomDepthColumnName))
                {
                    MessageBox.Show("请先选择顶深和底深列！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 检查是否已选择脆性和塑性矿物列
                if (_brittleColumns.Count == 0 || _ductileColumns.Count == 0)
                {
                    MessageBox.Show("请先选择脆性和塑性矿物列！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 清空之前的计算结果
                _dataPoints.Clear();

                // 重新初始化结果表，确保列名一致
                InitializeResultDataTable();

                // 输出源数据信息
                System.Diagnostics.Debug.WriteLine($"Calculate button clicked. Source data: {_sourceData.Rows.Count} rows, {_sourceData.Columns.Count} columns");
                System.Diagnostics.Debug.WriteLine($"Brittle columns: {_brittleColumns.Count}, Ductile columns: {_ductileColumns.Count}");
                foreach (string col in _brittleColumns)
                {
                    System.Diagnostics.Debug.WriteLine($"  - {col}");
                }
                foreach (string col in _ductileColumns)
                {
                    System.Diagnostics.Debug.WriteLine($"  - {col}");
                }

                // 处理每一行数据
                System.Diagnostics.Debug.WriteLine($"开始处理所有行数据，共 {_sourceData.Rows.Count} 行");

                for (int i = 0; i < _sourceData.Rows.Count; i++)
                {
                    DataRow row = _sourceData.Rows[i];
                    BrittlenessDataPoint dataPoint = new BrittlenessDataPoint();
                    dataPoint.RowIndex = i;

                    // 使用选择的列名获取顶深和底深值
                    if (row[topDepthColumnName] != DBNull.Value)
                    {
                        double topDepth;
                        if (double.TryParse(row[topDepthColumnName].ToString(), out topDepth))
                        {
                            dataPoint.TopDepth = topDepth;
                            System.Diagnostics.Debug.WriteLine($"标准解析: {row[topDepthColumnName]} -> {topDepth}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"无法解析顶深值: {row[topDepthColumnName]}");
                            continue;
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"行 {i} 的顶深值为空");
                        continue;
                    }

                    // 获取底深值
                    if (row[bottomDepthColumnName] != DBNull.Value)
                    {
                        double bottomDepth;
                        if (double.TryParse(row[bottomDepthColumnName].ToString(), out bottomDepth))
                        {
                            dataPoint.BottomDepth = bottomDepth;
                            System.Diagnostics.Debug.WriteLine($"标准解析: {row[bottomDepthColumnName]} -> {bottomDepth}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"无法解析底深值: {row[bottomDepthColumnName]}");
                            continue;
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"行 {i} 的底深值为空");
                        continue;
                    }

                    // 计算脆性矿物总量
                    double brittleSum = 0;
                    foreach (string colName in _brittleColumns)
                    {
                        // 获取映射后的列名
                        string mappedName = colName;
                        if (_columnMappings.ContainsKey(colName))
                        {
                            mappedName = _columnMappings[colName];
                        }

                        if (row[mappedName] != DBNull.Value)
                        {
                            double value;
                            if (double.TryParse(row[mappedName].ToString(), out value))
                            {
                                brittleSum += value;
                                dataPoint.BrittleMinerals.Add($"{colName}: {value}");
                                System.Diagnostics.Debug.WriteLine($"标准解析: {row[mappedName]} -> {value}");
                            }
                        }
                    }

                    // 计算塑性矿物总量
                    double ductileSum = 0;
                    foreach (string colName in _ductileColumns)
                    {
                        // 获取映射后的列名
                        string mappedName = colName;
                        if (_columnMappings.ContainsKey(colName))
                        {
                            mappedName = _columnMappings[colName];
                        }

                        if (row[mappedName] != DBNull.Value)
                        {
                            double value;
                            if (double.TryParse(row[mappedName].ToString(), out value))
                            {
                                ductileSum += value;
                                dataPoint.DuctileMinerals.Add($"{colName}: {value}");
                                System.Diagnostics.Debug.WriteLine($"标准解析: {row[mappedName]} -> {value}");
                            }
                        }
                    }

                    // 计算脆性指数
                    if (brittleSum + ductileSum > 0)
                    {
                        dataPoint.BrittleIndex = (brittleSum / (brittleSum + ductileSum)) * 100;
                    }
                    else
                    {
                        dataPoint.BrittleIndex = 0;
                    }

                    // 生成GeoID
                    dataPoint.GenerateGeoID();
                    System.Diagnostics.Debug.WriteLine($"生成GeoID: {dataPoint.GeoID}");

                    // 添加到数据点列表
                    _dataPoints.Add(dataPoint);

                    // 添加到结果数据表
                    DataRow resultRow = _resultData.NewRow();
                    resultRow["GeoID"] = dataPoint.GeoID;
                    resultRow["顶深/m"] = dataPoint.TopDepth;
                    resultRow["底深/m"] = dataPoint.BottomDepth;
                    resultRow["脆性指数"] = dataPoint.BrittleIndex;

                    // 添加脆性和塑性矿物值
                    foreach (string colName in _brittleColumns)
                    {
                        if (_resultData.Columns.Contains(colName))
                        {
                            // 获取映射后的列名
                            string mappedName = colName;
                            if (_columnMappings.ContainsKey(colName))
                            {
                                mappedName = _columnMappings[colName];
                            }

                            if (row[mappedName] != DBNull.Value)
                            {
                                double value;
                                if (double.TryParse(row[mappedName].ToString(), out value))
                                {
                                    resultRow[colName] = value;
                                }
                            }
                        }
                    }

                    foreach (string colName in _ductileColumns)
                    {
                        if (_resultData.Columns.Contains(colName))
                        {
                            // 获取映射后的列名
                            string mappedName = colName;
                            if (_columnMappings.ContainsKey(colName))
                            {
                                mappedName = _columnMappings[colName];
                            }

                            if (row[mappedName] != DBNull.Value)
                            {
                                double value;
                                if (double.TryParse(row[mappedName].ToString(), out value))
                                {
                                    resultRow[colName] = value;
                                }
                            }
                        }
                    }

                    _resultData.Rows.Add(resultRow);
                }

                // 显示结果
                dgvResult.DataSource = _resultData;
                resultLabel.Text = $"计算结果: 共 {_dataPoints.Count} 个数据点";

                // 高亮显示选中列
                HighlightSelectedColumns();

                // 如果有数据，则启用保存按钮
                btnSaveData.Enabled = _dataPoints.Count > 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"计算错误: {ex.Message}\n{ex.StackTrace}");
            }
        }
        // 保存计算日志到文件
        private void SaveCalculationLog(string logContent)
        {
            try
            {
                // 创建日志目录
                string logDir = Path.Combine(Application.StartupPath, "Logs");
                if (!Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                }

                // 创建日志文件名
                string logFileName = $"Calculation_{DateTime.Now.ToString("yyyyMMdd_HHmmss")}.log";
                string logFilePath = Path.Combine(logDir, logFileName);

                // 写入日志内容
                File.WriteAllText(logFilePath, logContent, Encoding.UTF8);

                System.Diagnostics.Debug.WriteLine($"计算日志已保存到: {logFilePath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存计算日志时出错: {ex.Message}");
            }
        }

        // ... existing code ...
        // 安全解析单元格值
        private double SafeParseDouble(object value)
        {
            if (value == null || value == DBNull.Value)
                return 0;

            string strValue = value.ToString().Trim('%', ' ');

            // 记录原始值，用于调试
            string originalValue = value.ToString();

            try
            {
                // 如果是数值类型，直接转换
                if (value is double doubleValue)
                    return doubleValue;
                if (value is int intValue)
                    return intValue;
                if (value is decimal decimalValue)
                    return (double)decimalValue;
                if (value is float floatValue)
                    return floatValue;

                // 处理科学计数法
                if (strValue.Contains("E") || strValue.Contains("e"))
                {
                    if (double.TryParse(strValue, NumberStyles.Float, CultureInfo.InvariantCulture, out double scientificResult))
                    {
                        System.Diagnostics.Debug.WriteLine($"解析科学计数法: {originalValue} -> {scientificResult}");
                        return scientificResult;
                    }
                }

                // 处理逗号分隔的数字（如1,234.56）
                if (strValue.Contains(","))
                {
                    string noCommas = strValue.Replace(",", "");
                    if (double.TryParse(noCommas, out double noCommaResult))
                    {
                        System.Diagnostics.Debug.WriteLine($"解析逗号分隔数字: {originalValue} -> {noCommaResult}");
                        return noCommaResult;
                    }
                }

                // 标准解析尝试
                if (double.TryParse(strValue, out double result))
                {
                    System.Diagnostics.Debug.WriteLine($"标准解析: {originalValue} -> {result}");
                    return result;
                }

                // 尝试不同的区域性设置
                if (double.TryParse(strValue, NumberStyles.Any, CultureInfo.InvariantCulture, out double invariantResult))
                {
                    System.Diagnostics.Debug.WriteLine($"使用不变区域性解析: {originalValue} -> {invariantResult}");
                    return invariantResult;
                }

                // 如果所有尝试都失败，记录并返回0
                System.Diagnostics.Debug.WriteLine($"无法解析值: {originalValue}，返回0");
                return 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析值时出错: {originalValue}, 错误: {ex.Message}");
                return 0;
            }
        }

        // ... existing code ...

        // 计算脆性指数
        private void CalculateBrittlenessIndex()
        {
            StringBuilder log = new StringBuilder();
            log.AppendLine($"===== 脆性指数计算开始 - {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")} =====");

            try
            {
                // 检查是否有数据
                if (_sourceData == null || _sourceData.Rows.Count == 0)
                {
                    string errorMsg = "没有数据可供计算！请先加载数据。";
                    log.AppendLine($"错误: {errorMsg}");
                    MessageBox.Show(errorMsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    SaveCalculationLog(log.ToString());
                    return;
                }

                log.AppendLine($"源数据表: {_sourceData.Rows.Count} 行, {_sourceData.Columns.Count} 列");

                // 清空之前的数据点
                _dataPoints.Clear();
                log.AppendLine("清空之前的数据点");

                // 验证顶深和底深列是否存在
                if (topDepthIndex == -1 || bottomDepthIndex == -1)
                {
                    string errorMsg = "请先选择顶深和底深列！";
                    log.AppendLine($"错误: {errorMsg}");
                    MessageBox.Show(errorMsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    SaveCalculationLog(log.ToString());
                    return;
                }

                log.AppendLine($"顶深列: {topDepthColumnName}, 索引: {topDepthIndex}");
                log.AppendLine($"底深列: {bottomDepthColumnName}, 索引: {bottomDepthIndex}");

                // 检查是否选择了脆性矿物和塑性矿物
                if (_brittleColumns.Count == 0 || _ductileColumns.Count == 0)
                {
                    string errorMsg = "请至少选择一种脆性矿物和一种塑性矿物！";
                    log.AppendLine($"错误: {errorMsg}");
                    MessageBox.Show(errorMsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    SaveCalculationLog(log.ToString());
                    return;
                }

                // 记录选择的脆性和塑性矿物列
                log.AppendLine($"脆性矿物列数量: {_brittleColumns.Count}, 塑性矿物列数量: {_ductileColumns.Count}");
                log.AppendLine("已选择的脆性矿物列: ");
                foreach (string column in _brittleColumns)
                {
                    log.AppendLine($"  - {column}");
                }

                log.AppendLine("已选择的塑性矿物列: ");
                foreach (string column in _ductileColumns)
                {
                    log.AppendLine($"  - {column}");
                }

                // 验证所有选择的列是否存在
                List<string> missingColumns = new List<string>();

                // 验证脆性矿物列
                foreach (string column in _brittleColumns)
                {
                    string actualColumnName = FindActualColumnName(column);
                    if (!_sourceData.Columns.Contains(actualColumnName))
                    {
                        missingColumns.Add($"脆性矿物列: {column} (映射为: {actualColumnName})");
                        log.AppendLine($"警告: 找不到脆性矿物列 {column} (映射为: {actualColumnName})");
                    }
                }

                // 验证塑性矿物列
                foreach (string column in _ductileColumns)
                {
                    string actualColumnName = FindActualColumnName(column);
                    if (!_sourceData.Columns.Contains(actualColumnName))
                    {
                        missingColumns.Add($"塑性矿物列: {column} (映射为: {actualColumnName})");
                        log.AppendLine($"警告: 找不到塑性矿物列 {column} (映射为: {actualColumnName})");
                    }
                }

                // 如果有缺失的列，显示警告
                if (missingColumns.Count > 0)
                {
                    StringBuilder warningMsg = new StringBuilder("以下列在数据表中不存在:\n\n");
                    foreach (string col in missingColumns)
                    {
                        warningMsg.AppendLine($"- {col}");
                    }
                    warningMsg.AppendLine("\n是否继续计算？（缺失的列将被忽略）");

                    DialogResult result = MessageBox.Show(warningMsg.ToString(), "警告", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                    if (result == DialogResult.No)
                    {
                        log.AppendLine("用户选择取消计算");
                        AlgorithmFormulaCalExtensions.SaveCalculationLog(log.ToString());
                        return;
                    }

                    log.AppendLine("用户选择继续计算，忽略缺失的列");
                }

                // 显示进度条
                using (var progressForm = new Form())
                {
                    progressForm.Text = "计算中...";
                    progressForm.Size = new Size(400, 100);
                    progressForm.StartPosition = FormStartPosition.CenterParent;
                    progressForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                    progressForm.MaximizeBox = false;
                    progressForm.MinimizeBox = false;

                    var progressBar = new ProgressBar();
                    progressBar.Dock = DockStyle.Fill;
                    progressBar.Minimum = 0;
                    progressBar.Maximum = _sourceData.Rows.Count;
                    progressBar.Value = 0;
                    progressBar.Style = ProgressBarStyle.Continuous;

                    progressForm.Controls.Add(progressBar);

                    // 在后台线程中执行计算
                    var backgroundWorker = new BackgroundWorker();
                    backgroundWorker.WorkerReportsProgress = true;

                    backgroundWorker.DoWork += (sender, e) =>
                    {
                        // 遍历每一行数据
                        for (int rowIndex = 0; rowIndex < _sourceData.Rows.Count; rowIndex++)
                        {
                            try
                            {
                                DataRow row = _sourceData.Rows[rowIndex];

                                // 创建新的数据点
                                BrittlenessDataPoint dataPoint = new BrittlenessDataPoint();
                                dataPoint.RowIndex = rowIndex;

                                // 设置顶深和底深
                                if (!row.IsNull(topDepthIndex) && !row.IsNull(bottomDepthIndex))
                                {
                                    dataPoint.TopDepth = AlgorithmFormulaCalExtensions.SafeParseDouble(row[topDepthIndex]);
                                    dataPoint.BottomDepth = AlgorithmFormulaCalExtensions.SafeParseDouble(row[bottomDepthIndex]);
                                    log.AppendLine($"行 {rowIndex}: 顶深 = {dataPoint.TopDepth}, 底深 = {dataPoint.BottomDepth}");
                                }
                                else
                                {
                                    // 如果顶深或底深为空，跳过此行
                                    log.AppendLine($"行 {rowIndex} 的顶深或底深为空，跳过此行");
                                    continue;
                                }

                                // 计算脆性矿物总量
                                double brittleMineralsSum = 0;
                                foreach (string columnName in _brittleColumns)
                                {
                                    string actualColumnName = FindActualColumnName(columnName);
                                    if (_sourceData.Columns.Contains(actualColumnName) && !row.IsNull(actualColumnName))
                                    {
                                        double value = AlgorithmFormulaCalExtensions.SafeParseDouble(row[actualColumnName]);
                                        brittleMineralsSum += value;
                                        dataPoint.BrittleMinerals.Add($"{actualColumnName}: {value}%");
                                        log.AppendLine($"  脆性矿物 {actualColumnName} = {value}");
                                    }
                                }

                                // 计算塑性矿物总量
                                double ductileMineralsSum = 0;
                                foreach (string columnName in _ductileColumns)
                                {
                                    string actualColumnName = FindActualColumnName(columnName);
                                    if (_sourceData.Columns.Contains(actualColumnName) && !row.IsNull(actualColumnName))
                                    {
                                        double value = AlgorithmFormulaCalExtensions.SafeParseDouble(row[actualColumnName]);
                                        ductileMineralsSum += value;
                                        dataPoint.DuctileMinerals.Add($"{actualColumnName}: {value}%");
                                        log.AppendLine($"  塑性矿物 {actualColumnName} = {value}");
                                    }
                                }

                                // 计算脆性指数
                                if (brittleMineralsSum + ductileMineralsSum > 0)
                                {
                                    dataPoint.BrittleIndex = (brittleMineralsSum / (brittleMineralsSum + ductileMineralsSum)) * 100;
                                    log.AppendLine($"  脆性指数 = {dataPoint.BrittleIndex:F2}%");
                                }
                                else
                                {
                                    dataPoint.BrittleIndex = 0;
                                    log.AppendLine($"  警告: 脆性矿物总量和塑性矿物总量均为0，脆性指数设为0");
                                }

                                // 生成唯一的GeoID
                                dataPoint.GenerateGeoID();
                                log.AppendLine($"  GeoID = {dataPoint.GeoID}");

                                // 添加到数据点列表
                                lock (_dataPoints)
                                {
                                    _dataPoints.Add(dataPoint);
                                }

                                System.Diagnostics.Debug.WriteLine($"行 {rowIndex} 计算完成: 脆性指数 = {dataPoint.BrittleIndex:F2}%, GeoID = {dataPoint.GeoID}");
                            }
                            catch (Exception ex)
                            {
                                log.AppendLine($"处理行 {rowIndex} 时出错: {ex.Message}");
                                System.Diagnostics.Debug.WriteLine($"处理行 {rowIndex} 时出错: {ex.Message}\n{ex.StackTrace}");
                            }

                            // 报告进度
                            backgroundWorker.ReportProgress(rowIndex + 1);
                        }
                    };

                    backgroundWorker.ProgressChanged += (sender, e) =>
                    {
                        progressBar.Value = e.ProgressPercentage;
                    };

                    backgroundWorker.RunWorkerCompleted += (sender, e) =>
                    {
                        progressForm.Close();

                        if (e.Error != null)
                        {
                            log.AppendLine($"计算过程中发生错误: {e.Error.Message}\n{e.Error.StackTrace}");
                            MessageBox.Show($"计算过程中发生错误: {e.Error.Message}\n\n请检查数据格式是否正确，并确保所有必需的列都存在。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            AlgorithmFormulaCalExtensions.SaveCalculationLog(log.ToString());
                            return;
                        }

                        try
                        {
                            // 更新结果表格
                            UpdateResultTable();
                            log.AppendLine($"更新结果表完成，共 {_resultData.Rows.Count} 行数据");

                            // 高亮显示列
                            HighlightColumns();
                            log.AppendLine("完成高亮显示列");

                            log.AppendLine($"计算完成，共生成 {_dataPoints.Count} 个数据点");

                            // 显示计算结果
                            MessageBox.Show($"计算完成！共计算了 {_dataPoints.Count} 个数据点的脆性指数。", "计算结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                        catch (Exception ex)
                        {
                            log.AppendLine($"更新结果表时出错: {ex.Message}\n{ex.StackTrace}");
                            MessageBox.Show($"更新结果表时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }

                        log.AppendLine($"===== 脆性指数计算结束 - {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")} =====");
                        AlgorithmFormulaCalExtensions.SaveCalculationLog(log.ToString());
                    };

                    // 启动后台线程
                    backgroundWorker.RunWorkerAsync();

                    // 显示进度条
                    progressForm.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                log.AppendLine($"计算过程中发生错误: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"计算过程中发生错误: {ex.Message}\n\n请检查数据格式是否正确，并确保所有必需的列都存在。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"计算错误: {ex.Message}\n{ex.StackTrace}");
                AlgorithmFormulaCalExtensions.SaveCalculationLog(log.ToString());
            }
        }

        // 更新结果表格
        private void UpdateResultTable()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始更新结果表格...");

                // 清空结果表
                _resultData.Clear();

                // 确保结果表有必要的列
                EnsureResultTableColumns();

                // 添加每个数据点到结果表
                foreach (BrittlenessDataPoint point in _dataPoints)
                {
                    DataRow newRow = _resultData.NewRow();

                    // 设置基本信息
                    newRow["GeoID"] = point.GeoID;
                    newRow["顶深/m"] = point.TopDepth;
                    newRow["底深/m"] = point.BottomDepth;
                    newRow["脆性指数"] = point.BrittleIndex;

                    // 计算脆性矿物总量和塑性矿物总量
                    double brittleMineralsTotal = 0;
                    double ductileMineralsTotal = 0;

                    // 解析脆性矿物列表中的值
                    foreach (string mineral in point.BrittleMinerals)
                    {
                        string[] parts = mineral.Split('=', ':');
                        if (parts.Length >= 2)
                        {
                            string mineralName = parts[0].Trim();
                            string valueStr = parts[1].Trim().TrimEnd('%');

                            if (double.TryParse(valueStr, out double value))
                            {
                                brittleMineralsTotal += value;

                                // 如果结果表中有对应的列，设置值
                                if (_resultData.Columns.Contains(mineralName))
                                {
                                    newRow[mineralName] = value;
                                }
                            }
                        }
                    }

                    // 解析塑性矿物列表中的值
                    foreach (string mineral in point.DuctileMinerals)
                    {
                        string[] parts = mineral.Split('=', ':');
                        if (parts.Length >= 2)
                        {
                            string mineralName = parts[0].Trim();
                            string valueStr = parts[1].Trim().TrimEnd('%');

                            if (double.TryParse(valueStr, out double value))
                            {
                                ductileMineralsTotal += value;

                                // 如果结果表中有对应的列，设置值
                                if (_resultData.Columns.Contains(mineralName))
                                {
                                    newRow[mineralName] = value;
                                }
                            }
                        }
                    }

                    // 设置脆性矿物总量和塑性矿物总量
                    if (_resultData.Columns.Contains("脆性矿物总量"))
                    {
                        newRow["脆性矿物总量"] = brittleMineralsTotal;
                    }

                    if (_resultData.Columns.Contains("塑性矿物总量"))
                    {
                        newRow["塑性矿物总量"] = ductileMineralsTotal;
                    }

                    // 设置脆性矿物和塑性矿物信息（从源数据表复制）
                    if (point.RowIndex >= 0 && point.RowIndex < _sourceData.Rows.Count)
                    {
                        DataRow sourceRow = _sourceData.Rows[point.RowIndex];

                        // 复制脆性矿物列
                        foreach (string columnName in _brittleColumns)
                        {
                            string actualColumnName = FindActualColumnName(columnName);
                            if (_sourceData.Columns.Contains(actualColumnName) && !sourceRow.IsNull(actualColumnName))
                            {
                                // 确保结果表中有对应的列
                                if (!_resultData.Columns.Contains(actualColumnName))
                                {
                                    _resultData.Columns.Add(actualColumnName, typeof(double));
                                    System.Diagnostics.Debug.WriteLine($"添加脆性矿物列到结果表: {actualColumnName}");
                                }

                                // 设置值
                                newRow[actualColumnName] = AlgorithmFormulaCalExtensions.SafeParseDouble(sourceRow[actualColumnName]);
                            }
                        }

                        // 复制塑性矿物列
                        foreach (string columnName in _ductileColumns)
                        {
                            string actualColumnName = FindActualColumnName(columnName);
                            if (_sourceData.Columns.Contains(actualColumnName) && !sourceRow.IsNull(actualColumnName))
                            {
                                // 确保结果表中有对应的列
                                if (!_resultData.Columns.Contains(actualColumnName))
                                {
                                    _resultData.Columns.Add(actualColumnName, typeof(double));
                                    System.Diagnostics.Debug.WriteLine($"添加塑性矿物列到结果表: {actualColumnName}");
                                }

                                // 设置值
                                newRow[actualColumnName] = AlgorithmFormulaCalExtensions.SafeParseDouble(sourceRow[actualColumnName]);
                            }
                        }
                    }

                    // 添加行到结果表
                    _resultData.Rows.Add(newRow);
                }

                // 刷新数据网格视图
                dgvResult.DataSource = null;
                dgvResult.DataSource = _resultData;

                // 统一列名单位标识
                UnifyColumnUnits();

                // 高亮显示列
                HighlightColumns();

                System.Diagnostics.Debug.WriteLine($"结果表更新完成，共 {_resultData.Rows.Count} 行数据");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新结果表时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"更新结果表时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }

        // 确保结果表有必要的列
        private void EnsureResultTableColumns()
        {
            try
            {
                // 如果结果表为空，创建必要的列
                if (_resultData.Columns.Count == 0)
                {
                    // 添加基本列
                    _resultData.Columns.Add("GeoID", typeof(string));
                    _resultData.Columns.Add("顶深/m", typeof(double));
                    _resultData.Columns.Add("底深/m", typeof(double));
                    _resultData.Columns.Add("脆性指数", typeof(double));
                    _resultData.Columns.Add("脆性矿物总量", typeof(double));
                    _resultData.Columns.Add("塑性矿物总量", typeof(double));

                    System.Diagnostics.Debug.WriteLine("创建结果表基本列");
                }

                // 确保所有脆性矿物列都存在
                foreach (string columnName in _brittleColumns)
                {
                    string actualColumnName = FindActualColumnName(columnName);
                    if (!_resultData.Columns.Contains(actualColumnName) && _sourceData.Columns.Contains(actualColumnName))
                    {
                        _resultData.Columns.Add(actualColumnName, typeof(double));
                        System.Diagnostics.Debug.WriteLine($"添加脆性矿物列到结果表: {actualColumnName}");
                    }
                }

                // 确保所有塑性矿物列都存在
                foreach (string columnName in _ductileColumns)
                {
                    string actualColumnName = FindActualColumnName(columnName);
                    if (!_resultData.Columns.Contains(actualColumnName) && _sourceData.Columns.Contains(actualColumnName))
                    {
                        _resultData.Columns.Add(actualColumnName, typeof(double));
                        System.Diagnostics.Debug.WriteLine($"添加塑性矿物列到结果表: {actualColumnName}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"结果表列数: {_resultData.Columns.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"确保结果表列时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }

        // 统一列名单位标识
        private void UnifyColumnUnits()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始统一列名单位标识...");

                // 遍历所有列，确保矿物列名都有百分比标识
                foreach (DataGridViewColumn column in dgvResult.Columns)
                {
                    string columnName = column.Name;

                    // 跳过基本列
                    if (columnName == "GeoID" || columnName == "顶深/m" || columnName == "底深/m" ||
                        columnName == "脆性指数" || columnName == "脆性矿物总量" || columnName == "塑性矿物总量")
                    {
                        continue;
                    }

                    // 检查是否是脆性或塑性矿物列
                    bool isBrittleColumn = _brittleColumns.Contains(columnName) ||
                                          _brittleColumns.Any(c => FindActualColumnName(c) == columnName);
                    bool isDuctileColumn = _ductileColumns.Contains(columnName) ||
                                          _ductileColumns.Any(c => FindActualColumnName(c) == columnName);

                    if (isBrittleColumn || isDuctileColumn)
                    {
                        // 如果列名不包含百分比标识，添加百分比标识
                        if (!columnName.Contains("%"))
                        {
                            string newColumnName = columnName + "%";

                            // 检查新列名是否已存在
                            if (!dgvResult.Columns.Contains(newColumnName))
                            {
                                column.HeaderText = newColumnName;
                                System.Diagnostics.Debug.WriteLine($"更新列标题: {columnName} -> {newColumnName}");
                            }
                        }

                        // 设置列的格式为百分比
                        column.DefaultCellStyle.Format = "0.00";
                    }
                }

                // 设置脆性指数列的格式
                if (dgvResult.Columns.Contains("脆性指数"))
                {
                    dgvResult.Columns["脆性指数"].DefaultCellStyle.Format = "0.00";
                    dgvResult.Columns["脆性指数"].HeaderText = "脆性指数/%";
                }

                // 设置脆性矿物总量和塑性矿物总量列的格式
                if (dgvResult.Columns.Contains("脆性矿物总量"))
                {
                    dgvResult.Columns["脆性矿物总量"].DefaultCellStyle.Format = "0.00";
                    dgvResult.Columns["脆性矿物总量"].HeaderText = "脆性矿物总量/%";
                }

                if (dgvResult.Columns.Contains("塑性矿物总量"))
                {
                    dgvResult.Columns["塑性矿物总量"].DefaultCellStyle.Format = "0.00";
                    dgvResult.Columns["塑性矿物总量"].HeaderText = "塑性矿物总量/%";
                }

                System.Diagnostics.Debug.WriteLine("完成统一列名单位标识");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"统一列名单位标识时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }

        // 高亮显示列
        private void HighlightColumns()
        {
            try
            {
                // 高亮显示顶深和底深列
                if (dgvResult.Columns.Contains("顶深"))
                {
                    dgvResult.Columns["顶深"].DefaultCellStyle.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns["顶深"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["顶深"].HeaderCell.Style.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns["顶深"].HeaderCell.Style.ForeColor = Color.White;
                }

                if (dgvResult.Columns.Contains("底深"))
                {
                    dgvResult.Columns["底深"].DefaultCellStyle.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns["底深"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["底深"].HeaderCell.Style.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns["底深"].HeaderCell.Style.ForeColor = Color.White;
                }

                // 高亮显示脆性指数列
                if (dgvResult.Columns.Contains("脆性指数"))
                {
                    dgvResult.Columns["脆性指数"].DefaultCellStyle.BackColor = Color.FromArgb(150, 100, 100);
                    dgvResult.Columns["脆性指数"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["脆性指数"].HeaderCell.Style.BackColor = Color.FromArgb(150, 100, 100);
                    dgvResult.Columns["脆性指数"].HeaderCell.Style.ForeColor = Color.White;
                }

                // 高亮显示脆性矿物列
                foreach (string columnName in _brittleColumns)
                {
                    if (dgvResult.Columns.Contains(columnName))
                    {
                        dgvResult.Columns[columnName].DefaultCellStyle.BackColor = Color.FromArgb(80, 120, 100);
                        dgvResult.Columns[columnName].DefaultCellStyle.ForeColor = Color.White;
                        dgvResult.Columns[columnName].HeaderCell.Style.BackColor = Color.FromArgb(80, 120, 100);
                        dgvResult.Columns[columnName].HeaderCell.Style.ForeColor = Color.White;
                        System.Diagnostics.Debug.WriteLine($"成功高亮脆性矿物列: {columnName}");
                    }
                }

                // 高亮显示塑性矿物列
                foreach (string columnName in _ductileColumns)
                {
                    if (dgvResult.Columns.Contains(columnName))
                    {
                        dgvResult.Columns[columnName].DefaultCellStyle.BackColor = Color.FromArgb(120, 80, 100);
                        dgvResult.Columns[columnName].DefaultCellStyle.ForeColor = Color.White;
                        dgvResult.Columns[columnName].HeaderCell.Style.BackColor = Color.FromArgb(120, 80, 100);
                        dgvResult.Columns[columnName].HeaderCell.Style.ForeColor = Color.White;
                        System.Diagnostics.Debug.WriteLine($"成功高亮塑性矿物列: {columnName}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮显示列时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }
        // ... existing code ...




        // ... existing code ...
        // 高亮显示选中的列
        private void HighlightSelectedColumns()
        {
            try
            {
                // 检查数据网格是否有数据源
                if (dgvResult.DataSource == null)
                {
                    System.Diagnostics.Debug.WriteLine("数据网格没有数据源，无法高亮显示");
                    return;
                }

                // 重置所有列的样式
                foreach (DataGridViewColumn column in dgvResult.Columns)
                {
                    column.DefaultCellStyle.BackColor = dgvResult.DefaultCellStyle.BackColor;
                    column.DefaultCellStyle.ForeColor = dgvResult.DefaultCellStyle.ForeColor;
                    column.HeaderCell.Style.BackColor = dgvResult.ColumnHeadersDefaultCellStyle.BackColor;
                    column.HeaderCell.Style.ForeColor = dgvResult.ColumnHeadersDefaultCellStyle.ForeColor;
                }
                System.Diagnostics.Debug.WriteLine("已重置所有列的样式");

                // 高亮显示顶深列
                if (!string.IsNullOrEmpty(topDepthColumnName) && dgvResult.Columns.Contains(topDepthColumnName))
                {
                    System.Diagnostics.Debug.WriteLine($"尝试高亮顶深列: {topDepthColumnName}, 存在: {dgvResult.Columns.Contains(topDepthColumnName)}");
                    dgvResult.Columns[topDepthColumnName].DefaultCellStyle.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns[topDepthColumnName].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns[topDepthColumnName].HeaderCell.Style.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns[topDepthColumnName].HeaderCell.Style.ForeColor = Color.White;
                    System.Diagnostics.Debug.WriteLine($"成功高亮顶深列: {topDepthColumnName}");
                }
                else if (dgvResult.Columns.Contains("顶深/m"))
                {
                    System.Diagnostics.Debug.WriteLine($"尝试高亮顶深列: 顶深/m, 存在: {dgvResult.Columns.Contains("顶深/m")}");
                    dgvResult.Columns["顶深/m"].DefaultCellStyle.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns["顶深/m"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["顶深/m"].HeaderCell.Style.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns["顶深/m"].HeaderCell.Style.ForeColor = Color.White;
                    System.Diagnostics.Debug.WriteLine($"成功高亮顶深列: 顶深/m");
                }

                // 高亮显示底深列
                if (!string.IsNullOrEmpty(bottomDepthColumnName) && dgvResult.Columns.Contains(bottomDepthColumnName))
                {
                    System.Diagnostics.Debug.WriteLine($"尝试高亮底深列: {bottomDepthColumnName}, 存在: {dgvResult.Columns.Contains(bottomDepthColumnName)}");
                    dgvResult.Columns[bottomDepthColumnName].DefaultCellStyle.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns[bottomDepthColumnName].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns[bottomDepthColumnName].HeaderCell.Style.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns[bottomDepthColumnName].HeaderCell.Style.ForeColor = Color.White;
                    System.Diagnostics.Debug.WriteLine($"成功高亮底深列: {bottomDepthColumnName}");
                }
                else if (dgvResult.Columns.Contains("底深/m"))
                {
                    System.Diagnostics.Debug.WriteLine($"尝试高亮底深列: 底深/m, 存在: {dgvResult.Columns.Contains("底深/m")}");
                    dgvResult.Columns["底深/m"].DefaultCellStyle.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns["底深/m"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["底深/m"].HeaderCell.Style.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns["底深/m"].HeaderCell.Style.ForeColor = Color.White;
                    System.Diagnostics.Debug.WriteLine($"成功高亮底深列: 底深/m");
                }

                // 高亮显示脆性矿物列
                System.Diagnostics.Debug.WriteLine($"尝试高亮 {_brittleColumns.Count} 个脆性矿物列");
                foreach (string columnName in _brittleColumns)
                {
                    string mappedName = columnName;

                    // 检查是否有映射
                    if (_columnMappings.ContainsKey(columnName))
                    {
                        mappedName = _columnMappings[columnName];
                    }

                    System.Diagnostics.Debug.WriteLine($"尝试高亮脆性矿物列: {columnName} -> {mappedName}, 存在: {dgvResult.Columns.Contains(mappedName)}");

                    if (dgvResult.Columns.Contains(mappedName))
                    {
                        dgvResult.Columns[mappedName].DefaultCellStyle.BackColor = Color.FromArgb(80, 120, 100);
                        dgvResult.Columns[mappedName].DefaultCellStyle.ForeColor = Color.White;
                        dgvResult.Columns[mappedName].HeaderCell.Style.BackColor = Color.FromArgb(80, 120, 100);
                        dgvResult.Columns[mappedName].HeaderCell.Style.ForeColor = Color.White;
                        System.Diagnostics.Debug.WriteLine($"成功高亮脆性矿物列: {mappedName}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"警告: 脆性矿物列 {columnName} (映射为 {mappedName}) 在数据网格中不存在");
                    }
                }

                // 高亮显示塑性矿物列
                System.Diagnostics.Debug.WriteLine($"尝试高亮 {_ductileColumns.Count} 个塑性矿物列");
                foreach (string columnName in _ductileColumns)
                {
                    string mappedName = columnName;

                    // 检查是否有映射
                    if (_columnMappings.ContainsKey(columnName))
                    {
                        mappedName = _columnMappings[columnName];
                    }

                    System.Diagnostics.Debug.WriteLine($"尝试高亮塑性矿物列: {columnName} -> {mappedName}, 存在: {dgvResult.Columns.Contains(mappedName)}");

                    if (dgvResult.Columns.Contains(mappedName))
                    {
                        dgvResult.Columns[mappedName].DefaultCellStyle.BackColor = Color.FromArgb(120, 80, 100);
                        dgvResult.Columns[mappedName].DefaultCellStyle.ForeColor = Color.White;
                        dgvResult.Columns[mappedName].HeaderCell.Style.BackColor = Color.FromArgb(120, 80, 100);
                        dgvResult.Columns[mappedName].HeaderCell.Style.ForeColor = Color.White;
                        System.Diagnostics.Debug.WriteLine($"成功高亮塑性矿物列: {mappedName}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"警告: 塑性矿物列 {columnName} (映射为 {mappedName}) 在数据网格中不存在");
                    }
                }

                System.Diagnostics.Debug.WriteLine("完成高亮显示选中列");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮显示选中列时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }

        // 高亮显示源数据表中的选中列
        private void HighlightSourceDataColumns()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("高亮显示源数据表中的选中列");

                // 确保数据网格视图有数据
                if (dgvResult.DataSource == null || dgvResult.Columns.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("警告: 数据网格视图没有数据或列，无法高亮显示");
                    return;
                }

                // 输出当前数据网格视图的列信息
                System.Diagnostics.Debug.WriteLine("当前数据网格视图列信息: ");
                foreach (DataGridViewColumn col in dgvResult.Columns)
                {
                    System.Diagnostics.Debug.WriteLine($"  列名: {col.Name}, 标题: {col.HeaderText}, 索引: {col.Index}");
                }

                // 重置所有列的样式
                foreach (DataGridViewColumn col in dgvResult.Columns)
                {
                    col.DefaultCellStyle.BackColor = Color.FromArgb(50, 50, 50);
                    col.DefaultCellStyle.ForeColor = Color.White;
                    col.HeaderCell.Style.BackColor = Color.FromArgb(50, 50, 50);
                    col.HeaderCell.Style.ForeColor = Color.White;
                }

                System.Diagnostics.Debug.WriteLine("已重置所有列的样式");

                // 高亮显示顶深和底深列
                if (!string.IsNullOrEmpty(topDepthColumnName))
                {
                    System.Diagnostics.Debug.WriteLine($"尝试高亮顶深列: {topDepthColumnName}, 存在: {dgvResult.Columns.Contains(topDepthColumnName)}");
                    if (dgvResult.Columns.Contains(topDepthColumnName))
                    {
                        dgvResult.Columns[topDepthColumnName].DefaultCellStyle.BackColor = Color.SeaGreen;
                        dgvResult.Columns[topDepthColumnName].DefaultCellStyle.ForeColor = Color.White;
                        dgvResult.Columns[topDepthColumnName].HeaderCell.Style.BackColor = Color.SeaGreen;
                        dgvResult.Columns[topDepthColumnName].HeaderCell.Style.ForeColor = Color.White;
                        System.Diagnostics.Debug.WriteLine($"成功高亮顶深列: {topDepthColumnName}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"警告: 顶深列 {topDepthColumnName} 在数据网格中不存在");

                        // 尝试查找包含"顶深"的列
                        foreach (DataGridViewColumn col in dgvResult.Columns)
                        {
                            if (col.Name.Contains("顶深") || col.HeaderText.Contains("顶深"))
                            {
                                col.DefaultCellStyle.BackColor = Color.SeaGreen;
                                col.DefaultCellStyle.ForeColor = Color.White;
                                col.HeaderCell.Style.BackColor = Color.SeaGreen;
                                col.HeaderCell.Style.ForeColor = Color.White;
                                System.Diagnostics.Debug.WriteLine($"通过关键词匹配成功高亮顶深列: {col.Name}");
                                break;
                            }
                        }
                    }
                }

                if (!string.IsNullOrEmpty(bottomDepthColumnName))
                {
                    System.Diagnostics.Debug.WriteLine($"尝试高亮底深列: {bottomDepthColumnName}, 存在: {dgvResult.Columns.Contains(bottomDepthColumnName)}");
                    if (dgvResult.Columns.Contains(bottomDepthColumnName))
                    {
                        dgvResult.Columns[bottomDepthColumnName].DefaultCellStyle.BackColor = Color.SeaGreen;
                        dgvResult.Columns[bottomDepthColumnName].DefaultCellStyle.ForeColor = Color.White;
                        dgvResult.Columns[bottomDepthColumnName].HeaderCell.Style.BackColor = Color.SeaGreen;
                        dgvResult.Columns[bottomDepthColumnName].HeaderCell.Style.ForeColor = Color.White;
                        System.Diagnostics.Debug.WriteLine($"成功高亮底深列: {bottomDepthColumnName}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"警告: 底深列 {bottomDepthColumnName} 在数据网格中不存在");

                        // 尝试查找包含"底深"的列
                        foreach (DataGridViewColumn col in dgvResult.Columns)
                        {
                            if (col.Name.Contains("底深") || col.HeaderText.Contains("底深"))
                            {
                                col.DefaultCellStyle.BackColor = Color.SeaGreen;
                                col.DefaultCellStyle.ForeColor = Color.White;
                                col.HeaderCell.Style.BackColor = Color.SeaGreen;
                                col.HeaderCell.Style.ForeColor = Color.White;
                                System.Diagnostics.Debug.WriteLine($"通过关键词匹配成功高亮底深列: {col.Name}");
                                break;
                            }
                        }
                    }
                }

                // 高亮显示脆性矿物列
                System.Diagnostics.Debug.WriteLine($"尝试高亮 {_brittleColumns.Count} 个脆性矿物列");
                foreach (string colName in _brittleColumns)
                {
                    bool highlighted = false;

                    // 1. 尝试直接匹配
                    if (dgvResult.Columns.Contains(colName))
                    {
                        dgvResult.Columns[colName].DefaultCellStyle.BackColor = Color.FromArgb(80, 100, 120);
                        dgvResult.Columns[colName].DefaultCellStyle.ForeColor = Color.White;
                        dgvResult.Columns[colName].HeaderCell.Style.BackColor = Color.FromArgb(80, 100, 120);
                        dgvResult.Columns[colName].HeaderCell.Style.ForeColor = Color.White;
                        System.Diagnostics.Debug.WriteLine($"成功高亮脆性矿物列: {colName}");
                        highlighted = true;
                    }

                    // 2. 尝试使用映射查找
                    if (!highlighted && _columnMappings.TryGetValue(colName, out string mappedName))
                    {
                        if (dgvResult.Columns.Contains(mappedName))
                        {
                            dgvResult.Columns[mappedName].DefaultCellStyle.BackColor = Color.FromArgb(80, 100, 120);
                            dgvResult.Columns[mappedName].DefaultCellStyle.ForeColor = Color.White;
                            dgvResult.Columns[mappedName].HeaderCell.Style.BackColor = Color.FromArgb(80, 100, 120);
                            dgvResult.Columns[mappedName].HeaderCell.Style.ForeColor = Color.White;
                            System.Diagnostics.Debug.WriteLine($"通过映射成功高亮脆性矿物列: {colName} -> {mappedName}");
                            highlighted = true;
                        }
                    }

                    // 3. 尝试使用FindActualColumnName方法
                    if (!highlighted)
                    {
                        string actualColName = FindActualColumnName(colName);
                        if (!string.IsNullOrEmpty(actualColName) && dgvResult.Columns.Contains(actualColName))
                        {
                            dgvResult.Columns[actualColName].DefaultCellStyle.BackColor = Color.FromArgb(80, 100, 120);
                            dgvResult.Columns[actualColName].DefaultCellStyle.ForeColor = Color.White;
                            dgvResult.Columns[actualColName].HeaderCell.Style.BackColor = Color.FromArgb(80, 100, 120);
                            dgvResult.Columns[actualColName].HeaderCell.Style.ForeColor = Color.White;
                            System.Diagnostics.Debug.WriteLine($"通过FindActualColumnName成功高亮脆性矿物列: {colName} -> {actualColName}");
                            highlighted = true;
                        }
                    }

                    // 4. 尝试模糊匹配
                    if (!highlighted)
                    {
                        string cleanColName = colName.Replace("%", "").Trim().ToLower();
                        foreach (DataGridViewColumn col in dgvResult.Columns)
                        {
                            string cleanDataCol = col.Name.Replace("%", "").Trim().ToLower();
                            if (cleanDataCol.Contains(cleanColName) || cleanColName.Contains(cleanDataCol))
                            {
                                col.DefaultCellStyle.BackColor = Color.FromArgb(80, 100, 120);
                                col.DefaultCellStyle.ForeColor = Color.White;
                                col.HeaderCell.Style.BackColor = Color.FromArgb(80, 100, 120);
                                col.HeaderCell.Style.ForeColor = Color.White;
                                System.Diagnostics.Debug.WriteLine($"通过模糊匹配成功高亮脆性矿物列: {colName} -> {col.Name}");
                                highlighted = true;
                                break;
                            }
                        }
                    }

                    // 5. 尝试使用全局映射
                    if (!highlighted)
                    {
                        foreach (var mapping in _globalBrittleMappings)
                        {
                            if (mapping.Key == colName || mapping.Value.Contains(colName))
                            {
                                foreach (string pattern in mapping.Value)
                                {
                                    string cleanPattern = pattern.Replace("%", "").Trim().ToLower();
                                    foreach (DataGridViewColumn col in dgvResult.Columns)
                                    {
                                        string cleanDataCol = col.Name.Replace("%", "").Trim().ToLower();
                                        if (cleanDataCol.Contains(cleanPattern) || cleanPattern.Contains(cleanDataCol))
                                        {
                                            col.DefaultCellStyle.BackColor = Color.FromArgb(80, 100, 120);
                                            col.DefaultCellStyle.ForeColor = Color.White;
                                            col.HeaderCell.Style.BackColor = Color.FromArgb(80, 100, 120);
                                            col.HeaderCell.Style.ForeColor = Color.White;
                                            System.Diagnostics.Debug.WriteLine($"通过全局映射成功高亮脆性矿物列: {colName} -> {pattern} -> {col.Name}");
                                            highlighted = true;
                                            break;
                                        }
                                    }
                                    if (highlighted) break;
                                }
                            }
                            if (highlighted) break;
                        }
                    }

                    if (!highlighted)
                    {
                        System.Diagnostics.Debug.WriteLine($"警告: 脆性矿物列 {colName} 无法在数据网格中找到匹配");
                    }
                }

                // 高亮显示塑性矿物列
                System.Diagnostics.Debug.WriteLine($"尝试高亮 {_ductileColumns.Count} 个塑性矿物列");
                foreach (string colName in _ductileColumns)
                {
                    bool highlighted = false;

                    // 1. 尝试直接匹配
                    if (dgvResult.Columns.Contains(colName))
                    {
                        dgvResult.Columns[colName].DefaultCellStyle.BackColor = Color.FromArgb(120, 80, 100);
                        dgvResult.Columns[colName].DefaultCellStyle.ForeColor = Color.White;
                        dgvResult.Columns[colName].HeaderCell.Style.BackColor = Color.FromArgb(120, 80, 100);
                        dgvResult.Columns[colName].HeaderCell.Style.ForeColor = Color.White;
                        System.Diagnostics.Debug.WriteLine($"成功高亮塑性矿物列: {colName}");
                        highlighted = true;
                    }

                    // 2. 尝试使用映射查找
                    if (!highlighted && _columnMappings.TryGetValue(colName, out string mappedName))
                    {
                        if (dgvResult.Columns.Contains(mappedName))
                        {
                            dgvResult.Columns[mappedName].DefaultCellStyle.BackColor = Color.FromArgb(120, 80, 100);
                            dgvResult.Columns[mappedName].DefaultCellStyle.ForeColor = Color.White;
                            dgvResult.Columns[mappedName].HeaderCell.Style.BackColor = Color.FromArgb(120, 80, 100);
                            dgvResult.Columns[mappedName].HeaderCell.Style.ForeColor = Color.White;
                            System.Diagnostics.Debug.WriteLine($"通过映射成功高亮塑性矿物列: {colName} -> {mappedName}");
                            highlighted = true;
                        }
                    }

                    // 3. 尝试使用FindActualColumnName方法
                    if (!highlighted)
                    {
                        string actualColName = FindActualColumnName(colName);
                        if (!string.IsNullOrEmpty(actualColName) && dgvResult.Columns.Contains(actualColName))
                        {
                            dgvResult.Columns[actualColName].DefaultCellStyle.BackColor = Color.FromArgb(120, 80, 100);
                            dgvResult.Columns[actualColName].DefaultCellStyle.ForeColor = Color.White;
                            dgvResult.Columns[actualColName].HeaderCell.Style.BackColor = Color.FromArgb(120, 80, 100);
                            dgvResult.Columns[actualColName].HeaderCell.Style.ForeColor = Color.White;
                            System.Diagnostics.Debug.WriteLine($"通过FindActualColumnName成功高亮塑性矿物列: {colName} -> {actualColName}");
                            highlighted = true;
                        }
                    }

                    // 4. 尝试模糊匹配
                    if (!highlighted)
                    {
                        string cleanColName = colName.Replace("%", "").Trim().ToLower();
                        foreach (DataGridViewColumn col in dgvResult.Columns)
                        {
                            string cleanDataCol = col.Name.Replace("%", "").Trim().ToLower();
                            if (cleanDataCol.Contains(cleanColName) || cleanColName.Contains(cleanDataCol))
                            {
                                col.DefaultCellStyle.BackColor = Color.FromArgb(120, 80, 100);
                                col.DefaultCellStyle.ForeColor = Color.White;
                                col.HeaderCell.Style.BackColor = Color.FromArgb(120, 80, 100);
                                col.HeaderCell.Style.ForeColor = Color.White;
                                System.Diagnostics.Debug.WriteLine($"通过模糊匹配成功高亮塑性矿物列: {colName} -> {col.Name}");
                                highlighted = true;
                                break;
                            }
                        }
                    }

                    // 5. 尝试使用全局映射
                    if (!highlighted)
                    {
                        foreach (var mapping in _globalDuctileMappings)
                        {
                            if (mapping.Key == colName || mapping.Value.Contains(colName))
                            {
                                foreach (string pattern in mapping.Value)
                                {
                                    string cleanPattern = pattern.Replace("%", "").Trim().ToLower();
                                    foreach (DataGridViewColumn col in dgvResult.Columns)
                                    {
                                        string cleanDataCol = col.Name.Replace("%", "").Trim().ToLower();
                                        if (cleanDataCol.Contains(cleanPattern) || cleanPattern.Contains(cleanDataCol))
                                        {
                                            col.DefaultCellStyle.BackColor = Color.FromArgb(120, 80, 100);
                                            col.DefaultCellStyle.ForeColor = Color.White;
                                            col.HeaderCell.Style.BackColor = Color.FromArgb(120, 80, 100);
                                            col.HeaderCell.Style.ForeColor = Color.White;
                                            System.Diagnostics.Debug.WriteLine($"通过全局映射成功高亮塑性矿物列: {colName} -> {pattern} -> {col.Name}");
                                            highlighted = true;
                                            break;
                                        }
                                    }
                                    if (highlighted) break;
                                }
                            }
                            if (highlighted) break;
                        }
                    }

                    if (!highlighted)
                    {
                        System.Diagnostics.Debug.WriteLine($"警告: 塑性矿物列 {colName} 无法在数据网格中找到匹配");
                    }
                }

                // 强制刷新UI
                dgvResult.Refresh();
                Application.DoEvents();

                System.Diagnostics.Debug.WriteLine("完成高亮显示源数据表中的选中列");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮显示源数据表中的选中列时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }
        // 高亮显示结果表中的列
        private void HighlightResultColumns()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("高亮显示结果表中的列");

                // 确保数据网格视图有数据
                if (dgvResult.DataSource == null || dgvResult.Columns.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("警告: 数据网格视图没有数据或列，无法高亮显示");
                    return;
                }

                // 输出当前数据网格视图的列信息
                System.Diagnostics.Debug.WriteLine("当前数据网格视图列信息: ");
                foreach (DataGridViewColumn col in dgvResult.Columns)
                {
                    System.Diagnostics.Debug.WriteLine($"  列名: {col.Name}, 标题: {col.HeaderText}, 索引: {col.Index}");
                }

                // 重置所有列的样式
                foreach (DataGridViewColumn col in dgvResult.Columns)
                {
                    col.DefaultCellStyle.BackColor = Color.FromArgb(50, 50, 50);
                    col.DefaultCellStyle.ForeColor = Color.White;
                    col.HeaderCell.Style.BackColor = Color.FromArgb(50, 50, 50);
                    col.HeaderCell.Style.ForeColor = Color.White;
                }

                System.Diagnostics.Debug.WriteLine("已重置所有列的样式");

                // 高亮显示顶深和底深列
                if (dgvResult.Columns.Contains("顶深/m"))
                {
                    dgvResult.Columns["顶深/m"].DefaultCellStyle.BackColor = Color.SeaGreen;
                    dgvResult.Columns["顶深/m"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["顶深/m"].HeaderCell.Style.BackColor = Color.SeaGreen;
                    dgvResult.Columns["顶深/m"].HeaderCell.Style.ForeColor = Color.White;
                    System.Diagnostics.Debug.WriteLine("成功高亮顶深列: 顶深/m");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告: 顶深列 '顶深/m' 在结果表中不存在");
                }

                if (dgvResult.Columns.Contains("底深/m"))
                {
                    dgvResult.Columns["底深/m"].DefaultCellStyle.BackColor = Color.SeaGreen;
                    dgvResult.Columns["底深/m"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["底深/m"].HeaderCell.Style.BackColor = Color.SeaGreen;
                    dgvResult.Columns["底深/m"].HeaderCell.Style.ForeColor = Color.White;
                    System.Diagnostics.Debug.WriteLine("成功高亮底深列: 底深/m");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告: 底深列 '底深/m' 在结果表中不存在");
                }

                // 高亮显示脆性矿物总量列
                if (dgvResult.Columns.Contains("脆性矿物总量"))
                {
                    dgvResult.Columns["脆性矿物总量"].DefaultCellStyle.BackColor = Color.FromArgb(80, 100, 120);
                    dgvResult.Columns["脆性矿物总量"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["脆性矿物总量"].HeaderCell.Style.BackColor = Color.FromArgb(80, 100, 120);
                    dgvResult.Columns["脆性矿物总量"].HeaderCell.Style.ForeColor = Color.White;
                    System.Diagnostics.Debug.WriteLine("成功高亮脆性矿物总量列");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告: 脆性矿物总量列在结果表中不存在");
                }

                // 高亮显示塑性矿物总量列
                if (dgvResult.Columns.Contains("塑性矿物总量"))
                {
                    dgvResult.Columns["塑性矿物总量"].DefaultCellStyle.BackColor = Color.FromArgb(120, 80, 100);
                    dgvResult.Columns["塑性矿物总量"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["塑性矿物总量"].HeaderCell.Style.BackColor = Color.FromArgb(120, 80, 100);
                    dgvResult.Columns["塑性矿物总量"].HeaderCell.Style.ForeColor = Color.White;
                    System.Diagnostics.Debug.WriteLine("成功高亮塑性矿物总量列");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告: 塑性矿物总量列在结果表中不存在");
                }

                // 高亮显示脆性矿物列
                if (dgvResult.Columns.Contains("脆性矿物"))
                {
                    dgvResult.Columns["脆性矿物"].DefaultCellStyle.BackColor = Color.FromArgb(80, 100, 120);
                    dgvResult.Columns["脆性矿物"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["脆性矿物"].HeaderCell.Style.BackColor = Color.FromArgb(80, 100, 120);
                    dgvResult.Columns["脆性矿物"].HeaderCell.Style.ForeColor = Color.White;
                    System.Diagnostics.Debug.WriteLine("成功高亮脆性矿物列");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告: 脆性矿物列在结果表中不存在");
                }

                // 高亮显示塑性矿物列
                if (dgvResult.Columns.Contains("塑性矿物"))
                {
                    dgvResult.Columns["塑性矿物"].DefaultCellStyle.BackColor = Color.FromArgb(120, 80, 100);
                    dgvResult.Columns["塑性矿物"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["塑性矿物"].HeaderCell.Style.BackColor = Color.FromArgb(120, 80, 100);
                    dgvResult.Columns["塑性矿物"].HeaderCell.Style.ForeColor = Color.White;
                    System.Diagnostics.Debug.WriteLine("成功高亮塑性矿物列");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告: 塑性矿物列在结果表中不存在");
                }

                // 高亮显示脆性指数列
                if (dgvResult.Columns.Contains("脆性指数"))
                {
                    dgvResult.Columns["脆性指数"].DefaultCellStyle.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns["脆性指数"].DefaultCellStyle.ForeColor = Color.White;
                    dgvResult.Columns["脆性指数"].HeaderCell.Style.BackColor = Color.FromArgb(100, 100, 150);
                    dgvResult.Columns["脆性指数"].HeaderCell.Style.ForeColor = Color.White;
                    System.Diagnostics.Debug.WriteLine("成功高亮脆性指数列");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告: 脆性指数列在结果表中不存在");
                }

                // 强制刷新UI
                dgvResult.Refresh();
                Application.DoEvents();

                System.Diagnostics.Debug.WriteLine("完成高亮显示结果表中的列");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮显示结果表中的列时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }
        // 高亮显示指定列
        private void HighlightColumn(string columnName)
        {
            try
            {
                // 确保数据网格视图有数据
                if (dgvResult.DataSource == null || dgvResult.Columns.Count == 0)
                    return;

                // 检查列是否存在
                if (!dgvResult.Columns.Contains(columnName))
                {
                    System.Diagnostics.Debug.WriteLine($"警告: 列 {columnName} 在数据网格中不存在");
                    return;
                }

                // 设置列的样式
                dgvResult.Columns[columnName].DefaultCellStyle.BackColor = Color.SeaGreen;
                dgvResult.Columns[columnName].DefaultCellStyle.ForeColor = Color.White;
                System.Diagnostics.Debug.WriteLine($"高亮列: {columnName}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"高亮列 {columnName} 时出错: {ex.Message}");
            }
        }
        // 更新结果数据表
        private void UpdateResultDataTable()
        {
            try
            {
                // 清空现有数据
                _resultData.Rows.Clear();

                // 从数据点列表中填充结果表
                foreach (BrittlenessDataPoint point in _dataPoints)
                {
                    DataRow newRow = _resultData.NewRow();
                    newRow["GeoID"] = point.GeoID;
                    newRow["顶深/m"] = point.TopDepth;
                    newRow["底深/m"] = point.BottomDepth;
                    newRow["脆性指数"] = point.BrittleIndex;

                    // 计算脆性矿物总量和塑性矿物总量
                    double brittleMineralsTotal = 0;
                    double ductileMineralsTotal = 0;

                    // 解析脆性矿物列表中的值
                    foreach (string mineral in point.BrittleMinerals)
                    {
                        string[] parts = mineral.Split('=');
                        if (parts.Length == 2 && double.TryParse(parts[1], out double value))
                        {
                            brittleMineralsTotal += value;
                        }
                    }

                    // 解析塑性矿物列表中的值
                    foreach (string mineral in point.DuctileMinerals)
                    {
                        string[] parts = mineral.Split('=');
                        if (parts.Length == 2 && double.TryParse(parts[1], out double value))
                        {
                            ductileMineralsTotal += value;
                        }
                    }

                    // 添加脆性矿物总量和塑性矿物总量
                    newRow["脆性矿物总量"] = brittleMineralsTotal;
                    newRow["塑性矿物总量"] = ductileMineralsTotal;

                    // 添加脆性矿物列表
                    string brittleMinerals = string.Join(", ", point.BrittleMinerals);
                    newRow["脆性矿物"] = brittleMinerals;

                    // 添加塑性矿物列表
                    string ductileMinerals = string.Join(", ", point.DuctileMinerals);
                    newRow["塑性矿物"] = ductileMinerals;

                    _resultData.Rows.Add(newRow);
                }

                // 设置列头可见
                dgvResult.ColumnHeadersVisible = true;

                // 设置列宽度
                if (dgvResult.Columns.Contains("GeoID"))
                    dgvResult.Columns["GeoID"].Visible = false; // 隐藏GeoID列

                System.Diagnostics.Debug.WriteLine($"更新结果表完成，共 {_resultData.Rows.Count} 行数据");

                // 高亮显示选中的列
                HighlightSelectedColumns();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新结果数据表时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }
        // 处理单行数据
        private void ProcessRow(int rowIndex)
        {
            try
            {
                DataRow row = _sourceData.Rows[rowIndex];

                // 获取顶深和底深值
                double topDepth = 0;
                double bottomDepth = 0;

                // 确保能够正确转换数值
                if (row[topDepthIndex] != DBNull.Value &&
                    double.TryParse(row[topDepthIndex].ToString(), out topDepth) &&
                    row[bottomDepthIndex] != DBNull.Value &&
                    double.TryParse(row[bottomDepthIndex].ToString(), out bottomDepth))
                {
                    // 创建新的数据点
                    BrittlenessDataPoint dataPoint = new BrittlenessDataPoint
                    {
                        TopDepth = topDepth,
                        BottomDepth = bottomDepth,
                        RowIndex = rowIndex
                    };

                    // 计算脆性和塑性矿物含量
                    double brittleSum = 0;
                    double ductileSum = 0;
                    bool hasBrittleMinerals = false;
                    bool hasDuctileMinerals = false;

                    // 输出所有列名，帮助调试
                    System.Diagnostics.Debug.WriteLine($"处理行 {rowIndex} 时的所有列名和值:");
                    foreach (DataColumn col in _sourceData.Columns)
                    {
                        object value = row[col];
                        string valueStr = value == DBNull.Value ? "NULL" : value.ToString();
                        System.Diagnostics.Debug.WriteLine($"  列名: {col.ColumnName}, 值: {valueStr}");

                        // 检查列名是否在脆性或塑性列表中
                        bool inBrittleList = _brittleColumns.Contains(col.ColumnName);
                        bool inDuctileList = _ductileColumns.Contains(col.ColumnName);

                        // 检查列名是否包含特殊字符
                        bool hasSpecialChars = col.ColumnName.Contains("[") || col.ColumnName.Contains("]") ||
                                              col.ColumnName.Contains("(") || col.ColumnName.Contains(")") ||
                                              col.ColumnName.Contains("（") || col.ColumnName.Contains("）");

                        if (inBrittleList)
                        {
                            System.Diagnostics.Debug.WriteLine($"    该列在脆性矿物列表中");
                        }
                        else if (inDuctileList)
                        {
                            System.Diagnostics.Debug.WriteLine($"    该列在塑性矿物列表中");
                        }

                        if (hasSpecialChars)
                        {
                            System.Diagnostics.Debug.WriteLine($"    该列名包含特殊字符，可能需要特殊处理");
                        }
                    }

                    // 添加脆性矿物
                    foreach (string column in _brittleColumns)
                    {
                        // 尝试直接匹配列名（不区分大小写）
                        bool found = false;
                        foreach (DataColumn dataColumn in _sourceData.Columns)
                        {
                            if (string.Equals(dataColumn.ColumnName, column, StringComparison.OrdinalIgnoreCase))
                            {
                                double value = AlgorithmFormulaCalExtensions.SafeParseDouble(row[dataColumn.ColumnName]);
                                if (value > 0)
                                {
                                    brittleSum += value;
                                    dataPoint.BrittleMinerals.Add($"{dataColumn.ColumnName}={value}");
                                    hasBrittleMinerals = true;
                                    found = true;
                                    System.Diagnostics.Debug.WriteLine($"行 {rowIndex} 直接匹配脆性矿物 {dataColumn.ColumnName} = {value}");
                                }
                                break;
                            }
                        }

                        if (!found)
                        {
                            // 尝试模糊匹配（列名包含关键词或关键词包含列名）
                            foreach (DataColumn dataColumn in _sourceData.Columns)
                            {
                                // 移除%符号进行比较
                                string cleanColumn = column.TrimEnd('%', ' ');
                                string cleanDataColumn = dataColumn.ColumnName.TrimEnd('%', ' ');

                                if (cleanDataColumn.Contains(cleanColumn, StringComparison.OrdinalIgnoreCase) ||
                                    cleanColumn.Contains(cleanDataColumn, StringComparison.OrdinalIgnoreCase))
                                {
                                    double value = AlgorithmFormulaCalExtensions.SafeParseDouble(row[dataColumn.ColumnName]);
                                    if (value > 0)
                                    {
                                        brittleSum += value;
                                        dataPoint.BrittleMinerals.Add($"{dataColumn.ColumnName}={value}");
                                        hasBrittleMinerals = true;
                                        found = true;
                                        System.Diagnostics.Debug.WriteLine($"行 {rowIndex} 模糊匹配脆性矿物 {dataColumn.ColumnName} = {value}");
                                    }
                                }
                            }
                        }

                        if (!found)
                        {
                            // 尝试使用全局映射
                            foreach (var mapping in _globalBrittleMappings)
                            {
                                if (mapping.Key.Equals(column, StringComparison.OrdinalIgnoreCase) ||
                                    mapping.Value.Contains(column, StringComparer.OrdinalIgnoreCase))
                                {
                                    foreach (string pattern in mapping.Value)
                                    {
                                        foreach (DataColumn dataColumn in _sourceData.Columns)
                                        {
                                            // 移除%符号进行比较
                                            string cleanPattern = pattern.TrimEnd('%', ' ');
                                            string cleanDataColumn = dataColumn.ColumnName.TrimEnd('%', ' ');

                                            if (cleanDataColumn.Contains(cleanPattern, StringComparison.OrdinalIgnoreCase) ||
                                                cleanPattern.Contains(cleanDataColumn, StringComparison.OrdinalIgnoreCase))
                                            {
                                                double value = AlgorithmFormulaCalExtensions.SafeParseDouble(row[dataColumn.ColumnName]);
                                                if (value > 0)
                                                {
                                                    brittleSum += value;
                                                    dataPoint.BrittleMinerals.Add($"{dataColumn.ColumnName}={value}");
                                                    hasBrittleMinerals = true;
                                                    found = true;
                                                    System.Diagnostics.Debug.WriteLine($"行 {rowIndex} 全局映射脆性矿物 {dataColumn.ColumnName} = {value}");
                                                    break;
                                                }
                                            }
                                        }
                                        if (found) break;
                                    }
                                }
                                if (found) break;
                            }
                        }

                        if (!found)
                        {
                            System.Diagnostics.Debug.WriteLine($"警告: 脆性矿物列 {column} 在数据源中不存在或无法匹配");
                        }
                    }

                    // 添加塑性矿物
                    foreach (string column in _ductileColumns)
                    {
                        // 尝试直接匹配列名（不区分大小写）
                        bool found = false;
                        foreach (DataColumn dataColumn in _sourceData.Columns)
                        {
                            if (string.Equals(dataColumn.ColumnName, column, StringComparison.OrdinalIgnoreCase))
                            {
                                double value = AlgorithmFormulaCalExtensions.SafeParseDouble(row[dataColumn.ColumnName]);
                                if (value > 0)
                                {
                                    ductileSum += value;
                                    dataPoint.DuctileMinerals.Add($"{dataColumn.ColumnName}={value}");
                                    hasDuctileMinerals = true;
                                    found = true;
                                    System.Diagnostics.Debug.WriteLine($"行 {rowIndex} 直接匹配塑性矿物 {dataColumn.ColumnName} = {value}");
                                }
                                break;
                            }
                        }

                        if (!found)
                        {
                            // 尝试模糊匹配（列名包含关键词或关键词包含列名）
                            foreach (DataColumn dataColumn in _sourceData.Columns)
                            {
                                // 移除%符号进行比较
                                string cleanColumn = column.TrimEnd('%', ' ');
                                string cleanDataColumn = dataColumn.ColumnName.TrimEnd('%', ' ');

                                if (cleanDataColumn.Contains(cleanColumn, StringComparison.OrdinalIgnoreCase) ||
                                    cleanColumn.Contains(cleanDataColumn, StringComparison.OrdinalIgnoreCase))
                                {
                                    double value = AlgorithmFormulaCalExtensions.SafeParseDouble(row[dataColumn.ColumnName]);
                                    if (value > 0)
                                    {
                                        ductileSum += value;
                                        dataPoint.DuctileMinerals.Add($"{dataColumn.ColumnName}={value}");
                                        hasDuctileMinerals = true;
                                        found = true;
                                        System.Diagnostics.Debug.WriteLine($"行 {rowIndex} 模糊匹配塑性矿物 {dataColumn.ColumnName} = {value}");
                                    }
                                }
                            }
                        }

                        if (!found)
                        {
                            // 尝试使用全局映射
                            foreach (var mapping in _globalDuctileMappings)
                            {
                                if (mapping.Key.Equals(column, StringComparison.OrdinalIgnoreCase) ||
                                    mapping.Value.Contains(column, StringComparer.OrdinalIgnoreCase))
                                {
                                    foreach (string pattern in mapping.Value)
                                    {
                                        foreach (DataColumn dataColumn in _sourceData.Columns)
                                        {
                                            // 移除%符号进行比较
                                            string cleanPattern = pattern.TrimEnd('%', ' ');
                                            string cleanDataColumn = dataColumn.ColumnName.TrimEnd('%', ' ');

                                            if (cleanDataColumn.Contains(cleanPattern, StringComparison.OrdinalIgnoreCase) ||
                                                cleanPattern.Contains(cleanDataColumn, StringComparison.OrdinalIgnoreCase))
                                            {
                                                double value = AlgorithmFormulaCalExtensions.SafeParseDouble(row[dataColumn.ColumnName]);
                                                if (value > 0)
                                                {
                                                    ductileSum += value;
                                                    dataPoint.DuctileMinerals.Add($"{dataColumn.ColumnName}={value}");
                                                    hasDuctileMinerals = true;
                                                    found = true;
                                                    System.Diagnostics.Debug.WriteLine($"行 {rowIndex} 全局映射塑性矿物 {dataColumn.ColumnName} = {value}");
                                                    break;
                                                }
                                            }
                                        }
                                        if (found) break;
                                    }
                                }
                                if (found) break;
                            }
                        }

                        if (!found)
                        {
                            System.Diagnostics.Debug.WriteLine($"警告: 塑性矿物列 {column} 在数据源中不存在或无法匹配");
                        }
                    }

                    // 检查是否有有效的矿物数据
                    if (!hasBrittleMinerals && !hasDuctileMinerals)
                    {
                        System.Diagnostics.Debug.WriteLine($"跳过行 {rowIndex}: 没有找到有效的脆性或塑性矿物数据");
                        return;
                    }

                    // 计算脆性指数
                    double totalSum = brittleSum + ductileSum;
                    if (totalSum > 0)
                    {
                        dataPoint.BrittleIndex = (brittleSum / totalSum) * 100;
                    }
                    else
                    {
                        dataPoint.BrittleIndex = 0;
                    }

                    // 生成GeoID
                    dataPoint.GenerateGeoID();

                    // 添加到数据点列表
                    _dataPoints.Add(dataPoint);

                    // 输出详细的计算过程，帮助调试
                    System.Diagnostics.Debug.WriteLine($"处理行 {rowIndex} 的详细计算过程:");
                    System.Diagnostics.Debug.WriteLine($"  顶深 = {topDepth}");
                    System.Diagnostics.Debug.WriteLine($"  底深 = {bottomDepth}");
                    System.Diagnostics.Debug.WriteLine($"  脆性矿物总量 = {brittleSum}");
                    System.Diagnostics.Debug.WriteLine($"  脆性矿物列表:");
                    foreach (string mineral in dataPoint.BrittleMinerals)
                    {
                        System.Diagnostics.Debug.WriteLine($"    - {mineral}");
                    }
                    System.Diagnostics.Debug.WriteLine($"  塑性矿物总量 = {ductileSum}");
                    System.Diagnostics.Debug.WriteLine($"  塑性矿物列表:");
                    foreach (string mineral in dataPoint.DuctileMinerals)
                    {
                        System.Diagnostics.Debug.WriteLine($"    - {mineral}");
                    }
                    System.Diagnostics.Debug.WriteLine($"  总矿物量 = {totalSum}");
                    System.Diagnostics.Debug.WriteLine($"  脆性指数 = (脆性矿物总量 / 总矿物量) * 100 = ({brittleSum} / {totalSum}) * 100 = {dataPoint.BrittleIndex:F2}");
                    System.Diagnostics.Debug.WriteLine($"  GeoID = {dataPoint.GeoID}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"跳过行 {rowIndex}: 顶深或底深数据无效");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"处理行 {rowIndex} 时出错: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"处理行 {rowIndex} 时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        // 安全解析单元格值

        private double ParseCellValue(object cellValue)
        {
            try
            {
                if (cellValue == null || cellValue == DBNull.Value)
                    return 0;

                // 尝试直接转换为double
                if (cellValue is double doubleValue)
                    return doubleValue;

                // 尝试解析字符串
                if (double.TryParse(cellValue.ToString(), out double result))
                    return result;

                // 记录无法解析的值
                System.Diagnostics.Debug.WriteLine($"无法解析值: {cellValue}");
                return 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析单元格值时出错: {ex.Message}");
                return 0;
            }
        }
        // 列数据求和
        private double SumColumns(List<string> columns)
        {
            double sum = 0;

            if (_sourceData != null && _sourceData.Rows.Count > 0)
            {
                foreach (DataRow row in _sourceData.Rows)
                {
                    foreach (var col in columns)
                    {
                        if (_sourceData.Columns.Contains(col) && row[col] != DBNull.Value)
                        {
                            sum += Convert.ToDouble(row[col]);
                        }
                    }
                }
            }

            return sum;
        }

        // DataGridView单元格点击事件
        private void DgvResult_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            // 如果点击的是列头或者行头，则返回
            if (e.RowIndex < 0 || e.ColumnIndex < 0)
                return;

            // 获取点击的行
            DataGridViewRow row = dgvResult.Rows[e.RowIndex];

            // 高亮显示选中的行
            dgvResult.ClearSelection();
            row.Selected = true;

            // 获取GeoID
            string geoID = row.Cells["GeoID"].Value.ToString();

            // 查找对应的数据点
            BrittlenessDataPoint selectedPoint = _dataPoints.FirstOrDefault(p => p.GeoID == geoID);

            if (selectedPoint != null)
            {
                // 显示详细信息
                double topDepth = selectedPoint.TopDepth;
                double bottomDepth = selectedPoint.BottomDepth;
                double brittleIndex = selectedPoint.BrittleIndex;

                // 在状态栏显示详细信息
                resultLabel.Text = $"选中数据点: 顶深={topDepth}m, 底深={bottomDepth}m, 脆性指数={brittleIndex}%";

                // 如果有原始行索引，可以同步显示原始数据
                if (selectedPoint.RowIndex >= 0 && selectedPoint.RowIndex < _sourceData.Rows.Count)
                {
                    // 这里可以添加代码，在另一个表格中显示原始行数据
                    // 例如：dgvSource.ClearSelection();
                    // dgvSource.Rows[selectedPoint.RowIndex].Selected = true;

                    // 或者创建一个临时数据表显示选中行的所有列数据
                    DataTable selectedRowData = new DataTable();

                    // 添加列
                    foreach (DataColumn column in _sourceData.Columns)
                    {
                        selectedRowData.Columns.Add(column.ColumnName, column.DataType);
                    }

                    // 添加行数据
                    DataRow selectedRow = selectedRowData.NewRow();
                    for (int i = 0; i < _sourceData.Columns.Count; i++)
                    {
                        selectedRow[i] = _sourceData.Rows[selectedPoint.RowIndex][i];
                    }
                    selectedRowData.Rows.Add(selectedRow);

                    // 可以将这个临时表显示在另一个DataGridView中
                    // dgvSelectedRow.DataSource = selectedRowData;

                    // 或者在状态栏显示更多信息
                    resultLabel.Text += $" (原始行索引: {selectedPoint.RowIndex + 1})";
                }
            }
        }

        // 获取结果数据表
        public DataTable GetResultData()
        {
            // 创建一个新的DataTable用于返回到主页面
            DataTable resultTable = new DataTable();
            resultTable.Columns.Add("GeoID", typeof(string));
            resultTable.Columns.Add("顶深/m", typeof(double));
            resultTable.Columns.Add("底深/m", typeof(double));
            resultTable.Columns.Add("脆性指数", typeof(double));

            // 复制数据
            foreach (BrittlenessDataPoint point in _dataPoints)
            {
                DataRow newRow = resultTable.NewRow();
                newRow["GeoID"] = point.GeoID;
                newRow["顶深/m"] = point.TopDepth;
                newRow["底深/m"] = point.BottomDepth;
                newRow["脆性指数"] = point.BrittleIndex;
                resultTable.Rows.Add(newRow);
            }

            return resultTable;
        }

        // 可视化按钮事件
        private void BtnVisualize_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有数据可视化
                if (_resultData == null || _resultData.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可视化，请先计算脆性指数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"Visualizing {_dataPoints.Count} data points");
                foreach (var point in _dataPoints.Take(5))
                {
                    System.Diagnostics.Debug.WriteLine($"Sample point: {point.GeoID}, {point.TopDepth}, {point.BottomDepth}, {point.BrittleIndex}");
                }

                // 设置对话框结果为OK，表示要返回数据
                this.DialogResult = DialogResult.OK;

                // 关闭当前窗体
                this.Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in BtnVisualize_Click: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"可视化数据时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 保存数据按钮事件
        private void BtnSaveData_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查是否有数据可保存
                if (_dataPoints.Count == 0)
                {
                    MessageBox.Show("没有数据可保存，请先计算脆性指数！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 显示保存对话框
                using (SaveFileDialog saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.Filter = "Excel 文件 (*.xlsx)|*.xlsx|CSV 文件 (*.csv)|*.csv";
                    saveFileDialog.Title = "保存脆性指数数据";
                    saveFileDialog.FileName = $"脆性指数数据_{DateTime.Now:yyyyMMdd_HHmmss}";

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        string filePath = saveFileDialog.FileName;

                        // 根据文件类型保存数据
                        if (filePath.EndsWith(".csv", StringComparison.OrdinalIgnoreCase))
                        {
                            SaveToCsv(filePath);
                        }
                        else if (filePath.EndsWith(".xlsx", StringComparison.OrdinalIgnoreCase))
                        {
                            SaveToExcel(filePath);
                        }
                        else
                        {
                            MessageBox.Show("不支持的文件格式", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }

                        MessageBox.Show($"数据已成功保存到: {filePath}", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存数据时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 保存到CSV文件
        private void SaveToCsv(string filePath)
        {
            using (StreamWriter writer = new StreamWriter(filePath, false, Encoding.UTF8))
            {
                // 写入列头
                writer.WriteLine("GeoID,顶深/m,底深/m,脆性指数,脆性矿物,塑性矿物");

                // 写入数据行
                foreach (BrittlenessDataPoint point in _dataPoints)
                {
                    string brittleMinerals = string.Join("|", point.BrittleMinerals).Replace(",", ";");
                    string ductileMinerals = string.Join("|", point.DuctileMinerals).Replace(",", ";");

                    writer.WriteLine($"{point.GeoID},{point.TopDepth},{point.BottomDepth},{point.BrittleIndex},\"{brittleMinerals}\",\"{ductileMinerals}\"");
                }
            }
        }

        // 保存到Excel文件
        private void SaveToExcel(string filePath)
        {
            // 创建Excel工作簿
            NPOI.XSSF.UserModel.XSSFWorkbook workbook = new NPOI.XSSF.UserModel.XSSFWorkbook();
            NPOI.SS.UserModel.ISheet sheet = workbook.CreateSheet("脆性指数数据");

            // 创建列头样式
            NPOI.SS.UserModel.ICellStyle headerStyle = workbook.CreateCellStyle();
            NPOI.SS.UserModel.IFont headerFont = workbook.CreateFont();
            headerFont.IsBold = true;
            headerStyle.SetFont(headerFont);

            // 创建百分比格式样式
            NPOI.SS.UserModel.ICellStyle percentStyle = workbook.CreateCellStyle();
            percentStyle.DataFormat = workbook.CreateDataFormat().GetFormat("0.00%");

            // 创建数字格式样式
            NPOI.SS.UserModel.ICellStyle numberStyle = workbook.CreateCellStyle();
            numberStyle.DataFormat = workbook.CreateDataFormat().GetFormat("0.00");

            // 添加矿物成分合并列
            DataTable exportData = _resultData.Copy();
            exportData.Columns.Add("矿物成分", typeof(string), "脆性矿物 + ';' + 塑性矿物");

            // 创建列头行
            NPOI.SS.UserModel.IRow headerRow = sheet.CreateRow(0);
            string[] headers = { "GeoID", "顶深/m", "底深/m", "脆性指数", "脆性矿物", "塑性矿物", "矿物成分" };

            for (int i = 0; i < headers.Length; i++)
            {
                NPOI.SS.UserModel.ICell cell = headerRow.CreateCell(i);
                cell.SetCellValue(headers[i]);
                cell.CellStyle = headerStyle;

                // 设置列宽
                sheet.SetColumnWidth(i, 256 * 20); // 20个字符宽度
            }

            // 创建数据行
            for (int i = 0; i < _dataPoints.Count; i++)
            {
                BrittlenessDataPoint point = _dataPoints[i];
                NPOI.SS.UserModel.IRow row = sheet.CreateRow(i + 1);

                // 设置单元格值
                row.CreateCell(0).SetCellValue(point.GeoID);

                // 设置数字格式单元格
                NPOI.SS.UserModel.ICell topDepthCell = row.CreateCell(1);
                topDepthCell.SetCellValue(point.TopDepth);
                topDepthCell.CellStyle = numberStyle;

                NPOI.SS.UserModel.ICell bottomDepthCell = row.CreateCell(2);
                bottomDepthCell.SetCellValue(point.BottomDepth);
                bottomDepthCell.CellStyle = numberStyle;

                // 设置百分比格式单元格
                NPOI.SS.UserModel.ICell brittleIndexCell = row.CreateCell(3);
                brittleIndexCell.SetCellValue(point.BrittleIndex / 100.0); // 转换为小数以便于百分比格式化
                brittleIndexCell.CellStyle = percentStyle;

                // 设置文本单元格
                row.CreateCell(4).SetCellValue(string.Join(", ", point.BrittleMinerals));
                row.CreateCell(5).SetCellValue(string.Join(", ", point.DuctileMinerals));

                // 设置矿物成分列
                string mineralComposition = $"{string.Join(", ", point.BrittleMinerals)}; {string.Join(", ", point.DuctileMinerals)}";
                row.CreateCell(6).SetCellValue(mineralComposition);
            }

            // 保存到文件
            using (FileStream fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(fs);
            }
        }

        // 手动添加数据相关方法已移除

        // 初始化列名映射
        // 初始化列名映射
        private void InitializeColumnMappings()
        {
            // 清空现有映射
            _columnMappings.Clear();

            // 添加常见的列名映射（不区分大小写）
            // 顶深映射
            _columnMappings["顶深"] = "顶深/m";
            _columnMappings["顶深m"] = "顶深/m";
            _columnMappings["顶深（m）"] = "顶深/m";
            _columnMappings["顶深(m)"] = "顶深/m";
            _columnMappings["top depth"] = "顶深/m";

            // 底深映射
            _columnMappings["底深"] = "底深/m";
            _columnMappings["底深m"] = "底深/m";
            _columnMappings["底深（m）"] = "底深/m";
            _columnMappings["底深(m)"] = "底深/m";
            _columnMappings["bottom depth"] = "底深/m";

            // 矿物映射 - 脆性矿物
            _columnMappings["钾长石"] = "钾长石（正长石）%";
            _columnMappings["正长石"] = "钾长石（正长石）%";
            _columnMappings["钾长石%"] = "钾长石（正长石）%";
            _columnMappings["正长石%"] = "钾长石（正长石）%";

            _columnMappings["菱铁矿"] = "菱铁矿%";

            _columnMappings["斜长石"] = "斜长石%";

            _columnMappings["白云石"] = "白云石%";
            _columnMappings["白云岩"] = "白云石%";

            // 矿物映射 - 塑性矿物
            _columnMappings["黏土矿物"] = "黏土矿物总量%";
            _columnMappings["黏土"] = "黏土矿物总量%";
            _columnMappings["黏土矿物总量"] = "黏土矿物总量%";

            // 添加更多映射...
        }

        // 初始化全局映射
        private void InitializeGlobalMappings()
        {
            // 清空现有映射
            _globalBrittleMappings.Clear();
            _globalDuctileMappings.Clear();

            // 脆性矿物映射组
            _globalBrittleMappings["石英%"] = new List<string> {
                "石英", "石英%", "石英含量", "Quartz", "石英含量/%", "SiO2", "石英含量", "石英矿物", "石英矿物含量"
            };

            _globalBrittleMappings["钾长石（正长石）%"] = new List<string> {
                "钾长石", "正长石", "钾长石%", "正长石%", "钾长石（正长石）%", "K-feldspar", "长石",
                "钾长石（正长石）", "钾长石(正长石)", "钾长石(正长石)%", "正长石含量", "钾长石含量"
            };

            _globalBrittleMappings["菱铁矿%"] = new List<string> {
                "菱铁矿", "菱铁矿%", "siderite", "碳酸盐", "碳酸盐矿物", "菱铁矿含量", "菱铁矿含量/%"
            };

            _globalBrittleMappings["斜长石%"] = new List<string> {
                "斜长石", "斜长石%", "plagioclase", "长石", "斜长石含量", "斜长石含量/%"
            };

            _globalBrittleMappings["白云石%"] = new List<string> {
                "白云石", "白云岩", "白云石%", "白云岩%", "dolomite", "碳酸盐", "碳酸盐矿物",
                "白云石含量", "白云岩含量", "白云石含量/%", "白云岩含量/%"
            };

            _globalBrittleMappings["方解石%"] = new List<string> {
                "方解石", "方解石%", "calcite", "碳酸盐", "碳酸盐矿物", "方解石含量", "方解石含量/%"
            };

            _globalBrittleMappings["脆性矿物含量/%"] = new List<string> {
                "脆性矿物含量", "脆性矿物含量/%", "脆性矿物", "脆性矿物总量", "脆性矿物总量/%",
                "brittle minerals", "brittle mineral content"
            };

            // 塑性矿物映射组
            _globalDuctileMappings["黏土矿物总量%"] = new List<string> {
                "黏土", "黏土矿物", "黏土矿物总量", "黏土矿物总量%", "clay minerals", "黏土含量",
                "黏土矿物含量", "黏土矿物总量含量", "黏土总量", "黏土总量%"
            };

            _globalDuctileMappings["高岭石（K)"] = new List<string> {
                "高岭石", "高岭石(K)", "高岭石（K)", "高岭石(K）", "高岭石（K）", "kaolinite",
                "高岭石含量", "高岭石%", "高岭石含量/%"
            };

            _globalDuctileMappings["绿泥石（C）"] = new List<string> {
                "绿泥石", "绿泥石(C)", "绿泥石（C)", "绿泥石(C）", "绿泥石（C）", "chlorite",
                "绿泥石含量", "绿泥石%", "绿泥石含量/%"
            };

            _globalDuctileMappings["伊利石（It）"] = new List<string> {
                "伊利石", "伊利石%", "illite", "黏土矿物", "伊利石(It)", "伊利石（It)", "伊利石(It）",
                "伊利石（It）", "伊利石含量", "伊利石含量/%"
            };

            _globalDuctileMappings["伊/蒙间层矿物（I/S）"] = new List<string> {
                "伊/蒙间层矿物", "伊/蒙间层矿物（I/S）", "伊/蒙间层矿物(I/S)", "伊蒙混层", "伊蒙混层矿物",
                "伊/蒙混层", "伊/蒙混层矿物", "I/S", "伊蒙间层", "伊蒙间层矿物", "伊/蒙间层矿物含量",
                "伊/蒙间层矿物%", "伊/蒙间层矿物含量/%"
            };

            _globalDuctileMappings["蒙脱石"] = new List<string> {
                "蒙脱石", "蒙脱石%", "montmorillonite", "黏土矿物", "蒙脱石含量", "蒙脱石含量/%"
            };

            System.Diagnostics.Debug.WriteLine("全局映射初始化完成:");
            System.Diagnostics.Debug.WriteLine("脆性矿物映射:");
            foreach (var mapping in _globalBrittleMappings)
            {
                System.Diagnostics.Debug.WriteLine($"  {mapping.Key}: {string.Join(", ", mapping.Value)}");
            }
            System.Diagnostics.Debug.WriteLine("塑性矿物映射:");
            foreach (var mapping in _globalDuctileMappings)
            {
                System.Diagnostics.Debug.WriteLine($"  {mapping.Key}: {string.Join(", ", mapping.Value)}");
            }
        }

        // 获取实际的列名（考虑映射关系）
        private string GetActualColumnName(string columnName)
        {
            // 首先检查列名是否直接存在于数据表中
            if (_sourceData.Columns.Contains(columnName))
            {
                return columnName;
            }

            // 检查是否有映射
            if (_columnMappings.TryGetValue(columnName, out string mappedName))
            {
                // 检查映射的列名是否存在
                if (_sourceData.Columns.Contains(mappedName))
                {
                    return mappedName;
                }
            }

            // 尝试不区分大小写查找列名
            foreach (DataColumn column in _sourceData.Columns)
            {
                if (string.Equals(column.ColumnName, columnName, StringComparison.OrdinalIgnoreCase))
                {
                    return column.ColumnName;
                }
            }

            // 如果找不到，输出调试信息并返回原始列名
            System.Diagnostics.Debug.WriteLine($"警告：找不到列 '{columnName}'，也没有对应的映射");
            return columnName;
        }

        // 添加安全的数据解析方法
        // 安全解析单元格数据
        private double ParseCell(object cellValue)
        {
            try
            {
                if (cellValue == null || cellValue == DBNull.Value)
                    return 0;

                // 尝试解析为双精度浮点数
                if (double.TryParse(cellValue.ToString(), out double result))
                    return result;

                // 如果解析失败，记录日志并返回0
                System.Diagnostics.Debug.WriteLine($"无法解析值: {cellValue}");
                return 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析单元格值时出错: {ex.Message}");
                return 0;
            }
        }

        // 获取安全的单元格值
        private double GetSafeValue(DataRow row, string columnName)
        {
            try
            {
                // 获取实际列名
                string actualColumnName = GetActualColumnName(columnName);

                // 如果列存在，返回解析后的值
                if (_sourceData.Columns.Contains(actualColumnName))
                    return ParseCell(row[actualColumnName]);

                // 如果列不存在，记录日志并返回0
                System.Diagnostics.Debug.WriteLine($"列 '{columnName}' (映射为 '{actualColumnName}') 不存在于数据表中");
                return 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取列 '{columnName}' 的值时出错: {ex.Message}");
                return 0;
            }
        }
        // 输出所有列名和示例数据
        private void PrintAllColumnNames()
        {
            // 调用重载方法，传入当前源数据表
            PrintAllColumnNames(_sourceData);
        }

        // 输出所有列名和示例数据 - 详细版本
        private void PrintAllColumnNames(DataTable dataTable)
        {
            try
            {
                if (dataTable == null || dataTable.Columns.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("数据表为空或没有列");
                    return;
                }

                System.Diagnostics.Debug.WriteLine("==================== 数据表所有列名和示例数据 ====================");
                System.Diagnostics.Debug.WriteLine($"数据表: {dataTable.TableName}, 行数: {dataTable.Rows.Count}, 列数: {dataTable.Columns.Count}");

                // 输出列名和类型
                System.Diagnostics.Debug.WriteLine("\n列信息:");
                foreach (DataColumn column in dataTable.Columns)
                {
                    System.Diagnostics.Debug.WriteLine($"列名: '{column.ColumnName}', 类型: {column.DataType.Name}, 序号: {column.Ordinal}");

                    // 尝试获取该列的示例数据（前3行）
                    if (dataTable.Rows.Count > 0)
                    {
                        System.Diagnostics.Debug.WriteLine("  示例数据:");
                        int sampleCount = Math.Min(3, dataTable.Rows.Count);
                        for (int i = 0; i < sampleCount; i++)
                        {
                            object value = dataTable.Rows[i][column];
                            string valueStr = value == DBNull.Value ? "NULL" : value.ToString();
                            System.Diagnostics.Debug.WriteLine($"    行{i + 1}: {valueStr}");
                        }
                    }

                    // 只有当传入的是源数据表时才检查分类信息
                    if (dataTable == _sourceData)
                    {
                        // 检查该列是否在脆性或塑性矿物列表中
                        if (_brittleColumns.Contains(column.ColumnName))
                        {
                            System.Diagnostics.Debug.WriteLine("  分类: 脆性矿物列");
                        }
                        else if (_ductileColumns.Contains(column.ColumnName))
                        {
                            System.Diagnostics.Debug.WriteLine("  分类: 塑性矿物列");
                        }
                        else if (column.ColumnName.Equals(topDepthColumnName, StringComparison.OrdinalIgnoreCase))
                        {
                            System.Diagnostics.Debug.WriteLine("  分类: 顶深列");
                        }
                        else if (column.ColumnName.Equals(bottomDepthColumnName, StringComparison.OrdinalIgnoreCase))
                        {
                            System.Diagnostics.Debug.WriteLine("  分类: 底深列");
                        }

                        // 检查是否有映射
                        foreach (var mapping in _columnMappings)
                        {
                            if (mapping.Value.Equals(column.ColumnName, StringComparison.OrdinalIgnoreCase))
                            {
                                System.Diagnostics.Debug.WriteLine($"  映射: '{mapping.Key}' -> '{mapping.Value}'");
                            }
                        }

                        // 检查全局映射
                        foreach (var mapping in _globalBrittleMappings)
                        {
                            if (mapping.Value.Contains(column.ColumnName, StringComparer.OrdinalIgnoreCase))
                            {
                                System.Diagnostics.Debug.WriteLine($"  全局脆性矿物映射: '{mapping.Key}' 包含 '{column.ColumnName}'");
                            }
                        }

                        foreach (var mapping in _globalDuctileMappings)
                        {
                            if (mapping.Value.Contains(column.ColumnName, StringComparer.OrdinalIgnoreCase))
                            {
                                System.Diagnostics.Debug.WriteLine($"  全局塑性矿物映射: '{mapping.Key}' 包含 '{column.ColumnName}'");
                            }
                        }
                    }

                    System.Diagnostics.Debug.WriteLine(""); // 空行分隔
                }

                // 只有当传入的是源数据表时才输出选择的列信息
                if (dataTable == _sourceData)
                {
                    // 输出当前选择的列
                    System.Diagnostics.Debug.WriteLine("\n当前选择的列:");
                    System.Diagnostics.Debug.WriteLine($"顶深列: {(string.IsNullOrEmpty(topDepthColumnName) ? "未选择" : topDepthColumnName)}, 索引: {topDepthIndex}");
                    System.Diagnostics.Debug.WriteLine($"底深列: {(string.IsNullOrEmpty(bottomDepthColumnName) ? "未选择" : bottomDepthColumnName)}, 索引: {bottomDepthIndex}");

                    System.Diagnostics.Debug.WriteLine("\n脆性矿物列:");
                    foreach (string col in _brittleColumns)
                    {
                        string actualCol = FindActualColumnName(col);
                        System.Diagnostics.Debug.WriteLine($"  - {col} (实际列名: {actualCol})");
                    }

                    System.Diagnostics.Debug.WriteLine("\n塑性矿物列:");
                    foreach (string col in _ductileColumns)
                    {
                        string actualCol = FindActualColumnName(col);
                        System.Diagnostics.Debug.WriteLine($"  - {col} (实际列名: {actualCol})");
                    }
                }

                System.Diagnostics.Debug.WriteLine("=================================================================");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"输出列名时出错: {ex.Message}\n{ex.StackTrace}");
            }
        }
        // 设置控件的Anchor属性
        private void SetControlAnchors()
        {
            // 设置主要面板的Dock属性
            leftPanel.Dock = DockStyle.Left;
            rightPanel.Dock = DockStyle.Fill;

            // 设置左侧面板中的控件
            lstAvailableColumns.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            btnAddBrittle.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnRemoveBrittle.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnAddDuctile.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnRemoveDuctile.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            // 设置右侧面板中的控件
            dgvResult.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            btnCalculate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnLoadData.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            btnSaveData.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnVisualize.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            // 设置其他控件
            lstBrittleColumns.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            lstDuctileColumns.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;

            // 设置顶深和底深控件
            btnTopDepth.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            lblTopDepth.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            btnBottomDepth.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            lblBottomDepth.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
        }
    }
}

// 在类外添加这两个方法的扩展类
public static class AlgorithmFormulaCalExtensions
{
    // 安全解析双精度浮点数
    public static double SafeParseDouble(object value)
    {
        try
        {
            if (value == null || value == DBNull.Value)
            {
                return 0;
            }

            // 尝试直接转换
            if (value is double doubleValue)
            {
                return doubleValue;
            }

            // 尝试从字符串转换
            string stringValue = value.ToString().Trim();

            // 处理百分比符号
            if (stringValue.EndsWith("%"))
            {
                stringValue = stringValue.TrimEnd('%');
                if (double.TryParse(stringValue, out double percentValue))
                {
                    return percentValue;
                }
            }

            // 处理科学计数法
            if (stringValue.Contains("E", StringComparison.OrdinalIgnoreCase) ||
                stringValue.Contains("e", StringComparison.OrdinalIgnoreCase))
            {
                if (double.TryParse(stringValue, System.Globalization.NumberStyles.Float,
                                   System.Globalization.CultureInfo.InvariantCulture, out double scientificValue))
                {
                    return scientificValue;
                }
            }

            // 标准解析
            if (double.TryParse(stringValue, out double result))
            {
                return result;
            }

            // 处理可能的逗号小数点
            stringValue = stringValue.Replace(',', '.');
            if (double.TryParse(stringValue, System.Globalization.NumberStyles.Any,
                               System.Globalization.CultureInfo.InvariantCulture, out double commaResult))
            {
                return commaResult;
            }

            // 如果所有尝试都失败，记录并返回0
            System.Diagnostics.Debug.WriteLine($"无法解析值: {value} 为双精度浮点数，返回0");
            return 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"解析双精度浮点数时出错: {ex.Message}，返回0");
            return 0;
        }
    }

    // 保存计算日志到文件
    public static void SaveCalculationLog(string logContent)
    {
        try
        {
            // 创建日志目录
            string logDir = Path.Combine(Application.StartupPath, "Logs");
            if (!Directory.Exists(logDir))
            {
                Directory.CreateDirectory(logDir);
            }

            // 创建日志文件名，包含时间戳
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string logFileName = Path.Combine(logDir, $"BrittlenessCalculation_{timestamp}.log");

            // 写入日志内容
            File.WriteAllText(logFileName, logContent);

            System.Diagnostics.Debug.WriteLine($"计算日志已保存到: {logFileName}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"保存计算日志时出错: {ex.Message}");
            MessageBox.Show($"保存计算日志时出错: {ex.Message}", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }
}
