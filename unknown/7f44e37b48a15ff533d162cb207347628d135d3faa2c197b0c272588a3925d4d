﻿namespace BritSystem
{
    partial class AlgorithmFormulaCal
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            leftPanel = new Panel();
            btnMapColumns = new Button();
            btnBottomDepth = new Button();
            btnTopDepth = new Button();
            lblBottomDepth = new Label();
            lblTopDepth = new Label();
            btnRemoveDuctile = new Button();
            btnRemoveBrittle = new Button();
            btnAddDuctile = new Button();
            btnAddBrittle = new Button();
            lstDuctileColumns = new ListBox();
            lblDuctile = new Label();
            lstBrittleColumns = new ListBox();
            lblBrittle = new Label();
            lstAvailableColumns = new ListBox();
            lblAvailable = new Label();
            rightPanel = new Panel();
            dgvResult = new DataGridView();
            resultLabel = new Label();
            btnCalculate = new Button();
            btnLoadData = new Button();
            btnVisualize = new Button();
            btnSaveData = new Button();
            formulaPanel = new Panel();
            formulaText = new Label();
            formulaLabel = new Label();
            btnTestData = new Button();
            btnShowData = new Button();
            leftPanel.SuspendLayout();
            rightPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dgvResult).BeginInit();
            formulaPanel.SuspendLayout();
            SuspendLayout();
            // 
            // leftPanel
            // 
            leftPanel.BackColor = Color.FromArgb(50, 50, 50);
            leftPanel.Controls.Add(btnMapColumns);
            leftPanel.Controls.Add(btnBottomDepth);
            leftPanel.Controls.Add(btnTopDepth);
            leftPanel.Controls.Add(lblBottomDepth);
            leftPanel.Controls.Add(lblTopDepth);
            leftPanel.Controls.Add(btnRemoveDuctile);
            leftPanel.Controls.Add(btnRemoveBrittle);
            leftPanel.Controls.Add(btnAddDuctile);
            leftPanel.Controls.Add(btnAddBrittle);
            leftPanel.Controls.Add(lstDuctileColumns);
            leftPanel.Controls.Add(lblDuctile);
            leftPanel.Controls.Add(lstBrittleColumns);
            leftPanel.Controls.Add(lblBrittle);
            leftPanel.Controls.Add(lstAvailableColumns);
            leftPanel.Controls.Add(lblAvailable);
            leftPanel.Dock = DockStyle.Left;
            leftPanel.Location = new Point(0, 0);
            leftPanel.Margin = new Padding(4);
            leftPanel.Name = "leftPanel";
            leftPanel.Size = new Size(505, 999);
            leftPanel.TabIndex = 0;
            // 
            // btnMapColumns
            // 
            btnMapColumns.BackColor = Color.FromArgb(70, 130, 180);
            btnMapColumns.FlatStyle = FlatStyle.Flat;
            btnMapColumns.Font = new Font("微软雅黑", 9F, FontStyle.Bold);
            btnMapColumns.ForeColor = Color.White;
            btnMapColumns.Location = new Point(24, 570);
            btnMapColumns.Margin = new Padding(4);
            btnMapColumns.Name = "btnMapColumns";
            btnMapColumns.Size = new Size(120, 30);
            btnMapColumns.TabIndex = 14;
            btnMapColumns.Text = "映射列名";
            btnMapColumns.UseVisualStyleBackColor = false;
            btnMapColumns.Click += BtnMapColumns_Click;
            // 
            // btnBottomDepth
            // 
            btnBottomDepth.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            btnBottomDepth.Location = new Point(128, 364);
            btnBottomDepth.Name = "btnBottomDepth";
            btnBottomDepth.Size = new Size(243, 34);
            btnBottomDepth.TabIndex = 12;
            btnBottomDepth.Text = "button2";
            btnBottomDepth.UseVisualStyleBackColor = true;
            // 
            // btnTopDepth
            // 
            btnTopDepth.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            btnTopDepth.Location = new Point(128, 304);
            btnTopDepth.Name = "btnTopDepth";
            btnTopDepth.Size = new Size(243, 34);
            btnTopDepth.TabIndex = 7;
            btnTopDepth.Text = "button1";
            btnTopDepth.UseVisualStyleBackColor = true;
            // 
            // lblBottomDepth
            // 
            lblBottomDepth.AutoSize = true;
            lblBottomDepth.Location = new Point(24, 364);
            lblBottomDepth.Name = "lblBottomDepth";
            lblBottomDepth.Size = new Size(71, 24);
            lblBottomDepth.TabIndex = 11;
            lblBottomDepth.Text = "底深/m";
            // 
            // lblTopDepth
            // 
            lblTopDepth.AutoSize = true;
            lblTopDepth.Location = new Point(24, 304);
            lblTopDepth.Name = "lblTopDepth";
            lblTopDepth.Size = new Size(71, 24);
            lblTopDepth.TabIndex = 10;
            lblTopDepth.Text = "顶深/m";
            // 
            // btnRemoveDuctile
            // 
            btnRemoveDuctile.BackColor = Color.FromArgb(80, 80, 80);
            btnRemoveDuctile.FlatStyle = FlatStyle.Flat;
            btnRemoveDuctile.ForeColor = Color.White;
            btnRemoveDuctile.Location = new Point(257, 714);
            btnRemoveDuctile.Margin = new Padding(4);
            btnRemoveDuctile.Name = "btnRemoveDuctile";
            btnRemoveDuctile.Size = new Size(98, 36);
            btnRemoveDuctile.TabIndex = 9;
            btnRemoveDuctile.Text = "← 移除";
            btnRemoveDuctile.UseVisualStyleBackColor = false;
            btnRemoveDuctile.Click += btnRemoveDuctile_Click;
            // 
            // btnRemoveBrittle
            // 
            btnRemoveBrittle.BackColor = Color.FromArgb(80, 80, 80);
            btnRemoveBrittle.FlatStyle = FlatStyle.Flat;
            btnRemoveBrittle.ForeColor = Color.White;
            btnRemoveBrittle.Location = new Point(257, 425);
            btnRemoveBrittle.Margin = new Padding(4);
            btnRemoveBrittle.Name = "btnRemoveBrittle";
            btnRemoveBrittle.Size = new Size(98, 36);
            btnRemoveBrittle.TabIndex = 8;
            btnRemoveBrittle.Text = "← 移除";
            btnRemoveBrittle.UseVisualStyleBackColor = false;
            btnRemoveBrittle.Click += btnRemoveBrittle_Click;
            // 
            // btnAddDuctile
            // 
            btnAddDuctile.BackColor = Color.SteelBlue;
            btnAddDuctile.FlatStyle = FlatStyle.Flat;
            btnAddDuctile.ForeColor = Color.White;
            btnAddDuctile.Location = new Point(367, 713);
            btnAddDuctile.Margin = new Padding(4);
            btnAddDuctile.Name = "btnAddDuctile";
            btnAddDuctile.Size = new Size(98, 36);
            btnAddDuctile.TabIndex = 7;
            btnAddDuctile.Text = "→ 塑性";
            btnAddDuctile.UseVisualStyleBackColor = false;
            btnAddDuctile.Click += btnAddDuctile_Click;
            // 
            // btnAddBrittle
            // 
            btnAddBrittle.BackColor = Color.SteelBlue;
            btnAddBrittle.FlatStyle = FlatStyle.Flat;
            btnAddBrittle.ForeColor = Color.White;
            btnAddBrittle.Location = new Point(367, 425);
            btnAddBrittle.Margin = new Padding(4);
            btnAddBrittle.Name = "btnAddBrittle";
            btnAddBrittle.Size = new Size(98, 36);
            btnAddBrittle.TabIndex = 6;
            btnAddBrittle.Text = "→ 脆性";
            btnAddBrittle.UseVisualStyleBackColor = false;
            btnAddBrittle.Click += btnAddBrittle_Click;
            // 
            // lstDuctileColumns
            // 
            lstDuctileColumns.BackColor = Color.FromArgb(120, 80, 100);
            lstDuctileColumns.BorderStyle = BorderStyle.FixedSingle;
            lstDuctileColumns.ForeColor = Color.White;
            lstDuctileColumns.FormattingEnabled = true;
            lstDuctileColumns.ItemHeight = 24;
            lstDuctileColumns.Location = new Point(24, 767);
            lstDuctileColumns.Margin = new Padding(4);
            lstDuctileColumns.Name = "lstDuctileColumns";
            lstDuctileColumns.Size = new Size(440, 194);
            lstDuctileColumns.TabIndex = 5;
            // 
            // lblDuctile
            // 
            lblDuctile.AutoSize = true;
            lblDuctile.ForeColor = Color.White;
            lblDuctile.Location = new Point(24, 719);
            lblDuctile.Margin = new Padding(4, 0, 4, 0);
            lblDuctile.Name = "lblDuctile";
            lblDuctile.Size = new Size(86, 24);
            lblDuctile.TabIndex = 4;
            lblDuctile.Text = "塑性矿物:";
            // 
            // lstBrittleColumns
            // 
            lstBrittleColumns.BackColor = Color.FromArgb(80, 100, 120);
            lstBrittleColumns.BorderStyle = BorderStyle.FixedSingle;
            lstBrittleColumns.ForeColor = Color.White;
            lstBrittleColumns.FormattingEnabled = true;
            lstBrittleColumns.ItemHeight = 24;
            lstBrittleColumns.Location = new Point(13, 483);
            lstBrittleColumns.Margin = new Padding(4);
            lstBrittleColumns.Name = "lstBrittleColumns";
            lstBrittleColumns.Size = new Size(451, 194);
            lstBrittleColumns.TabIndex = 3;
            // 
            // lblBrittle
            // 
            lblBrittle.AutoSize = true;
            lblBrittle.ForeColor = Color.White;
            lblBrittle.Location = new Point(24, 431);
            lblBrittle.Margin = new Padding(4, 0, 4, 0);
            lblBrittle.Name = "lblBrittle";
            lblBrittle.Size = new Size(86, 24);
            lblBrittle.TabIndex = 2;
            lblBrittle.Text = "脆性矿物:";
            // 
            // lstAvailableColumns
            // 
            lstAvailableColumns.BackColor = Color.FromArgb(60, 60, 60);
            lstAvailableColumns.BorderStyle = BorderStyle.FixedSingle;
            lstAvailableColumns.ForeColor = Color.White;
            lstAvailableColumns.FormattingEnabled = true;
            lstAvailableColumns.ItemHeight = 24;
            lstAvailableColumns.Location = new Point(24, 60);
            lstAvailableColumns.Margin = new Padding(4);
            lstAvailableColumns.Name = "lstAvailableColumns";
            lstAvailableColumns.SelectionMode = SelectionMode.MultiExtended;
            lstAvailableColumns.Size = new Size(440, 194);
            lstAvailableColumns.TabIndex = 1;
            lstAvailableColumns.DoubleClick += lstAvailableColumns_DoubleClick;
            // 
            // lblAvailable
            // 
            lblAvailable.AutoSize = true;
            lblAvailable.ForeColor = Color.White;
            lblAvailable.Location = new Point(24, 24);
            lblAvailable.Margin = new Padding(4, 0, 4, 0);
            lblAvailable.Name = "lblAvailable";
            lblAvailable.Size = new Size(68, 24);
            lblAvailable.TabIndex = 0;
            lblAvailable.Text = "可用列:";
            // 
            // rightPanel
            // 
            rightPanel.BackColor = Color.FromArgb(40, 40, 40);
            rightPanel.Controls.Add(dgvResult);
            rightPanel.Controls.Add(resultLabel);
            rightPanel.Controls.Add(btnCalculate);
            rightPanel.Controls.Add(btnLoadData);
            rightPanel.Controls.Add(btnVisualize);
            rightPanel.Controls.Add(btnSaveData);
            rightPanel.Controls.Add(formulaPanel);
            rightPanel.Controls.Add(btnTestData);
            rightPanel.Controls.Add(btnShowData);
            rightPanel.Dock = DockStyle.Fill;
            rightPanel.Location = new Point(505, 0);
            rightPanel.Margin = new Padding(4);
            rightPanel.Name = "rightPanel";
            rightPanel.Size = new Size(928, 999);
            rightPanel.TabIndex = 1;
            // 
            // dgvResult
            // 
            dgvResult.AllowUserToAddRows = false;
            dgvResult.AllowUserToDeleteRows = false;
            dgvResult.BackgroundColor = Color.FromArgb(60, 60, 60);
            dgvResult.BorderStyle = BorderStyle.Fixed3D;
            dgvResult.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dgvResult.GridColor = Color.FromArgb(80, 80, 80);
            dgvResult.Location = new Point(24, 252);
            dgvResult.Margin = new Padding(4);
            dgvResult.Name = "dgvResult";
            dgvResult.ReadOnly = true;
            dgvResult.RowHeadersWidth = 62;
            dgvResult.RowTemplate.Height = 25;
            dgvResult.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvResult.Size = new Size(884, 743);
            dgvResult.TabIndex = 3;
            dgvResult.CellClick += DgvResult_CellClick;
            // 
            // resultLabel
            // 
            resultLabel.AutoSize = true;
            resultLabel.ForeColor = Color.White;
            resultLabel.Location = new Point(24, 216);
            resultLabel.Margin = new Padding(4, 0, 4, 0);
            resultLabel.Name = "resultLabel";
            resultLabel.Size = new Size(86, 24);
            resultLabel.TabIndex = 2;
            resultLabel.Text = "计算结果:";
            // 
            // btnCalculate
            // 
            btnCalculate.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnCalculate.BackColor = Color.SeaGreen;
            btnCalculate.FlatStyle = FlatStyle.Flat;
            btnCalculate.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnCalculate.ForeColor = Color.White;
            btnCalculate.Location = new Point(736, 144);
            btnCalculate.Margin = new Padding(4);
            btnCalculate.Name = "btnCalculate";
            btnCalculate.Size = new Size(183, 48);
            btnCalculate.TabIndex = 1;
            btnCalculate.Text = "计算脆性指数";
            btnCalculate.UseVisualStyleBackColor = false;
            btnCalculate.Click += btnCalculate_Click;
            // 
            // btnLoadData
            // 
            btnLoadData.BackColor = Color.RoyalBlue;
            btnLoadData.FlatStyle = FlatStyle.Flat;
            btnLoadData.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnLoadData.ForeColor = Color.White;
            btnLoadData.Location = new Point(24, 144);
            btnLoadData.Margin = new Padding(4);
            btnLoadData.Name = "btnLoadData";
            btnLoadData.Size = new Size(183, 48);
            btnLoadData.TabIndex = 4;
            btnLoadData.Text = "加载数据";
            btnLoadData.UseVisualStyleBackColor = false;
            btnLoadData.Click += btnLoadData_Click;
            // 
            // btnVisualize
            // 
            btnVisualize.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnVisualize.BackColor = Color.DarkOrange;
            btnVisualize.FlatStyle = FlatStyle.Flat;
            btnVisualize.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnVisualize.ForeColor = Color.White;
            btnVisualize.Location = new Point(518, 144);
            btnVisualize.Margin = new Padding(4);
            btnVisualize.Name = "btnVisualize";
            btnVisualize.Size = new Size(183, 48);
            btnVisualize.TabIndex = 5;
            btnVisualize.Text = "可视化数据";
            btnVisualize.UseVisualStyleBackColor = false;
            btnVisualize.Click += BtnVisualize_Click;
            // 
            // btnSaveData
            // 
            btnSaveData.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnSaveData.BackColor = Color.SteelBlue;
            btnSaveData.FlatStyle = FlatStyle.Flat;
            btnSaveData.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnSaveData.ForeColor = Color.White;
            btnSaveData.Location = new Point(745, 1005);
            btnSaveData.Margin = new Padding(4);
            btnSaveData.Name = "btnSaveData";
            btnSaveData.Size = new Size(183, 48);
            btnSaveData.TabIndex = 6;
            btnSaveData.Text = "保存数据";
            btnSaveData.UseVisualStyleBackColor = false;
            btnSaveData.Click += BtnSaveData_Click;
            // 
            // formulaPanel
            // 
            formulaPanel.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            formulaPanel.AutoSize = true;
            formulaPanel.BackColor = Color.FromArgb(55, 55, 55);
            formulaPanel.BorderStyle = BorderStyle.FixedSingle;
            formulaPanel.Controls.Add(formulaText);
            formulaPanel.Controls.Add(formulaLabel);
            formulaPanel.Location = new Point(24, 24);
            formulaPanel.Margin = new Padding(4);
            formulaPanel.Name = "formulaPanel";
            formulaPanel.Size = new Size(895, 96);
            formulaPanel.TabIndex = 0;
            // 
            // formulaText
            // 
            formulaText.AutoSize = true;
            formulaText.Font = new Font("微软雅黑", 10F, FontStyle.Italic);
            formulaText.ForeColor = Color.Cyan;
            formulaText.Location = new Point(12, 48);
            formulaText.Margin = new Padding(4, 0, 4, 0);
            formulaText.Name = "formulaText";
            formulaText.Size = new Size(558, 27);
            formulaText.TabIndex = 1;
            formulaText.Text = "脆性指数 = [V脆性矿物 / (V脆性矿物 + V塑性矿物)] × 100%";
            // 
            // formulaLabel
            // 
            formulaLabel.AutoSize = true;
            formulaLabel.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            formulaLabel.ForeColor = Color.White;
            formulaLabel.Location = new Point(12, 12);
            formulaLabel.Margin = new Padding(4, 0, 4, 0);
            formulaLabel.Name = "formulaLabel";
            formulaLabel.Size = new Size(178, 27);
            formulaLabel.TabIndex = 0;
            formulaLabel.Text = "脆性指数计算公式:";
            // 
            // btnTestData
            // 
            btnTestData.BackColor = Color.DarkSlateBlue;
            btnTestData.FlatStyle = FlatStyle.Flat;
            btnTestData.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnTestData.ForeColor = Color.White;
            btnTestData.Location = new Point(225, 144);
            btnTestData.Margin = new Padding(4);
            btnTestData.Name = "btnTestData";
            btnTestData.Size = new Size(183, 48);
            btnTestData.TabIndex = 7;
            btnTestData.Text = "生成测试数据";
            btnTestData.UseVisualStyleBackColor = false;
            // 
            // btnShowData
            // 
            btnShowData.BackColor = Color.DarkSlateGray;
            btnShowData.FlatStyle = FlatStyle.Flat;
            btnShowData.Font = new Font("微软雅黑", 10F, FontStyle.Bold);
            btnShowData.ForeColor = Color.White;
            btnShowData.Location = new Point(24, 204);
            btnShowData.Margin = new Padding(4);
            btnShowData.Name = "btnShowData";
            btnShowData.Size = new Size(183, 48);
            btnShowData.TabIndex = 8;
            btnShowData.Text = "显示源数据";
            btnShowData.UseVisualStyleBackColor = false;
            // 
            // AlgorithmFormulaCal
            // 
            AutoScaleDimensions = new SizeF(11F, 24F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.FromArgb(45, 45, 45);
            ClientSize = new Size(1433, 999);
            Controls.Add(rightPanel);
            Controls.Add(leftPanel);
            ForeColor = Color.White;
            Margin = new Padding(4);
            Name = "AlgorithmFormulaCal";
            Text = "脆性指数计算器";
            Load += AlgorithmFormulaCal_Load;
            leftPanel.ResumeLayout(false);
            leftPanel.PerformLayout();
            rightPanel.ResumeLayout(false);
            rightPanel.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dgvResult).EndInit();
            formulaPanel.ResumeLayout(false);
            formulaPanel.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private System.Windows.Forms.Panel leftPanel;
        private System.Windows.Forms.Button btnRemoveDuctile;
        private System.Windows.Forms.Button btnRemoveBrittle;
        private System.Windows.Forms.Button btnAddDuctile;
        private System.Windows.Forms.Button btnAddBrittle;
        private System.Windows.Forms.ListBox lstDuctileColumns;
        private System.Windows.Forms.Label lblDuctile;
        private System.Windows.Forms.ListBox lstBrittleColumns;
        private System.Windows.Forms.Label lblBrittle;
        private System.Windows.Forms.ListBox lstAvailableColumns;
        private System.Windows.Forms.Label lblAvailable;
        private System.Windows.Forms.Panel rightPanel;
        private System.Windows.Forms.DataGridView dgvResult;
        private System.Windows.Forms.Label resultLabel;
        private System.Windows.Forms.Button btnCalculate;
        private System.Windows.Forms.Button btnLoadData;
        private System.Windows.Forms.Button btnTestData;
        private System.Windows.Forms.Button btnShowData;
        private System.Windows.Forms.Button btnVisualize;
        private System.Windows.Forms.Button btnSaveData;
        private System.Windows.Forms.Panel formulaPanel;
        private System.Windows.Forms.Label formulaText;
        private System.Windows.Forms.Label formulaLabel;
        private Label lblBottomDepth;
        private Label lblTopDepth;
        private Button btnTopDepth;
        private Button btnBottomDepth;
        private Button btnMapColumns;
    }
}