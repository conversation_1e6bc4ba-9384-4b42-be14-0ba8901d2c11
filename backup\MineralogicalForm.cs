using System;
using System.Windows.Forms;
using System.Drawing;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;
using System.Windows.Forms.DataVisualization.Charting;
using System.ComponentModel;
using System.Text.RegularExpressions;
using System.Text;
using BorderStyle = System.Windows.Forms.BorderStyle;

namespace BritSystem
{
    public partial class MineralogicalForm : Form
    {
        // 配置类和控件声明
        private class AppConfig
        {
            public int MaxHeaderSearchRows { get; } = 20;
            public string[] RequiredColumns { get; } = { "顶深", "底深", "脆性" };
        }
        private readonly AppConfig config = new AppConfig();

        // 控件声明
        private Label lblTitle;
        private Label lblWelcome;
        private Button btnBack;
        private Button btnLogout;
        private Panel pnlInput;
        private Panel pnlData;
        private Panel pnlChart;
        private DataGridView dgvMineralData;
        private TextBox txtQuartz;
        private TextBox txtFeldspar;
        private TextBox txtCarbonate;
        private TextBox txtClay;
        private Button btnCalculate;
        private Button btnImport;
        private Button btnExport;
        private Button btnGenerateCurve;
        private Button btnDeletePoint;
        private Button btnSaveCurve;
        private Button btnReset;
        private Label lblResult;
        private Chart chartBrittleness;
        private string username;
        private ProgressBar progressBar;
        private BackgroundWorker dataLoader;
        private SplitContainer mainSplitContainer;
        private TabControl tabControl;
        private TabPage autoDetectTab, manualDetectTab;
        private string currentExcelFile;

        private HScrollBar hScrollBar;
        private VScrollBar vScrollBar;
        private double currentZoom = 1.0;
        private double currentXZoom = 1.0;
        private const double MAX_ZOOM = 15.0;  // Y轴最大放大15倍
        private const double MAX_X_ZOOM = 3.0; // X轴最大放大3倍
        private const double MIN_ZOOM = 1.0;   // 最小不缩小
        private const double ZOOM_FACTOR = 1.5;
        private List<double> yAxisLabels = new List<double>();
        private List<DataPoint> dataPoints = new List<DataPoint>();
        private HashSet<int> selectedRows = new HashSet<int>();  // 存储选中的行索引
        private List<int> deletedRows = new List<int>();  // 存储已删除的行索引

        // 数据点类
        private class DataPoint
        {
            public double TopDepth { get; set; }
            public double BottomDepth { get; set; }
            public double BrittleIndex { get; set; }
            public int RowIndex { get; set; }  // 对应的Excel行索引
        }

        // 数据相关
        private DataTable mineralData;

        public MineralogicalForm(string username)
        {
            this.username = username;
            InitializeComponent();
            InitializeAsync();
            // 移除 LoadData() 调用，确保不加载默认数据
        }

        private void InitializeAsync()
        {
            dataLoader = new BackgroundWorker { WorkerReportsProgress = true, WorkerSupportsCancellation = true };
            dataLoader.DoWork += DataLoader_DoWork;
            dataLoader.RunWorkerCompleted += DataLoader_RunWorkerCompleted;

            // 添加窗体关闭事件
            this.FormClosing += MineralogicalForm_FormClosing;
        }

        private void MineralogicalForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // 取消正在运行的后台任务
                if (dataLoader != null && dataLoader.IsBusy)
                {
                    dataLoader.CancelAsync();
                }

                // 释放图表资源
                if (chartBrittleness != null)
                {
                    chartBrittleness.Series.Clear();
                    chartBrittleness.ChartAreas.Clear();
                    chartBrittleness.Legends.Clear();
                    chartBrittleness.Dispose();
                }

                // 强制关闭窗体
                e.Cancel = false;
            }
            catch (Exception ex)
            {
                // 即使出错也强制关闭
                System.Diagnostics.Debug.WriteLine($"关闭窗体时出错: {ex.Message}");
                e.Cancel = false;
            }
        }



        private void InitializeComponent()
        {
            // 窗体设置
            this.Text = "矿物脆性指数分析系统 (专业版)";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(33, 33, 33); // 深色背景

            // 标题
            lblTitle = new Label();
            lblTitle.Text = "脆性指数系统 - 矿物组分法";
            lblTitle.Font = new Font("Microsoft YaHei", 20, FontStyle.Bold);
            lblTitle.ForeColor = Color.Cyan;
            lblTitle.TextAlign = ContentAlignment.MiddleCenter;
            lblTitle.Dock = DockStyle.Top;
            lblTitle.Height = 50;
            lblTitle.Padding = new Padding(0, 10, 0, 0);

            // 欢迎信息
            lblWelcome = new Label();
            lblWelcome.Text = $"欢迎, {username}";
            lblWelcome.Font = new Font("Microsoft YaHei", 12);
            lblWelcome.ForeColor = Color.White;
            lblWelcome.TextAlign = ContentAlignment.MiddleLeft;
            lblWelcome.Location = new Point(20, 60);
            lblWelcome.Size = new Size(300, 30);

            // 返回按钮
            btnBack = new Button();
            btnBack.Text = "返回仪表盘";
            btnBack.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnBack.Location = new Point(20, 100);
            btnBack.Size = new Size(150, 40);
            btnBack.BackColor = Color.FromArgb(0, 120, 212);
            btnBack.ForeColor = Color.White;
            btnBack.FlatStyle = FlatStyle.Flat;
            btnBack.Click += BtnBack_Click;

            // 退出登录按钮
            btnLogout = new Button();
            btnLogout.Text = "退出登录";
            btnLogout.Font = new Font("Microsoft YaHei", 10);
            btnLogout.Location = new Point(this.ClientSize.Width - 150 - 20, 100);
            btnLogout.Size = new Size(150, 40);
            btnLogout.BackColor = Color.FromArgb(153, 153, 153);
            btnLogout.ForeColor = Color.White;
            btnLogout.FlatStyle = FlatStyle.Flat;
            btnLogout.Click += BtnLogout_Click;

            // 输入面板
            pnlInput = new Panel();
            pnlInput.Location = new Point(20, 160);
            pnlInput.Size = new Size(this.ClientSize.Width - 40, 150);
            pnlInput.BackColor = Color.FromArgb(45, 45, 45);
            pnlInput.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;

            // 添加输入控件
            Label lblInputTitle = new Label();
            lblInputTitle.Text = "输入矿物组分 (%)";
            lblInputTitle.Font = new Font("Microsoft YaHei", 12, FontStyle.Bold);
            lblInputTitle.ForeColor = Color.White;
            lblInputTitle.Location = new Point(10, 10);
            lblInputTitle.Size = new Size(200, 25);
            pnlInput.Controls.Add(lblInputTitle);

            // 石英输入
            Label lblQuartz = new Label();
            lblQuartz.Text = "石英:";
            lblQuartz.Font = new Font("Microsoft YaHei", 10);
            lblQuartz.ForeColor = Color.White;
            lblQuartz.Location = new Point(20, 50);
            lblQuartz.Size = new Size(80, 25);
            pnlInput.Controls.Add(lblQuartz);

            txtQuartz = new TextBox();
            txtQuartz.Location = new Point(100, 50);
            txtQuartz.Size = new Size(80, 25);
            txtQuartz.Font = new Font("Microsoft YaHei", 10);
            pnlInput.Controls.Add(txtQuartz);

            // 长石输入
            Label lblFeldspar = new Label();
            lblFeldspar.Text = "长石:";
            lblFeldspar.Font = new Font("Microsoft YaHei", 10);
            lblFeldspar.ForeColor = Color.White;
            lblFeldspar.Location = new Point(200, 50);
            lblFeldspar.Size = new Size(80, 25);
            pnlInput.Controls.Add(lblFeldspar);

            txtFeldspar = new TextBox();
            txtFeldspar.Location = new Point(280, 50);
            txtFeldspar.Size = new Size(80, 25);
            txtFeldspar.Font = new Font("Microsoft YaHei", 10);
            pnlInput.Controls.Add(txtFeldspar);

            // 碳酸盐输入
            Label lblCarbonate = new Label();
            lblCarbonate.Text = "碳酸盐:";
            lblCarbonate.Font = new Font("Microsoft YaHei", 10);
            lblCarbonate.ForeColor = Color.White;
            lblCarbonate.Location = new Point(380, 50);
            lblCarbonate.Size = new Size(80, 25);
            pnlInput.Controls.Add(lblCarbonate);

            txtCarbonate = new TextBox();
            txtCarbonate.Location = new Point(460, 50);
            txtCarbonate.Size = new Size(80, 25);
            txtCarbonate.Font = new Font("Microsoft YaHei", 10);
            pnlInput.Controls.Add(txtCarbonate);

            // 粘土输入
            Label lblClay = new Label();
            lblClay.Text = "粘土:";
            lblClay.Font = new Font("Microsoft YaHei", 10);
            lblClay.ForeColor = Color.White;
            lblClay.Location = new Point(560, 50);
            lblClay.Size = new Size(80, 25);
            pnlInput.Controls.Add(lblClay);

            txtClay = new TextBox();
            txtClay.Location = new Point(640, 50);
            txtClay.Size = new Size(80, 25);
            txtClay.Font = new Font("Microsoft YaHei", 10);
            pnlInput.Controls.Add(txtClay);

            // 计算按钮
            btnCalculate = new Button();
            btnCalculate.Text = "计算脆性指数";
            btnCalculate.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnCalculate.Location = new Point(20, 90);
            btnCalculate.Size = new Size(150, 35);
            btnCalculate.BackColor = Color.Cyan;
            btnCalculate.ForeColor = Color.Black;
            btnCalculate.FlatStyle = FlatStyle.Flat;
            btnCalculate.Click += BtnCalculate_Click;
            pnlInput.Controls.Add(btnCalculate);

            // 结果标签
            lblResult = new Label();
            lblResult.Text = "";
            lblResult.Font = new Font("Microsoft YaHei", 12, FontStyle.Bold);
            lblResult.ForeColor = Color.Cyan;
            lblResult.Location = new Point(200, 90);
            lblResult.Size = new Size(500, 35);
            lblResult.TextAlign = ContentAlignment.MiddleLeft;
            pnlInput.Controls.Add(lblResult);

            // 左侧数据面板
            pnlData = new Panel();
            pnlData.Location = new Point(20, 400); // Y坐标从330改为400
            pnlData.Size = new Size(this.ClientSize.Width / 2 - 30, this.ClientSize.Height - 420);
            pnlData.BackColor = Color.FromArgb(45, 45, 45);
            pnlData.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;

            // 添加数据标题
            Label lblDataTitle = new Label();
            lblDataTitle.Text = "矿物组分数据";
            lblDataTitle.Font = new Font("Microsoft YaHei", 12, FontStyle.Bold);
            lblDataTitle.ForeColor = Color.White;
            lblDataTitle.Location = new Point(10, 5); // Y从10改为5
            lblDataTitle.Size = new Size(200, 25);
            pnlData.Controls.Add(lblDataTitle);

            // 添加导入/导出按钮
            btnImport = new Button();
            btnImport.Text = "导入数据";
            btnImport.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnImport.Location = new Point(pnlData.Width - 250, 5); // Y从10改为5
            btnImport.Size = new Size(110, 30);
            btnImport.BackColor = Color.FromArgb(0, 120, 212);
            btnImport.ForeColor = Color.White;
            btnImport.FlatStyle = FlatStyle.Flat;
            btnImport.Click += BtnImport_Click;
            pnlData.Controls.Add(btnImport);

            btnExport = new Button();
            btnExport.Text = "导出数据";
            btnExport.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnExport.Location = new Point(pnlData.Width - 130, 5); // Y从10改为5
            btnExport.Size = new Size(110, 30);
            btnExport.BackColor = Color.FromArgb(0, 120, 212);
            btnExport.ForeColor = Color.White;
            btnExport.FlatStyle = FlatStyle.Flat;
            btnExport.Click += BtnExport_Click;
            pnlData.Controls.Add(btnExport);

            // 添加数据网格
            dgvMineralData = new DataGridView();
            dgvMineralData.Location = new Point(10, 35); // Y从40改为35
            dgvMineralData.Size = new Size(pnlData.Width - 20, pnlData.Height - 45); // 高度从-50改为-45
            dgvMineralData.BackgroundColor = Color.FromArgb(45, 45, 45);
            dgvMineralData.ForeColor = Color.Black;
            dgvMineralData.GridColor = Color.FromArgb(60, 60, 60);
            dgvMineralData.BorderStyle = System.Windows.Forms.BorderStyle.None;
            dgvMineralData.RowHeadersVisible = false;
            dgvMineralData.AllowUserToAddRows = false;
            dgvMineralData.AllowUserToDeleteRows = false;
            dgvMineralData.ReadOnly = true;
            dgvMineralData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            dgvMineralData.AutoGenerateColumns = true; // 确保自动生成列
            dgvMineralData.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(60, 60, 60);
            dgvMineralData.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvMineralData.ColumnHeadersDefaultCellStyle.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            dgvMineralData.DefaultCellStyle.BackColor = Color.FromArgb(50, 50, 50);
            dgvMineralData.DefaultCellStyle.ForeColor = Color.White;
            dgvMineralData.DefaultCellStyle.Font = new Font("Microsoft YaHei", 9);
            dgvMineralData.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(55, 55, 55);
            dgvMineralData.AlternatingRowsDefaultCellStyle.ForeColor = Color.White;
            dgvMineralData.AlternatingRowsDefaultCellStyle.Font = new Font("Microsoft YaHei", 9);
            dgvMineralData.EnableHeadersVisualStyles = false;
            dgvMineralData.SelectionChanged += DataGridView_SelectionChanged;
            dgvMineralData.MouseClick += DataGridView_MouseClick;
            dgvMineralData.CellClick += DataGridView_CellClick;
            dgvMineralData.CellDoubleClick += DataGridView_CellDoubleClick;
            dgvMineralData.CellFormatting += DataGridView_CellFormatting;
            pnlData.Controls.Add(dgvMineralData);

            // 右侧图表面板
            pnlChart = new Panel();
            pnlChart.Location = new Point(this.ClientSize.Width / 2 + 10, 400); // Y坐标从330改为400
            pnlChart.Size = new Size(this.ClientSize.Width / 2 - 30, this.ClientSize.Height - 420);
            pnlChart.BackColor = Color.FromArgb(45, 45, 45);
            pnlChart.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;

            // 添加图表标题
            Label lblChartTitle = new Label();
            lblChartTitle.Text = "脆性指数曲线";
            lblChartTitle.Font = new Font("Microsoft YaHei", 12, FontStyle.Bold);
            lblChartTitle.ForeColor = Color.White;
            lblChartTitle.Location = new Point(10, 5); // Y从10改为5
            lblChartTitle.Size = new Size(150, 25);
            lblChartTitle.TextAlign = ContentAlignment.MiddleLeft;
            pnlChart.Controls.Add(lblChartTitle);

            // 添加生成曲线按钮
            btnGenerateCurve = new Button();
            btnGenerateCurve.Text = "生成曲线";
            btnGenerateCurve.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnGenerateCurve.Location = new Point(pnlChart.Width - 130, 5); // Y从10改为5
            btnGenerateCurve.Size = new Size(110, 30);
            btnGenerateCurve.BackColor = Color.FromArgb(0, 120, 212);
            btnGenerateCurve.ForeColor = Color.White;
            btnGenerateCurve.FlatStyle = FlatStyle.Flat;
            btnGenerateCurve.Click += BtnGenerateCurve_Click;
            pnlChart.Controls.Add(btnGenerateCurve);

            // 添加图表控件
            chartBrittleness = new Chart();
            chartBrittleness.Location = new Point(10, 35); // Y从40改为35
            chartBrittleness.Size = new Size(pnlChart.Width - 20, pnlChart.Height - 45); // 高度从-50改为-45
            chartBrittleness.BackColor = Color.FromArgb(45, 45, 45);
            chartBrittleness.ForeColor = Color.White;
            chartBrittleness.BorderlineColor = Color.FromArgb(60, 60, 60);
            chartBrittleness.BorderlineDashStyle = ChartDashStyle.Solid;
            chartBrittleness.BorderlineWidth = 1;
            chartBrittleness.MouseMove += MineralChart_MouseMove;
            chartBrittleness.MouseClick += MineralChart_MouseClick;
            chartBrittleness.MouseWheel += MineralChart_MouseWheel;
            chartBrittleness.MouseDown += MineralChart_MouseDown;
            chartBrittleness.MouseUp += MineralChart_MouseUp;

            // 配置图表区域
            ChartArea chartArea = new ChartArea("BrittlenessArea");
            chartArea.BackColor = Color.FromArgb(45, 45, 45);
            chartArea.AxisX.LabelStyle.ForeColor = Color.White;
            chartArea.AxisY.LabelStyle.ForeColor = Color.White;
            chartArea.AxisX.LineColor = Color.White;
            chartArea.AxisY.LineColor = Color.White;
            chartArea.AxisX.MajorGrid.LineColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisY.MajorGrid.LineColor = Color.FromArgb(60, 60, 60);
            chartArea.AxisX.Title = "脆性指数 (%)";
            chartArea.AxisY.Title = "深度 (m)";
            chartArea.AxisX.TitleForeColor = Color.Cyan;
            chartArea.AxisY.TitleForeColor = Color.Cyan;
            chartArea.AxisX.Minimum = 0;
            chartArea.AxisX.Maximum = 100;
            chartArea.AxisY.IsReversed = true; // 深度增加时向下显示
            chartBrittleness.ChartAreas.Add(chartArea);

            // 添加图例
            Legend legend = new Legend("BrittnessLegend");
            legend.BackColor = Color.FromArgb(45, 45, 45);
            legend.ForeColor = Color.White;
            legend.BorderColor = Color.FromArgb(60, 60, 60);
            chartBrittleness.Legends.Add(legend);

            // 添加数据系列
            Series series = new Series("脆性指数");
            series.ChartType = SeriesChartType.Point;
            series.MarkerStyle = MarkerStyle.Circle;
            series.MarkerSize = 8;
            series.Color = Color.Cyan;
            series.IsVisibleInLegend = true;
            series.Legend = "BrittnessLegend";
            series.ChartArea = "BrittlenessArea";
            chartBrittleness.Series.Add(series);

            pnlChart.Controls.Add(chartBrittleness);

            // 添加删除点和重置按钮
            btnDeletePoint = new Button();
            btnDeletePoint.Text = "删除点";
            btnDeletePoint.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnDeletePoint.Location = new Point(pnlChart.Width - 250, 5); // Y从10改为5
            btnDeletePoint.Size = new Size(110, 30);
            btnDeletePoint.BackColor = Color.FromArgb(0, 120, 212);
            btnDeletePoint.ForeColor = Color.White;
            btnDeletePoint.FlatStyle = FlatStyle.Flat;
            btnDeletePoint.Click += BtnDeletePoint_Click;
            pnlChart.Controls.Add(btnDeletePoint);

            btnReset = new Button();
            btnReset.Text = "重置视图";
            btnReset.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnReset.Location = new Point(pnlChart.Width - 370, 5); // Y从10改为5
            btnReset.Size = new Size(110, 30);
            btnReset.BackColor = Color.FromArgb(0, 120, 212);
            btnReset.ForeColor = Color.White;
            btnReset.FlatStyle = FlatStyle.Flat;
            btnReset.Click += BtnReset_Click;
            pnlChart.Controls.Add(btnReset);

            // 添加保存图像按钮
            btnSaveCurve = new Button();
            btnSaveCurve.Text = "保存图像";
            btnSaveCurve.Font = new Font("Microsoft YaHei", 10, FontStyle.Bold);
            btnSaveCurve.Location = new Point(pnlChart.Width - 490, 5); // Y从10改为5
            btnSaveCurve.Size = new Size(110, 30);
            btnSaveCurve.BackColor = Color.FromArgb(0, 120, 212);
            btnSaveCurve.ForeColor = Color.White;
            btnSaveCurve.FlatStyle = FlatStyle.Flat;
            btnSaveCurve.Click += BtnSaveCurve_Click;
            pnlChart.Controls.Add(btnSaveCurve);

            // 创建进度条
            progressBar = new ProgressBar();
            progressBar.Location = new Point(10, pnlData.Height - 30);
            progressBar.Size = new Size(pnlData.Width - 20, 20);
            progressBar.Visible = false;
            pnlData.Controls.Add(progressBar);

            // 添加紧急退出按钮
            Button btnEmergencyExit = new Button();
            btnEmergencyExit.Text = "紧急退出";
            btnEmergencyExit.Font = new Font("Microsoft YaHei", 9, FontStyle.Bold);
            btnEmergencyExit.Location = new Point(this.ClientSize.Width - 100, 10);
            btnEmergencyExit.Size = new Size(90, 30);
            btnEmergencyExit.BackColor = Color.FromArgb(192, 0, 0);
            btnEmergencyExit.ForeColor = Color.White;
            btnEmergencyExit.FlatStyle = FlatStyle.Flat;
            btnEmergencyExit.Click += (s, e) => { Application.Exit(); };

            // 添加控件到窗体
            this.Controls.Add(lblTitle);
            this.Controls.Add(lblWelcome);
            this.Controls.Add(btnBack);
            this.Controls.Add(btnLogout);
            this.Controls.Add(btnEmergencyExit);
            this.Controls.Add(pnlInput);
            this.Controls.Add(pnlData);
            this.Controls.Add(pnlChart);

            // 窗体大小调整事件
            this.Resize += MineralogicalForm_Resize;

            // 添加右键菜单
            var contextMenu = new ContextMenuStrip();
            var saveMenuItem = new ToolStripMenuItem("保存当前视图");
            saveMenuItem.Click += SaveCurrentView_Click;
            contextMenu.Items.Add(saveMenuItem);
            chartBrittleness.ContextMenuStrip = contextMenu;
        }

        private void MineralogicalForm_Resize(object sender, EventArgs e)
        {
            // 调整控件位置和大小
            btnLogout.Location = new Point(this.ClientSize.Width - 150 - 20, 100);
            pnlInput.Size = new Size(this.ClientSize.Width - 40, 150);

            // 调整数据面板位置和大小
            pnlData.Location = new Point(20, 400);
            pnlData.Size = new Size(this.ClientSize.Width / 2 - 30, this.ClientSize.Height - 420);
            dgvMineralData.Size = new Size(pnlData.Width - 20, pnlData.Height - 45); // 高度从-50改为-45

            // 调整图表面板位置和大小
            pnlChart.Location = new Point(this.ClientSize.Width / 2 + 10, 400);
            pnlChart.Size = new Size(this.ClientSize.Width / 2 - 30, this.ClientSize.Height - 420);
            chartBrittleness.Size = new Size(pnlChart.Width - 20, pnlChart.Height - 45); // 高度从-50改为-45

            // 调整按钮位置（确保在面板右侧）
            btnImport.Location = new Point(pnlData.Width - 250, 5);
            btnExport.Location = new Point(pnlData.Width - 130, 5);
            btnGenerateCurve.Location = new Point(pnlChart.Width - 130, 5);
            btnDeletePoint.Location = new Point(pnlChart.Width - 250, 5);
            btnReset.Location = new Point(pnlChart.Width - 370, 5);
            btnSaveCurve.Location = new Point(pnlChart.Width - 490, 5);
        }

        private void LoadData()
        {
            // 初始化空数据表
            mineralData = new DataTable();
            mineralData.Columns.Add("顶深/m", typeof(double));
            mineralData.Columns.Add("底深/m", typeof(double));
            mineralData.Columns.Add("脆性指数", typeof(double));

            // 绑定数据源，DataGridView 自动生成列
            dgvMineralData.DataSource = mineralData;

            // 输出调试信息
            System.Diagnostics.Debug.WriteLine($"LoadData: 初始化数据表完成");
            System.Diagnostics.Debug.WriteLine($"  列名: {string.Join(", ", mineralData.Columns.Cast<DataColumn>().Select(c => c.ColumnName))}");
            System.Diagnostics.Debug.WriteLine($"  DataGridView 列数: {dgvMineralData.Columns.Count}");
            foreach (DataGridViewColumn col in dgvMineralData.Columns)
            {
                System.Diagnostics.Debug.WriteLine($"    列: {col.Name}, 标题: {col.HeaderText}");
            }
        }

        private void BtnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取输入值
                if (!double.TryParse(txtQuartz.Text, out double quartz) ||
                    !double.TryParse(txtFeldspar.Text, out double feldspar) ||
                    !double.TryParse(txtCarbonate.Text, out double carbonate) ||
                    !double.TryParse(txtClay.Text, out double clay))
                {
                    MessageBox.Show("请输入有效的数值", "输入错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 计算脆性指数
                double brittleness = (quartz + feldspar + carbonate) / (quartz + feldspar + carbonate + clay) * 100;
                brittleness = Math.Round(brittleness, 2);

                // 显示结果
                lblResult.Text = $"脆性指数: {brittleness}%";

                // 添加到数据表中
                DataRow newRow = mineralData.NewRow();
                newRow["顶深"] = 0.0; // 默认值
                newRow["底深"] = 0.0; // 默认值
                newRow["石英"] = quartz;
                newRow["长石"] = feldspar;
                newRow["碳酸盐"] = carbonate;
                newRow["粘土"] = clay;
                newRow["脆性指数"] = brittleness;
                mineralData.Rows.Add(newRow);

                // 添加到数据网格中
                dgvMineralData.Rows.Add(0.0, 0.0, quartz, feldspar, carbonate, clay, brittleness);

                // 不自动生成曲线，只显示计算结果
                MessageBox.Show($"计算成功! 脆性指数为: {brittleness}%", "计算结果", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算脆性指数失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnBack_Click(object sender, EventArgs e)
        {
            // 返回仪表盘
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void BtnLogout_Click(object sender, EventArgs e)
        {
            // 退出登录
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }



        private void HScrollBar_ValueChanged(object sender, EventArgs e)
        {
            var chartArea = chartBrittleness.ChartAreas[0];
            double range = chartArea.AxisX.Maximum - chartArea.AxisX.Minimum;
            double viewRange = chartArea.AxisX.ScaleView.ViewMaximum - chartArea.AxisX.ScaleView.ViewMinimum;
            double offset = (double)hScrollBar.Value / hScrollBar.Maximum * (range - viewRange);
            chartArea.AxisX.ScaleView.Zoom(offset, offset + viewRange);
        }

        private void VScrollBar_ValueChanged(object sender, EventArgs e)
        {
            var chartArea = chartBrittleness.ChartAreas[0];
            double range = chartArea.AxisY.Maximum - chartArea.AxisY.Minimum;
            double viewRange = chartArea.AxisY.ScaleView.ViewMaximum - chartArea.AxisY.ScaleView.ViewMinimum;
            double offset = (double)vScrollBar.Value / vScrollBar.Maximum * (range - viewRange);
            chartArea.AxisY.ScaleView.Zoom(offset, offset + viewRange);
        }

        private void MineralChart_MouseWheel(object sender, MouseEventArgs e)
        {
            if (e.Delta == 0) return;

            var chartArea = chartBrittleness.ChartAreas[0];

            // 检查是否按下Ctrl键 - 如果是，则缩放X轴，否则缩放Y轴
            if ((ModifierKeys & Keys.Control) == Keys.Control)
            {
                // X轴缩放
                double newXZoom = currentXZoom;

                if (e.Delta > 0)
                {
                    newXZoom *= ZOOM_FACTOR;
                    if (newXZoom > MAX_X_ZOOM) newXZoom = MAX_X_ZOOM;
                }
                else
                {
                    newXZoom /= ZOOM_FACTOR;
                    if (newXZoom < MIN_ZOOM) newXZoom = MIN_ZOOM;
                }

                if (newXZoom == currentXZoom) return;

                // 获取鼠标位置对应的X轴值
                var pos = e.Location;
                double xValue = chartArea.AxisX.PixelPositionToValue(pos.X);

                // 计算新的显示范围
                double xRange = (chartArea.AxisX.Maximum - chartArea.AxisX.Minimum) / newXZoom;
                double xMin = xValue - (xRange / 2);
                double xMax = xValue + (xRange / 2);

                // 确保不超出数据范围
                if (xMin < chartArea.AxisX.Minimum)
                {
                    xMin = chartArea.AxisX.Minimum;
                    xMax = xMin + xRange;
                }
                if (xMax > chartArea.AxisX.Maximum)
                {
                    xMax = chartArea.AxisX.Maximum;
                    xMin = xMax - xRange;
                }

                chartArea.AxisX.ScaleView.Zoom(xMin, xMax);
                currentXZoom = newXZoom;

                // 如果缩放到最小值，则重置视图
                if (newXZoom == MIN_ZOOM)
                {
                    chartArea.AxisX.ScaleView.ZoomReset();
                }
            }
            else
            {
                // Y轴缩放
                double newZoom = currentZoom;

                if (e.Delta > 0)
                {
                    newZoom *= ZOOM_FACTOR;
                    if (newZoom > MAX_ZOOM) newZoom = MAX_ZOOM;
                }
                else
                {
                    newZoom /= ZOOM_FACTOR;
                    if (newZoom < MIN_ZOOM) newZoom = MIN_ZOOM;
                }

                if (newZoom == currentZoom) return;

                // 获取鼠标位置对应的Y轴值
                var pos = e.Location;
                double yValue = chartArea.AxisY.PixelPositionToValue(pos.Y);

                // 计算新的显示范围
                double yRange = (chartArea.AxisY.Maximum - chartArea.AxisY.Minimum) / newZoom;
                double yMin = yValue - (yRange / 2);
                double yMax = yValue + (yRange / 2);

                // 确保不超出数据范围
                if (yMin < chartArea.AxisY.Minimum)
                {
                    yMin = chartArea.AxisY.Minimum;
                    yMax = yMin + yRange;
                }
                if (yMax > chartArea.AxisY.Maximum)
                {
                    yMax = chartArea.AxisY.Maximum;
                    yMin = yMax - yRange;
                }

                chartArea.AxisY.ScaleView.Zoom(yMin, yMax);
                currentZoom = newZoom;

                // 如果缩放到最小值，则重置视图
                if (newZoom == MIN_ZOOM)
                {
                    chartArea.AxisY.ScaleView.ZoomReset();
                }

                UpdateYAxisLabels();
            }

            chartBrittleness.Invalidate();
        }

        private void MineralChart_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Middle)
            {
                ResetChartView();
            }
        }

        private void MineralChart_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Middle)
            {
                ResetChartView();
            }
        }

        private void MineralChart_MouseMove(object sender, MouseEventArgs e)
        {
            var results = chartBrittleness.HitTest(e.X, e.Y, false, ChartElementType.DataPoint);
            foreach (var result in results)
            {
                if (result.ChartElementType == ChartElementType.DataPoint && result.Series.Name == "矿物点")
                {
                    // 重置所有点的样式（保持选中点的高亮）
                    for (int i = 0; i < result.Series.Points.Count; i++)
                    {
                        if (!selectedRows.Contains(i))
                        {
                            result.Series.Points[i].Color = Color.Cyan;
                            result.Series.Points[i].MarkerSize = 8;
                        }
                    }

                    // 高亮悬浮的点（如果不是已选中的点）
                    if (!selectedRows.Contains(result.PointIndex))
                    {
                        result.Series.Points[result.PointIndex].Color = Color.FromArgb(0, 200, 255);
                        result.Series.Points[result.PointIndex].MarkerSize = 10;
                    }
                    break;
                }
            }
        }

        private void MineralChart_MouseClick(object sender, MouseEventArgs e)
        {
            var results = chartBrittleness.HitTest(e.X, e.Y, false, ChartElementType.DataPoint);
            foreach (var result in results)
            {
                if (result.ChartElementType == ChartElementType.DataPoint && result.Series.Name == "矿物点")
                {
                    if (result.PointIndex < dataPoints.Count)
                    {
                        var point = dataPoints[result.PointIndex];
                        MessageBox.Show($"顶深: {point.TopDepth}m\n底深: {point.BottomDepth}m\n脆性指数: {point.BrittleIndex}%", "点信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    break;
                }
            }
        }


        private void ResetChartView()
        {
            if (chartBrittleness.Series.Count > 0 && dgvMineralData.DataSource != null)
            {
                selectedRows.Clear();  // 重置选中行
                dataPoints.Clear();  // 清空数据点列表
                chartBrittleness.Series.Clear();
                chartBrittleness.ChartAreas[0].RecalculateAxesScale();

                // 重新生成曲线
                var series = new Series("脆性指数")
                {
                    ChartType = SeriesChartType.FastLine,
                    Color = Color.Cyan,
                    BorderWidth = 2
                };
                var pointSeries = new Series("矿物点")
                {
                    ChartType = SeriesChartType.Point,
                    Color = Color.Cyan,
                    MarkerSize = 8
                };

                var data = (DataTable)dgvMineralData.DataSource;
                List<double> topDepths = new List<double>();
                foreach (DataRow row in data.Rows)
                {
                    double topDepth = 0;

                    try
                    {
                        // 尝试直接获取列值
                        topDepth = ParseCell(row["顶深/m"]);
                    }
                    catch
                    {
                        // 如果失败，尝试使用索引获取值
                        try
                        {
                            if (row.ItemArray.Length >= 1)
                            {
                                topDepth = ParseCell(row.ItemArray[0]);
                            }
                        }
                        catch
                        {
                            // 如果仍然失败，跳过这一行
                            continue;
                        }
                    }

                    if (!double.IsNaN(topDepth) && !double.IsInfinity(topDepth))
                    {
                        topDepths.Add(topDepth);
                    }
                }

                if (topDepths.Count > 0)
                {
                    double minTopDepth = topDepths.Min();
                    double maxTopDepth = topDepths.Max();

                    var chartArea = chartBrittleness.ChartAreas[0];
                    chartArea.AxisY.CustomLabels.Clear();

                    // 更新Y轴刻度数组
                    yAxisLabels.Clear();
                    yAxisLabels.AddRange(topDepths.OrderBy(d => d));

                    // 设置Y轴范围
                    chartArea.AxisY.Minimum = minTopDepth;
                    chartArea.AxisY.Maximum = maxTopDepth;

                    // 计算初始缩放比例
                    double dataHeight = maxTopDepth - minTopDepth;
                    double chartHeight = chartBrittleness.Height;
                    double initialZoom = dataHeight / chartHeight;

                    if (initialZoom > 1)
                    {
                        currentZoom = 1.0 / initialZoom;  // 自动设置合适的缩放比例
                        chartArea.AxisY.ScaleView.Zoom(minTopDepth, maxTopDepth);
                    }
                    else
                    {
                        currentZoom = 1.0;
                        chartArea.AxisY.ScaleView.ZoomReset();
                    }

                    // 设置Y轴属性
                    chartArea.AxisY.LabelStyle.Format = "0.00";
                    chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
                    chartArea.AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dash;

                    // 显示初始Y轴刻度
                    UpdateYAxisLabels();

                    foreach (DataRow row in data.Rows)
                    {
                        double depth = 0;
                        double value = 0;

                        try
                        {
                            // 尝试直接获取列值
                            depth = ParseCell(row["顶深/m"]);
                            value = ParseCell(row["脆性指数"]);
                        }
                        catch
                        {
                            // 如果失败，尝试使用索引获取值
                            try
                            {
                                if (row.ItemArray.Length >= 3)
                                {
                                    depth = ParseCell(row.ItemArray[0]);
                                    value = ParseCell(row.ItemArray[2]);
                                }
                            }
                            catch
                            {
                                // 如果仍然失败，跳过这一行
                                continue;
                            }
                        }

                        if (!double.IsNaN(depth) && !double.IsInfinity(depth) && !double.IsNaN(value) && !double.IsInfinity(value))
                        {
                            series.Points.AddXY(value, depth);
                            pointSeries.Points.AddXY(value, depth);
                        }
                    }

                    chartBrittleness.Series.Add(series);
                    chartBrittleness.Series.Add(pointSeries);

                    // 强制刷新图表
                    chartBrittleness.Invalidate();
                }
            }
        }

        private void SaveCurrentView_Click(object sender, EventArgs e)
        {
            using (var sfd = new SaveFileDialog { Filter = "PNG图像|*.png|JPEG图像|*.jpg" })
            {
                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        var format = sfd.FileName.EndsWith(".png") ?
                            ChartImageFormat.Png : ChartImageFormat.Jpeg;

                        // 保存当前的视图
                        chartBrittleness.SaveImage(sfd.FileName, format);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"保存图像失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void BtnSaveCurve_Click(object sender, EventArgs e)
        {
            using (var sfd = new SaveFileDialog { Filter = "PNG图像|*.png|JPEG图像|*.jpg" })
            {
                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        var format = sfd.FileName.EndsWith(".png") ?
                            ChartImageFormat.Png : ChartImageFormat.Jpeg;

                        // 保存原始视图
                        var chartArea = chartBrittleness.ChartAreas[0];

                        // 保存当前视图状态
                        double currentMin = chartArea.AxisY.ScaleView.ViewMinimum;
                        double currentMax = chartArea.AxisY.ScaleView.ViewMaximum;
                        double currentZoomLevel = currentZoom;

                        // 重置视图
                        chartArea.AxisY.ScaleView.ZoomReset();
                        currentZoom = 1.0;

                        // 确保显示完整的Y轴刻度
                        UpdateYAxisLabels();

                        // 强制刷新图表
                        chartBrittleness.Invalidate();
                        Application.DoEvents();

                        // 保存图像
                        chartBrittleness.SaveImage(sfd.FileName, format);

                        // 恢复之前的视图
                        if (!double.IsNaN(currentMin) && !double.IsNaN(currentMax))
                        {
                            chartArea.AxisY.ScaleView.Zoom(currentMin, currentMax);
                            currentZoom = currentZoomLevel;
                            UpdateYAxisLabels();
                        }

                        MessageBox.Show($"图像已保存到 {sfd.FileName}", "保存成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"保存图像失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private double ParseCell(object cellValue)
        {
            if (cellValue == null || cellValue == DBNull.Value || string.IsNullOrWhiteSpace(cellValue.ToString()))
            {
                return double.NaN;
            }

            string strValue = cellValue.ToString().Trim();

            // 尝试直接解析为数字
            if (double.TryParse(strValue, out double result))
            {
                if (double.IsInfinity(result) || double.IsNaN(result))
                {
                    return double.NaN;
                }
                return result;
            }

            return double.NaN;
        }

        private void UpdateYAxisLabels()
        {
            var chartArea = chartBrittleness.ChartAreas[0];
            chartArea.AxisY.CustomLabels.Clear();
            chartArea.AxisY.LabelStyle.Format = "0.00";
            chartArea.AxisY.IntervalAutoMode = IntervalAutoMode.VariableCount;

            if (yAxisLabels.Count > 0)
            {
                // 根据缩放级别调整显示的标签数量
                int labelStep = currentZoom > 3.0 ? 4 : 2;

                for (int i = 0; i < yAxisLabels.Count; i += labelStep)
                {
                    double labelValue = yAxisLabels[i];
                    if (labelValue >= chartArea.AxisY.ScaleView.ViewMinimum &&
                        labelValue <= chartArea.AxisY.ScaleView.ViewMaximum)
                    {
                        var label = chartArea.AxisY.CustomLabels.Add(
                            labelValue - 0.0001,
                            labelValue + 0.0001,
                            $"{labelValue:F2}m");
                        label.RowIndex = 0;
                    }
                }
            }
        }

        private void BtnGenerateCurve_Click(object sender, EventArgs e)
        {
            try
            {
                // 清除现有数据
                chartBrittleness.Series.Clear();
                dataPoints.Clear();  // 清空数据点列表

                // 创建曲线系列
                var series = new Series("脆性指数")
                {
                    ChartType = SeriesChartType.FastLine,
                    Color = Color.Cyan,
                    BorderWidth = 2
                };

                // 创建点系列
                var pointSeries = new Series("矿物点")
                {
                    ChartType = SeriesChartType.Point,
                    Color = Color.Red,
                    MarkerSize = 8,
                    MarkerStyle = MarkerStyle.Circle
                };

                // 直接从 DataGridView 的 DataSource 获取数据
                var data = (DataTable)dgvMineralData.DataSource;
                if (data == null || data.Rows.Count == 0)
                {
                    MessageBox.Show("没有数据可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                List<double> topDepths = new List<double>();

                // 收集数据点
                for (int i = 0; i < data.Rows.Count; i++)
                {
                    var row = data.Rows[i];
                    // 打印行数据以进行调试
                    System.Diagnostics.Debug.WriteLine($"处理行 {i + 1}:");
                    System.Diagnostics.Debug.WriteLine($"  行数据: {string.Join(", ", row.ItemArray)}");
                    System.Diagnostics.Debug.WriteLine($"  列名: {string.Join(", ", data.Columns.Cast<DataColumn>().Select(c => c.ColumnName))}");

                    double topDepth = ParseCell(row["顶深/m"]);
                    double bottomDepth = ParseCell(row["底深/m"]);
                    double brittleIndex = ParseCell(row["脆性指数"]);

                    System.Diagnostics.Debug.WriteLine($"  解析结果: 顶深={topDepth}, 底深={bottomDepth}, 脆性指数={brittleIndex}");

                    if (!double.IsNaN(topDepth) && !double.IsInfinity(topDepth) &&
                        !double.IsNaN(bottomDepth) && !double.IsInfinity(bottomDepth) &&
                        !double.IsNaN(brittleIndex) && !double.IsInfinity(brittleIndex))
                    {
                        dataPoints.Add(new DataPoint
                        {
                            TopDepth = topDepth,
                            BottomDepth = bottomDepth,
                            BrittleIndex = brittleIndex,
                            RowIndex = i
                        });
                        topDepths.Add(topDepth);
                    }
                }

                // 检查是否有足够的数据点
                if (topDepths.Count <= 0)
                {
                    MessageBox.Show("没有有效的数据点可以生成曲线！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 确保有足够的数据范围
                double minTopDepth = topDepths.Min();
                double maxTopDepth = topDepths.Max();

                // 如果最小值和最大值相同，添加一个小的偏移量以避免坐标轴错误
                if (Math.Abs(maxTopDepth - minTopDepth) < 0.001)
                {
                    maxTopDepth += 1.0;
                }

                var chartArea = chartBrittleness.ChartAreas[0];
                chartArea.AxisY.CustomLabels.Clear();

                // 更新Y轴刻度数组
                yAxisLabels.Clear();
                yAxisLabels.AddRange(topDepths.OrderBy(d => d));

                // 设置Y轴范围
                chartArea.AxisY.Minimum = minTopDepth;
                chartArea.AxisY.Maximum = maxTopDepth;

                // 设置X轴范围（脆性指数范围）
                // 计算实际的脆性指数范围
                double minBrittle = dataPoints.Count > 0 ? dataPoints.Min(p => p.BrittleIndex) : 0;
                double maxBrittle = dataPoints.Count > 0 ? dataPoints.Max(p => p.BrittleIndex) : 100;

                // 添加一些边距
                minBrittle = Math.Max(0, minBrittle - 5);
                maxBrittle = maxBrittle + 5;

                chartArea.AxisX.Minimum = minBrittle;
                chartArea.AxisX.Maximum = maxBrittle;

                // 计算初始缩放比例
                double dataHeight = maxTopDepth - minTopDepth;
                double chartHeight = chartBrittleness.Height;
                double initialZoom = dataHeight / chartHeight;

                if (initialZoom > 1)
                {
                    currentZoom = 1.0 / initialZoom;  // 自动设置合适的缩放比例
                    chartArea.AxisY.ScaleView.Zoom(minTopDepth, maxTopDepth);
                }
                else
                {
                    currentZoom = 1.0;
                }

                // 设置Y轴属性
                chartArea.AxisY.LabelStyle.Format = "0.00";
                chartArea.AxisY.MajorGrid.LineColor = Color.LightGray;
                chartArea.AxisY.MajorGrid.LineDashStyle = ChartDashStyle.Dash;

                // 显示初始Y轴刻度
                UpdateYAxisLabels();

                // 添加数据点
                foreach (var point in dataPoints)
                {
                    series.Points.AddXY(point.BrittleIndex, point.TopDepth);
                    pointSeries.Points.AddXY(point.BrittleIndex, point.TopDepth);
                }

                // 添加系列到图表
                chartBrittleness.Series.Add(series);
                chartBrittleness.Series.Add(pointSeries);
                selectedRows.Clear();  // 重置选中行

                // 强制刷新图表
                chartBrittleness.Invalidate();

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"成功生成曲线，数据点数量: {dataPoints.Count}");
                System.Diagnostics.Debug.WriteLine($"X轴范围: {minBrittle} - {maxBrittle}");
                System.Diagnostics.Debug.WriteLine($"Y轴范围: {minTopDepth} - {maxTopDepth}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成曲线时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                System.Diagnostics.Debug.WriteLine($"生成曲线异常: {ex.Message}\n{ex.StackTrace}");
            }
        }

        private void BtnImport_Click(object sender, EventArgs e)
        {
            using (var ofd = new OpenFileDialog { Filter = "Excel文件|*.xls;*.xlsx" })
            {
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        // 先初始化数据表，确保数据表结构正确
                        LoadData();

                        currentExcelFile = ofd.FileName;
                        var ds = ReadExcelSheets(currentExcelFile);
                        if (ds != null && ds.Tables.Count > 0)
                        {
                            mineralData = ds.Tables[0];
                            MessageBox.Show("文件加载成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            // 自动识别列头
                            AutoDetectColumns();
                        }
                        else
                        {
                            MessageBox.Show("文件内容为空！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"文件读取失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        // 不再需要ImportExcelFile方法，因为我们现在使用ReadExcelSheets方法

        private double GetCellValueAsDouble(ICell cell)
        {
            if (cell == null) return 0;

            switch (cell.CellType)
            {
                case CellType.Numeric:
                    return cell.NumericCellValue;
                case CellType.String:
                    if (double.TryParse(cell.StringCellValue, out double result))
                        return result;
                    return 0;
                case CellType.Formula:
                    try
                    {
                        return cell.NumericCellValue;
                    }
                    catch
                    {
                        return 0;
                    }
                default:
                    return 0;
            }
        }

        private void BtnExport_Click(object sender, EventArgs e)
        {
            try
            {
                // 创建保存文件对话框
                SaveFileDialog saveFileDialog = new SaveFileDialog();
                saveFileDialog.Filter = "Excel文件 (*.xlsx)|*.xlsx";
                saveFileDialog.Title = "保存Excel文件";
                saveFileDialog.FileName = $"脆性指数数据_{DateTime.Now:yyyyMMdd}.xlsx";

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 导出Excel文件
                    ExportExcelFile(saveFileDialog.FileName);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出Excel文件时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportExcelFile(string filePath)
        {
            try
            {
                // 创建Excel工作簿
                IWorkbook workbook = new XSSFWorkbook();
                ISheet sheet = workbook.CreateSheet("脆性指数数据");

                // 创建标题行样式
                ICellStyle headerStyle = workbook.CreateCellStyle();
                IFont headerFont = workbook.CreateFont();
                headerFont.IsBold = true;
                headerStyle.SetFont(headerFont);

                // 创建标题行
                IRow headerRow = sheet.CreateRow(0);
                string[] headers = { "顶深/m", "底深/m", "脆性指数" };

                for (int i = 0; i < headers.Length; i++)
                {
                    ICell cell = headerRow.CreateCell(i);
                    cell.SetCellValue(headers[i]);
                    cell.CellStyle = headerStyle;
                    sheet.AutoSizeColumn(i);
                }

                // 添加数据行
                for (int i = 0; i < mineralData.Rows.Count; i++)
                {
                    IRow row = sheet.CreateRow(i + 1);
                    DataRow dataRow = mineralData.Rows[i];

                    // 尝试导出所有列的数据
                    for (int j = 0; j < dataRow.ItemArray.Length; j++)
                    {
                        if (dataRow.ItemArray[j] != DBNull.Value && dataRow.ItemArray[j] != null)
                        {
                            try
                            {
                                row.CreateCell(j).SetCellValue(Convert.ToDouble(dataRow.ItemArray[j]));
                            }
                            catch
                            {
                                row.CreateCell(j).SetCellValue(dataRow.ItemArray[j].ToString());
                            }
                        }
                        else
                        {
                            row.CreateCell(j).SetCellValue("");
                        }
                    }
                }

                // 自动调整列宽
                for (int i = 0; i < headers.Length; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 保存文件
                using (FileStream fs = new FileStream(filePath, FileMode.Create, FileAccess.Write))
                {
                    workbook.Write(fs);
                }

                MessageBox.Show($"成功导出 {mineralData.Rows.Count} 行数据到 {filePath}", "导出成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出Excel文件时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DataGridView_SelectionChanged(object sender, EventArgs e)
        {
            // 移除此事件处理程序，因为我们现在使用自定义的选择逻辑
        }

        private void DataGridView_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            // 当双击单元格时，维持配色
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                // 如果是数据单元格，显示详细信息
                try
                {
                    string columnName = dgvMineralData.Columns[e.ColumnIndex].HeaderText;
                    object cellValue = dgvMineralData.Rows[e.RowIndex].Cells[e.ColumnIndex].Value;
                    string cellText = cellValue?.ToString() ?? "";

                    // 显示单元格详细信息
                    MessageBox.Show($"列: {columnName}\n行: {e.RowIndex + 1}\n值: {cellText}", "单元格信息", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"显示单元格信息时出错: {ex.Message}");
                }
            }

            // 确保维持选中行的高亮显示
            UpdateHighlights();
        }

        private void DataGridView_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            // 确保单元格格式化时保持正确的背景色
            if (e.RowIndex >= 0)
            {
                if (selectedRows.Contains(e.RowIndex))
                {
                    // 选中行使用高亮颜色
                    e.CellStyle.BackColor = Color.LightGoldenrodYellow;
                    e.CellStyle.ForeColor = Color.Black; // 确保文本颜色与背景对比度高
                    e.CellStyle.SelectionBackColor = Color.Orange; // 选中时的背景色
                    e.CellStyle.SelectionForeColor = Color.Black; // 选中时的文本颜色
                }
                else if (e.RowIndex % 2 == 0)
                {
                    // 偶数行使用深色背景
                    e.CellStyle.BackColor = Color.FromArgb(50, 50, 50);
                    e.CellStyle.ForeColor = Color.White;
                }
                else
                {
                    // 奇数行使用稍浅色背景
                    e.CellStyle.BackColor = Color.FromArgb(55, 55, 55);
                    e.CellStyle.ForeColor = Color.White;
                }

                // 如果单元格值为0，则显示为空字符串
                if (e.Value != null && e.ColumnIndex >= 2 && e.ColumnIndex <= 5) // 矿物含量列
                {
                    if (double.TryParse(e.Value.ToString(), out double value) && value == 0)
                    {
                        e.Value = "";
                        e.FormattingApplied = true;
                    }
                }
            }
        }

        private void DataGridView_MouseClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                DataGridView.HitTestInfo hit = dgvMineralData.HitTest(e.X, e.Y);
                if (hit.RowIndex >= 0)
                {
                    if ((ModifierKeys & Keys.Control) != Keys.Control)
                    {
                        // 如果没有按住Ctrl，清除之前的选择
                        selectedRows.Clear();
                    }
                    selectedRows.Add(hit.RowIndex);
                    UpdateHighlights();
                }
            }
        }

        private void DataGridView_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            // 只处理列标题点击
            if (e.RowIndex >= 0 || e.ColumnIndex < 0)
                return;

            // 获取列标题
            string columnHeader = dgvMineralData.Columns[e.ColumnIndex].HeaderText;

            // 显示列信息
            var result = MessageBox.Show($"是否将列 '{columnHeader}' 设置为顶深列？", "列选择",
                MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                columnPositions["顶深"] = (0, e.ColumnIndex);
                MessageBox.Show($"已将列 '{columnHeader}' 设置为顶深列", "设置成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else if (result == DialogResult.No)
            {
                var result2 = MessageBox.Show($"是否将列 '{columnHeader}' 设置为底深列？", "列选择",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result2 == DialogResult.Yes)
                {
                    columnPositions["底深"] = (0, e.ColumnIndex);
                    MessageBox.Show($"已将列 '{columnHeader}' 设置为底深列", "设置成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    var result3 = MessageBox.Show($"是否将列 '{columnHeader}' 设置为脆性指数列？", "列选择",
                        MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result3 == DialogResult.Yes)
                    {
                        columnPositions["脆性"] = (0, e.ColumnIndex);
                        MessageBox.Show($"已将列 '{columnHeader}' 设置为脆性指数列", "设置成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
        }

        private void UpdateHighlights()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"\n=== 更新高亮显示 ===\n");
                System.Diagnostics.Debug.WriteLine($"选中行数: {selectedRows.Count}");

                // 清除所有行的高亮
                foreach (DataGridViewRow row in dgvMineralData.Rows)
                {
                    // 使用深色主题的背景色
                    if (row.Index % 2 == 0)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(50, 50, 50);
                    }
                    else
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(55, 55, 55);
                    }
                    row.DefaultCellStyle.ForeColor = Color.White;
                }

                // 高亮选中的行
                foreach (int rowIndex in selectedRows)
                {
                    if (rowIndex < dgvMineralData.Rows.Count)
                    {
                        dgvMineralData.Rows[rowIndex].DefaultCellStyle.BackColor = Color.LightGoldenrodYellow;
                        dgvMineralData.Rows[rowIndex].DefaultCellStyle.ForeColor = Color.Black;
                        dgvMineralData.Rows[rowIndex].DefaultCellStyle.SelectionBackColor = Color.Orange;
                        dgvMineralData.Rows[rowIndex].DefaultCellStyle.SelectionForeColor = Color.Black;
                        System.Diagnostics.Debug.WriteLine($"高亮行 {rowIndex}");
                    }
                }

                // 强制刷新表格
                dgvMineralData.Refresh();

                // 更新图表点的高亮
                if (chartBrittleness.Series.Count >= 2)
                {
                    var pointSeries = chartBrittleness.Series[1];
                    System.Diagnostics.Debug.WriteLine($"图表点数: {pointSeries.Points.Count}");

                    // 重置所有点的样式
                    for (int i = 0; i < pointSeries.Points.Count; i++)
                    {
                        pointSeries.Points[i].Color = Color.Cyan;
                        pointSeries.Points[i].MarkerSize = 8;
                    }

                    // 高亮选中的点
                    foreach (int rowIndex in selectedRows)
                    {
                        if (rowIndex < pointSeries.Points.Count)
                        {
                            pointSeries.Points[rowIndex].Color = Color.FromArgb(255, 165, 0); // 橙色
                            pointSeries.Points[rowIndex].MarkerSize = 12;
                            System.Diagnostics.Debug.WriteLine($"高亮图表点 {rowIndex}");
                        }
                    }

                    // 强制刷新图表
                    chartBrittleness.Invalidate();
                }

                System.Diagnostics.Debug.WriteLine("\n=== 高亮显示更新完成 ===\n");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"\n更新高亮显示时出错: {ex.Message}\n{ex.StackTrace}\n");
            }
        }

        private void BtnDeletePoint_Click(object sender, EventArgs e)
        {
            if (selectedRows.Count == 0)
            {
                MessageBox.Show("请先选择要删除的脆性点！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var result = MessageBox.Show(
                $"确定要删除选中的 {selectedRows.Count} 个脆性点吗？",
                "确认删除",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // 记录要删除的行
                deletedRows.AddRange(selectedRows);

                // 清除选择
                selectedRows.Clear();

                // 重新生成曲线
                RegenerateCurveWithoutDeletedPoints();
            }
        }

        private void RegenerateCurveWithoutDeletedPoints()
        {
            if (dgvMineralData.DataSource is DataTable dataTable)
            {
                // 创建新的DataTable
                DataTable newTable = dataTable.Clone();

                // 复制未删除的行
                for (int i = 0; i < dataTable.Rows.Count; i++)
                {
                    if (!deletedRows.Contains(i))
                    {
                        newTable.ImportRow(dataTable.Rows[i]);
                    }
                }

                // 更新数据源
                dgvMineralData.DataSource = newTable;

                // 重新生成曲线
                if (newTable.Rows.Count > 0)
                {
                    BtnGenerateCurve_Click(null, EventArgs.Empty);
                }
                else
                {
                    chartBrittleness.Series.Clear();
                }
            }
        }

        private void BtnReset_Click(object sender, EventArgs e)
        {
            ResetChartView();
        }



        private void BtnReadExcel_Click(object sender, EventArgs e)
        {
            if (mineralData == null)
            {
                MessageBox.Show("请先选择文件。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            AutoDetectColumns();
        }

        private void AutoDetectColumns()
        {
            try
            {
                if (mineralData == null)
                {
                    MessageBox.Show("数据为空，请先导入数据。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 开始自动检测列
                DetectColumnsPosition(mineralData);

                var cleanData = ProcessRawData(mineralData);

                // 设置 DataGridView 的数据源
                dgvMineralData.DataSource = cleanData;

                // 输出调试信息
                System.Diagnostics.Debug.WriteLine($"AutoDetectColumns: 处理后的数据表");
                System.Diagnostics.Debug.WriteLine($"  行数: {cleanData.Rows.Count}");
                System.Diagnostics.Debug.WriteLine($"  列名: {string.Join(", ", cleanData.Columns.Cast<DataColumn>().Select(c => c.ColumnName))}");
                System.Diagnostics.Debug.WriteLine($"  DataGridView 列数: {dgvMineralData.Columns.Count}");
                foreach (DataGridViewColumn col in dgvMineralData.Columns)
                {
                    System.Diagnostics.Debug.WriteLine($"    列: {col.Name}, 标题: {col.HeaderText}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"自动检测失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ManualDetectButton_Click(object sender, EventArgs e)
        {
            if (mineralData == null)
            {
                MessageBox.Show("请先选择文件。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var manualDetectForm = new ManualDetectForm(mineralData);
            manualDetectForm.FormClosed += ManualDetectForm_FormClosed;
            manualDetectForm.ShowDialog();
        }

        private void ManualDetectForm_FormClosed(object sender, FormClosedEventArgs e)
        {
            var manualDetectForm = (ManualDetectForm)sender;
            if (manualDetectForm.SelectedColumns != null)
            {
                // 清除之前的列位置信息
                columnPositions.Clear();

                // 添加手动选择的列位置
                foreach (var kvp in manualDetectForm.SelectedColumns)
                {
                    columnPositions.Add(kvp.Key, (0, kvp.Value));
                }

                // 处理数据
                var cleanData = new DataTable();
                cleanData.Columns.Add("顶深/m", typeof(double));
                cleanData.Columns.Add("底深/m", typeof(double));
                cleanData.Columns.Add("脆性指数", typeof(double));

                int startRow = 1; // 假设从第二行开始是数据
                for (int i = startRow; i < mineralData.Rows.Count; i++)
                {
                    try
                    {
                        double top = ParseCell(mineralData.Rows[i][manualDetectForm.SelectedColumns["顶深"]]);
                        double bottom = ParseCell(mineralData.Rows[i][manualDetectForm.SelectedColumns["底深"]]);
                        double brittle = ParseCell(mineralData.Rows[i][manualDetectForm.SelectedColumns["脆性"]]);

                        if (IsValidData(top, bottom, brittle))
                        {
                            cleanData.Rows.Add(top, bottom, brittle);
                        }
                    }
                    catch
                    {
                        // 跳过无效行
                    }
                }

                dgvMineralData.DataSource = cleanData;
            }
        }

        private bool IsValidData(double top, double bottom, double brittle)
        {
            bool isValid = !double.IsNaN(top) &&
                          !double.IsNaN(bottom) &&
                          !double.IsNaN(brittle) &&
                          top < bottom;
            // 移除对脆性指数范围的限制
            // brittle >= 0 &&
            // brittle <= 100;

            if (!isValid)
            {
                string reason = "";
                if (double.IsNaN(top)) reason += "顶深无效 ";
                if (double.IsNaN(bottom)) reason += "底深无效 ";
                if (double.IsNaN(brittle)) reason += "脆性指数无效 ";
                if (top >= bottom) reason += "顶深不小于底深 ";
                // 移除对脆性指数范围的检查
                // if (brittle < 0 || brittle > 100) reason += "脆性指数超出范围 ";

                // 数据验证失败
            }

            return isValid;
        }

        private void DetectColumnsPosition(DataTable table)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("\n=== 开始检测列位置 ===\n");
                columnPositions.Clear();

                // 防御性检查，确保表格有效
                if (table == null || table.Columns.Count == 0 || table.Rows.Count == 0)
                {
                    throw new ArgumentException("输入数据表无效或为空");
                }

                // 记录表格基本信息
                System.Diagnostics.Debug.WriteLine($"表格列数: {table.Columns.Count}, 行数: {table.Rows.Count}");
                for (int col = 0; col < Math.Min(10, table.Columns.Count); col++)
                {
                    System.Diagnostics.Debug.WriteLine($"列 {col + 1}: {table.Columns[col].ColumnName}");
                }

                // 输出前几行数据
                System.Diagnostics.Debug.WriteLine("\n前几行数据:");
                for (int row = 0; row < Math.Min(5, table.Rows.Count); row++)
                {
                    string rowData = $"行 {row + 1}: ";
                    for (int col = 0; col < Math.Min(10, table.Columns.Count); col++)
                    {
                        rowData += $"[{table.Rows[row][col]}] ";
                    }
                    System.Diagnostics.Debug.WriteLine(rowData);
                }

                // 优先检测脆性列（包含公式）
                bool foundBrittleColumn = false;

                // 第一步：检查列名是否包含"脆性"
                for (int col = 0; col < table.Columns.Count; col++)
                {
                    string columnName = table.Columns[col].ColumnName.CleanString();

                    // 检查列名是否精确匹配"脆性指数"
                    if (columnName == "脆性指数".CleanString())
                    {
                        // 在该列中查找包含"脆性"关键词的单元格
                        for (int row = 0; row < Math.Min(10, table.Rows.Count); row++)
                        {
                            string cellText = table.Rows[row][col]?.ToString() ?? "";
                            if (cellText.CleanString().Contains("脆性".CleanString()))
                            {
                                columnPositions.Add("脆性", (row, col));
                                System.Diagnostics.Debug.WriteLine($"在行 {row + 1} 找到脆性关键词");
                                break;
                            }
                        }

                        if (!columnPositions.ContainsKey("脆性"))
                        {
                            columnPositions.Add("脆性", (0, col)); // 如果没有找到包含关键词的单元格，默认使用第一行
                        }
                        foundBrittleColumn = true;
                        System.Diagnostics.Debug.WriteLine($"通过列名检测到脆性列: {table.Columns[col].ColumnName}");
                        break;
                    }
                }

                // 第二步：如果没有找到精确匹配脆性指数的列，则检查包含脆性关键词的列
                if (!foundBrittleColumn)
                {
                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        string columnName = table.Columns[col].ColumnName.CleanString();

                        // 检查列名是否包含"脆性"但不是"TOC"
                        if (columnName.Contains("脆性".CleanString()) &&
                            !columnName.Contains("toc".CleanString()) &&
                            !columnName.Contains("总有机碳".CleanString()))
                        {
                            // 在该列中查找包含"脆性"关键词的单元格
                            for (int row = 0; row < Math.Min(10, table.Rows.Count); row++)
                            {
                                string cellText = table.Rows[row][col]?.ToString() ?? "";
                                if (cellText.CleanString().Contains("脆性".CleanString()))
                                {
                                    columnPositions.Add("脆性", (row, col));
                                    System.Diagnostics.Debug.WriteLine($"在行 {row + 1} 找到脆性关键词");
                                    break;
                                }
                            }

                            if (!columnPositions.ContainsKey("脆性"))
                            {
                                columnPositions.Add("脆性", (0, col));
                            }

                            foundBrittleColumn = true;
                            System.Diagnostics.Debug.WriteLine($"通过列名包含脆性检测到脆性列: {table.Columns[col].ColumnName}");
                            break;
                        }
                    }
                }

                // 第三步：如果还是没有找到脆性列，则检查公式
                if (!foundBrittleColumn)
                {
                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        for (int row = 0; row < Math.Min(10, table.Rows.Count); row++)
                        {
                            string cellValue = table.Rows[row][col]?.ToString() ?? "";
                            // 允许包含括号、混合运算符（如加减乘除）
                            if (Regex.IsMatch(cellValue, @"^([A-Z]+\d+[\+\-\*/]?)+$"))
                            {
                                columnPositions.Add("脆性", (row, col));
                                foundBrittleColumn = true;
                                System.Diagnostics.Debug.WriteLine($"通过公式检测到脆性列: {cellValue}");
                                break;
                            }
                        }
                        if (foundBrittleColumn) break;
                    }
                }

                // 第四步：如果还是没有找到脆性列，尝试直接查找数字列
                if (!foundBrittleColumn)
                {
                    // 首先检查是否有列包含0-100之间的数字
                    List<(int col, int numericRows)> candidateColumns = new List<(int, int)>();

                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        // 跳过TOC列
                        string columnName = table.Columns[col].ColumnName.CleanString();
                        if (columnName.Contains("toc".CleanString()) ||
                            columnName.Contains("总有机碳".CleanString()))
                        {
                            continue;
                        }

                        // 检查前更多行数据是否包含0-100之间的数字
                        int numericRows = 0;
                        int maxRowsToCheck = Math.Min(100, table.Rows.Count); // 扩大检测范围
                        double validRatio = table.Rows.Count > 100 ? 0.2 : 0.3; // 根据数据量调整比例

                        for (int row = 0; row < maxRowsToCheck; row++)
                        {
                            string cellValue = table.Rows[row][col]?.ToString() ?? "";
                            if (double.TryParse(cellValue, out double value) && value >= 0 && value <= 100)
                            {
                                numericRows++;
                            }
                        }

                        // 如果有足够比例的行包含0-100之间的数字，则认为是脆性指数列
                        if (numericRows > maxRowsToCheck * validRatio)
                        {
                            candidateColumns.Add((col, numericRows));
                            System.Diagnostics.Debug.WriteLine($"列 {col + 1} 包含 {numericRows} 行数字数据，可能是脆性指数列");
                        }
                    }

                    // 按照包含数字行数排序，选择数字行最多的列
                    candidateColumns = candidateColumns.OrderByDescending(c => c.numericRows).ToList();

                    // 选择数字行最多的列作为脆性指数列
                    if (candidateColumns.Count > 0)
                    {
                        int col = candidateColumns[0].col;

                        // 在该列中查找包含"脆性"关键词的单元格
                        for (int searchRow = 0; searchRow < Math.Min(10, table.Rows.Count); searchRow++)
                        {
                            string cellText = table.Rows[searchRow][col]?.ToString() ?? "";
                            if (cellText.CleanString().Contains("脆性".CleanString()))
                            {
                                columnPositions.Add("脆性", (searchRow, col));
                                System.Diagnostics.Debug.WriteLine($"在行 {searchRow + 1} 找到脆性关键词");
                                break;
                            }
                        }

                        if (!columnPositions.ContainsKey("脆性"))
                        {
                            columnPositions.Add("脆性", (0, col)); // 如果没有找到包含关键词的单元格，默认使用第一行
                        }
                        foundBrittleColumn = true;
                        System.Diagnostics.Debug.WriteLine($"通过数字检测到脆性列: {table.Columns[col].ColumnName}");
                    }
                }

                // 检测其他列（顶深和底深）
                for (int row = 0; row < Math.Min(config.MaxHeaderSearchRows, table.Rows.Count); row++)
                {
                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        string cellValue = table.Rows[row][col].ToString().CleanString();

                        foreach (var keyword in config.RequiredColumns)
                        {
                            if (keyword == "脆性" && foundBrittleColumn) continue; // 如果已经找到脆性列，跳过

                            if (cellValue.Contains(keyword.CleanString()))
                            {
                                if (!columnPositions.ContainsKey(keyword))
                                {
                                    columnPositions.Add(keyword, (row, col));
                                    System.Diagnostics.Debug.WriteLine($"检测到{keyword}列: 行={row + 1}, 列={col + 1}");
                                }
                            }
                        }
                    }
                }

                // 如果没有找到顶深和底深列，尝试根据数据类型推断
                if (!columnPositions.ContainsKey("顶深") && !columnPositions.ContainsKey("底深"))
                {
                    System.Diagnostics.Debug.WriteLine("尝试根据数据类型推断顶深和底深列...");

                    // 查找前两个包含递增数字的列作为顶深和底深
                    List<int> depthColumns = new List<int>();

                    for (int col = 0; col < table.Columns.Count; col++)
                    {
                        // 跳过已识别的脆性列
                        if (foundBrittleColumn && columnPositions.ContainsKey("脆性") && columnPositions["脆性"].col == col)
                            continue;

                        // 检查是否是递增的数字列
                        bool isDepthColumn = true;
                        double lastValue = double.MinValue;
                        int numericRows = 0;

                        for (int row = 1; row < Math.Min(20, table.Rows.Count); row++)
                        {
                            string cellValue = table.Rows[row][col]?.ToString() ?? "";
                            if (double.TryParse(cellValue, out double value))
                            {
                                numericRows++;
                                if (value < lastValue && lastValue != double.MinValue)
                                {
                                    // 如果数值下降，可能不是深度列
                                    isDepthColumn = false;
                                    break;
                                }
                                lastValue = value;
                            }
                        }

                        if (isDepthColumn && numericRows > 5)
                        {
                            depthColumns.Add(col);
                            System.Diagnostics.Debug.WriteLine($"列 {col + 1} 可能是深度列，包含 {numericRows} 行递增数字");

                            if (depthColumns.Count >= 2)
                                break; // 找到两个可能的深度列就停止
                        }
                    }

                    // 如果找到可能的深度列，将第一个设为顶深，第二个设为底深
                    if (depthColumns.Count >= 2)
                    {
                        columnPositions.Add("顶深", (0, depthColumns[0]));
                        columnPositions.Add("底深", (0, depthColumns[1]));
                        System.Diagnostics.Debug.WriteLine($"根据数据类型推断顶深列为列 {depthColumns[0] + 1}，底深列为列 {depthColumns[1] + 1}");
                    }
                    else if (depthColumns.Count == 1)
                    {
                        // 如果只找到一个深度列，尝试找到下一个数字列作为底深
                        columnPositions.Add("顶深", (0, depthColumns[0]));

                        for (int col = 0; col < table.Columns.Count; col++)
                        {
                            if (col == depthColumns[0] || (foundBrittleColumn && columnPositions.ContainsKey("脆性") && columnPositions["脆性"].col == col))
                                continue;

                            int numericRows = 0;
                            for (int row = 1; row < Math.Min(20, table.Rows.Count); row++)
                            {
                                string cellValue = table.Rows[row][col]?.ToString() ?? "";
                                if (double.TryParse(cellValue, out _))
                                    numericRows++;
                            }

                            if (numericRows > 5)
                            {
                                columnPositions.Add("底深", (0, col));
                                System.Diagnostics.Debug.WriteLine($"根据数据类型推断底深列为列 {col + 1}");
                                break;
                            }
                        }
                    }
                }

                // 输出检测结果
                System.Diagnostics.Debug.WriteLine("\n=== 列位置检测结果 ===\n");
                foreach (var pos in columnPositions)
                {
                    System.Diagnostics.Debug.WriteLine($"列名: {pos.Key}, 行: {pos.Value.row + 1}, 列: {pos.Value.col + 1}");
                }

                // 处理顶深度列，将其映射为顶深
                if (columnPositions.ContainsKey("顶深度"))
                {
                    if (!columnPositions.ContainsKey("顶深"))
                    {
                        columnPositions.Add("顶深", columnPositions["顶深度"]);
                    }
                    columnPositions.Remove("顶深度");
                    System.Diagnostics.Debug.WriteLine($"将顶深度列映射为顶深并删除顶深度");
                }

                // 处理底深度列，将其映射为底深
                if (columnPositions.ContainsKey("底深度"))
                {
                    if (!columnPositions.ContainsKey("底深"))
                    {
                        columnPositions.Add("底深", columnPositions["底深度"]);
                    }
                    columnPositions.Remove("底深度");
                    System.Diagnostics.Debug.WriteLine($"将底深度列映射为底深并删除底深度");
                }

                var missing = config.RequiredColumns.Where(c => !columnPositions.ContainsKey(c)).ToList();
                if (missing.Any())
                {
                    string errorMsg = $"缺少必要列头：{string.Join(",", missing)}";
                    System.Diagnostics.Debug.WriteLine($"\n错误: {errorMsg}\n");
                    throw new Exception(errorMsg);
                }

                System.Diagnostics.Debug.WriteLine("\n=== 列位置检测完成 ===\n");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"\n列位置检测异常: {ex.Message}\n{ex.StackTrace}\n");
                throw; // 重新抛出异常以便上层处理
            }
        }

        private DataTable ProcessRawData(DataTable rawData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("\n=== 开始处理原始数据 ===\n");
                var cleanData = new DataTable();
                cleanData.Columns.Add("顶深/m", typeof(double));
                cleanData.Columns.Add("底深/m", typeof(double));
                cleanData.Columns.Add("脆性指数", typeof(double));

                int startRow = columnPositions.Values.Max(p => p.row) + 1;
                List<double> topDepths = new List<double>();
                int validRows = 0;
                int invalidRows = 0;
                StringBuilder errorLog = new StringBuilder();

                System.Diagnostics.Debug.WriteLine($"数据开始行: {startRow + 1}");
                System.Diagnostics.Debug.WriteLine($"列位置信息:");
                foreach (var pos in columnPositions)
                {
                    System.Diagnostics.Debug.WriteLine($"  {pos.Key}: 行={pos.Value.row + 1}, 列={pos.Value.col + 1}");
                }

                // 处理列名映射
                string topDepthKey = "顶深";
                string bottomDepthKey = "底深";
                string brittleKey = "脆性";

                // 确保必要的列存在
                if (!columnPositions.ContainsKey(topDepthKey))
                {
                    throw new Exception("未找到顶深列");
                }
                if (!columnPositions.ContainsKey(bottomDepthKey))
                {
                    throw new Exception("未找到底深列");
                }
                if (!columnPositions.ContainsKey(brittleKey))
                {
                    throw new Exception("未找到脆性指数列");
                }

                for (int i = startRow; i < rawData.Rows.Count; i++)
                {
                    try
                    {
                        var row = rawData.Rows[i];

                        // 检查是否为空行
                        bool isEmptyRow = true;
                        for (int j = 0; j < rawData.Columns.Count; j++)
                        {
                            if (!string.IsNullOrWhiteSpace(row[j]?.ToString()))
                            {
                                isEmptyRow = false;
                                break;
                            }
                        }
                        if (isEmptyRow) continue;

                        // 获取顶深值
                        double top = ParseCell(row[columnPositions[topDepthKey].col]);
                        System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 从{topDepthKey}列获取值 {top}");

                        // 获取底深值
                        double bottom = ParseCell(row[columnPositions[bottomDepthKey].col]);
                        System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 从{bottomDepthKey}列获取值 {bottom}");

                        // 获取脆性指数值
                        double brittle = ParseCell(row[columnPositions[brittleKey].col]);
                        System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 从{brittleKey}列获取值 {brittle}");

                        if (IsValidData(top, bottom, brittle))
                        {
                            cleanData.Rows.Add(top, bottom, brittle);
                            topDepths.Add(top);
                            validRows++;
                            System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 数据有效，已添加");
                        }
                        else
                        {
                            invalidRows++;
                            string reason = "";
                            if (double.IsNaN(top)) reason += "顶深无效 ";
                            if (double.IsNaN(bottom)) reason += "底深无效 ";
                            if (double.IsNaN(brittle)) reason += "脆性指数无效 ";
                            if (top >= bottom) reason += "顶深不小于底深 ";
                            // 移除对脆性指数范围的检查
                            // if (brittle < 0 || brittle > 100) reason += "脆性指数超出范围 ";

                            errorLog.AppendLine($"行 {i + 1}: 数据无效 - {reason}");
                            System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 数据无效 - {reason}");
                        }
                    }
                    catch (Exception ex)
                    {
                        // 处理行时出错
                        invalidRows++;
                        errorLog.AppendLine($"行 {i + 1}: 处理错误 - {ex.Message}");
                        System.Diagnostics.Debug.WriteLine($"行 {i + 1}: 处理错误 - {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"\n数据处理完成: 有效行数={validRows}, 无效行数={invalidRows}\n");

                if (invalidRows > 0 && errorLog.Length > 0)
                {
                    System.Diagnostics.Debug.WriteLine("\n错误日志:\n" + errorLog.ToString());
                }

                return cleanData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"\n处理原始数据异常: {ex.Message}\n{ex.StackTrace}\n");
                throw; // 重新抛出异常以便上层处理
            }
        }

        private void DataLoader_DoWork(object sender, DoWorkEventArgs e)
        {
            try
            {
                e.Result = ReadExcelSheets(e.Argument.ToString());
            }
            catch (Exception ex)
            {
                e.Result = ex;
            }
        }

        private void DataLoader_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => DataLoader_RunWorkerCompleted(sender, e)));
                return;
            }

            if (e.Error != null)
            {
                MessageBox.Show($"读取错误: {e.Error.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            if (e.Result is DataSet ds)
            {
                mineralData = ds.Tables[0]; // 假设取第一个工作表
                MessageBox.Show("数据加载成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else if (e.Result is Exception ex)
            {
                MessageBox.Show($"文件读取失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private DataSet ReadExcelSheets(string path)
        {
            var dataSet = new DataSet();

            using (var fs = new FileStream(path, FileMode.Open, FileAccess.Read))
            {
                var workbook = new XSSFWorkbook(fs);
                var evaluator = workbook.GetCreationHelper().CreateFormulaEvaluator();

                for (int i = 0; i < workbook.NumberOfSheets; i++)
                {
                    var sheet = workbook.GetSheetAt(i);
                    var dataTable = new DataTable(sheet.SheetName);

                    // 读取表头
                    var headerRow = sheet.GetRow(0);
                    if (headerRow != null)
                    {
                        for (int col = 0; col < headerRow.LastCellNum; col++)
                        {
                            var cell = headerRow.GetCell(col);
                            dataTable.Columns.Add(cell?.ToString() ?? $"Column{col + 1}");
                        }
                    }

                    // 读取数据
                    for (int row = 1; row <= sheet.LastRowNum; row++)
                    {
                        var dataRow = sheet.GetRow(row);
                        if (dataRow != null)
                        {
                            var newRow = dataTable.NewRow();
                            for (int col = 0; col < dataTable.Columns.Count; col++)
                            {
                                var cell = dataRow.GetCell(col);
                                if (cell != null)
                                {
                                    // 根据单元格类型处理
                                    switch (cell.CellType)
                                    {
                                        case CellType.Formula:
                                            try
                                            {
                                                // 尝试计算公式
                                                var evaluatedCell = evaluator.Evaluate(cell);
                                                if (evaluatedCell != null)
                                                {
                                                    switch (evaluatedCell.CellType)
                                                    {
                                                        case CellType.Numeric:
                                                            newRow[col] = evaluatedCell.NumberValue.ToString();
                                                            break;
                                                        case CellType.String:
                                                            newRow[col] = evaluatedCell.StringValue;
                                                            break;
                                                        case CellType.Boolean:
                                                            newRow[col] = evaluatedCell.BooleanValue.ToString();
                                                            break;
                                                        default:
                                                            // 如果无法计算，则保留原始公式
                                                            newRow[col] = cell.CellFormula;
                                                            break;
                                                    }
                                                }
                                                else
                                                {
                                                    // 如果无法计算，则保留原始公式
                                                    newRow[col] = cell.CellFormula;
                                                }
                                            }
                                            catch
                                            {
                                                // 如果计算出错，则保留原始公式
                                                newRow[col] = cell.CellFormula;
                                            }
                                            break;
                                        case CellType.Numeric:
                                            newRow[col] = cell.NumericCellValue.ToString();
                                            break;
                                        case CellType.String:
                                            newRow[col] = cell.StringCellValue;
                                            break;
                                        case CellType.Boolean:
                                            newRow[col] = cell.BooleanCellValue.ToString();
                                            break;
                                        default:
                                            newRow[col] = cell.ToString() ?? "";
                                            break;
                                    }
                                }
                                else
                                {
                                    newRow[col] = "";
                                }
                            }
                            dataTable.Rows.Add(newRow);
                        }
                    }

                    dataSet.Tables.Add(dataTable);
                }
            }

            return dataSet;
        }
    }
}
